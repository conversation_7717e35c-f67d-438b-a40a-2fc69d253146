// internal/bootstrap/rbac_bootstrap.go
package bootstrap

import (
	"time"

	"wnapi/internal/pkg/cache"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	rbacSvc "wnapi/modules/rbac/service"

	"gorm.io/gorm"
)

// BootstrapRBAC khởi tạo và kết nối toàn bộ thành phần của hệ thống phân quyền RBAC
func BootstrapRBAC(
	db *gorm.DB,
	appCache cache.Cache,
	appLogger logger.Logger,
	cfg config.Config,
) (*permission.MiddlewareFactory, error) {
	// 1. Chuẩn bị repositories (sử dụng interfaces, không phụ thuộc vào triển khai cụ thể)
	// Giả sử đã có UserRoleRepository và RolePermissionRepository

	// 2. Tạo PermissionCheckerService làm PermissionChecker
	// Đ<PERSON><PERSON> là adapter phù hợp với interface permission.PermissionChecker
	permissionChecker := rbacSvc.NewPermissionCheckerService(appLogger)

	// 3. Tạo CachedPermissionChecker
	permissionCacheTTL := cfg.GetDurationWithDefault("cache.permission.ttl", 5*time.Minute)
	cachedPermChecker := permission.NewCachedPermissionChecker(
		permissionChecker,
		appCache,
		permissionCacheTTL,
		appLogger,
	)

	// 4. Khởi tạo MiddlewareFactory
	middlewareFactory := permission.NewMiddlewareFactory(
		cachedPermChecker,
		appLogger,
	)

	// 5. Trả về MiddlewareFactory để sử dụng ở main.go hoặc core.App
	return middlewareFactory, nil
}
