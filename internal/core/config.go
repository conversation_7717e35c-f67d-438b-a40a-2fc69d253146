package core

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/joho/godotenv"
)

// Config quản lý cấu hình ứng dụng
type Config struct {
	loaded bool
}

// NewConfig tạo đối tượng Config từ biến môi trường
func NewConfig(envFile string) (*Config, error) {
	cfg := &Config{}

	// Nếu không có đường dẫn cụ thể, sử dụng .env trong thư mục hiện tại
	if envFile == "" {
		envFile = ".env"
	}

	// Kiểm tra file có tồn tại không
	if _, err := os.Stat(envFile); err == nil {
		// Đọc file .env
		if err := godotenv.Load(envFile); err != nil {
			return nil, fmt.Errorf("error loading .env file: %w", err)
		}
		cfg.loaded = true
	} else {
		// Nếu không tìm thấy file, vẫn tiếp tục nhưng ghi log
		fmt.Printf("Warning: .env file %s not found, using environment variables only\n", envFile)
		cfg.loaded = false
	}

	return cfg, nil
}

// Get lấy giá trị theo key từ biến môi trường
func (c *Config) Get(key string) interface{} {
	// Chuyển đổi key thành định dạng biến môi trường
	// Ví dụ: app.name -> APP_NAME
	envKey := strings.ToUpper(strings.ReplaceAll(key, ".", "_"))

	// Lấy giá trị từ biến môi trường
	return os.Getenv(envKey)
}

// GetString lấy giá trị string theo key
func (c *Config) GetString(key string) string {
	val := c.Get(key)
	if val == nil {
		return ""
	}
	return fmt.Sprintf("%v", val)
}

// GetInt lấy giá trị int theo key
func (c *Config) GetInt(key string) int {
	strVal := c.GetString(key)
	if strVal == "" {
		return 0
	}

	val, err := strconv.Atoi(strVal)
	if err != nil {
		return 0
	}

	return val
}

// GetBool lấy giá trị bool theo key
func (c *Config) GetBool(key string) bool {
	strVal := c.GetString(key)
	if strVal == "" {
		return false
	}

	// Các giá trị true
	strVal = strings.ToLower(strVal)
	return strVal == "true" || strVal == "1" || strVal == "yes" || strVal == "y"
}

// GetBoolWithDefault lấy giá trị bool theo key, nếu không có thì trả về giá trị mặc định
func (c *Config) GetBoolWithDefault(key string, defaultValue bool) bool {
	strVal := c.GetString(key)
	if strVal == "" {
		return defaultValue
	}

	// Các giá trị true
	strVal = strings.ToLower(strVal)
	return strVal == "true" || strVal == "1" || strVal == "yes" || strVal == "y"
}

// GetDuration lấy giá trị duration theo key
func (c *Config) GetDuration(key string) time.Duration {
	strVal := c.GetString(key)
	if strVal == "" {
		return 0
	}

	duration, err := time.ParseDuration(strVal)
	if err != nil {
		return 0
	}

	return duration
}

// GetStringSlice lấy danh sách chuỗi từ biến môi trường
func (c *Config) GetStringSlice(key string) []string {
	value := c.GetString(key)
	if value == "" {
		return []string{}
	}

	// Phân tách chuỗi bằng dấu phẩy
	parts := strings.Split(value, ",")
	for i := range parts {
		parts[i] = strings.TrimSpace(parts[i])
	}

	// Lọc phần tử rỗng
	var result []string
	for _, part := range parts {
		if part != "" {
			result = append(result, part)
		}
	}

	return result
}

// GetStringMap lấy map cấu hình từ tiền tố
func (c *Config) GetStringMap(prefix string) map[string]interface{} {
	result := make(map[string]interface{})

	// Duyệt qua tất cả biến môi trường có prefix
	for _, env := range os.Environ() {
		parts := strings.SplitN(env, "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := parts[0]
		value := parts[1]

		// Kiểm tra xem key có prefix không
		if strings.HasPrefix(key, prefix+"_") {
			// Loại bỏ prefix
			settingKey := strings.TrimPrefix(key, prefix+"_")
			// Chuyển key về lowercase
			settingKey = strings.ToLower(settingKey)
			// Thêm vào map kết quả
			result[settingKey] = value
		}
	}

	return result
}

// GetModuleSettings lấy cấu hình cho module từ biến môi trường
func (c *Config) GetModuleSettings(moduleName string) map[string]interface{} {
	result := make(map[string]interface{})
	prefix := strings.ToUpper(moduleName) + "_"

	// Duyệt qua tất cả biến môi trường có prefix của module
	for _, env := range os.Environ() {
		parts := strings.SplitN(env, "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := parts[0]
		value := parts[1]

		// Kiểm tra xem key có prefix của module không
		if strings.HasPrefix(key, prefix) {
			// Loại bỏ prefix
			settingKey := strings.TrimPrefix(key, prefix)
			// Chuyển key về lowercase
			settingKey = strings.ToLower(settingKey)
			// Thêm vào map kết quả
			result[settingKey] = value
		}
	}

	return result
}

// GetDurationWithDefault lấy giá trị duration theo key, nếu không có thì trả về giá trị mặc định
func (c *Config) GetDurationWithDefault(key string, defaultValue time.Duration) time.Duration {
	strVal := c.GetString(key)
	if strVal == "" {
		return defaultValue
	}

	duration, err := time.ParseDuration(strVal)
	if err != nil {
		return defaultValue
	}

	return duration
}

// GetIntWithDefault lấy giá trị int theo key, nếu không có thì trả về giá trị mặc định
func (c *Config) GetIntWithDefault(key string, defaultValue int) int {
	strVal := c.GetString(key)
	if strVal == "" {
		return defaultValue
	}

	val, err := strconv.Atoi(strVal)
	if err != nil {
		return defaultValue
	}

	return val
}

// GetStringWithDefault lấy giá trị string theo key, nếu không có thì trả về giá trị mặc định
func (c *Config) GetStringWithDefault(key string, defaultValue string) string {
	val := c.GetString(key)
	if val == "" {
		return defaultValue
	}
	return val
}
