// internal/core/app_bootstrap.go
package core

import (
	"context"
	"fmt"
	"sync"
	"wnapi/internal/pkg/cache"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/plugins"
)

// AppBootstrap là wrapper để khởi tạo ứng dụng với MiddlewareFactory
type AppBootstrap struct {
	Config            config.Config
	Logger            logger.Logger
	DBManager         *database.DBManager
	Cache             cache.Cache
	MiddlewareFactory *permission.MiddlewareFactory
	modules           []Module
	plugins           []plugins.Plugin
	modulesMu         sync.RWMutex
	pluginsMu         sync.RWMutex
	ctx               context.Context
}

// NewAppBootstrap tạo một AppBootstrap mới
func NewAppBootstrap(
	cfg config.Config,
	log logger.Logger,
	dbm *database.DBManager,
	appCache cache.Cache,
	mwFactory *permission.MiddlewareFactory,
) *AppBootstrap {
	return &AppBootstrap{
		Config:            cfg,
		Logger:            log,
		DBManager:         dbm,
		Cache:             appCache,
		MiddlewareFactory: mwFactory,
		modules:           []Module{},
		plugins:           []plugins.Plugin{},
		ctx:               context.Background(),
	}
}

// GetMiddlewareFactory trả về MiddlewareFactory để sử dụng
func (a *AppBootstrap) GetMiddlewareFactory() *permission.MiddlewareFactory {
	return a.MiddlewareFactory
}

// GetDBManager trả về database manager
func (a *AppBootstrap) GetDBManager() *database.DBManager {
	return a.DBManager
}

// GetLogger trả về logger
func (a *AppBootstrap) GetLogger() logger.Logger {
	return a.Logger
}

// GetCache trả về cache
func (a *AppBootstrap) GetCache() cache.Cache {
	return a.Cache
}

// GetConfig trả về config
func (a *AppBootstrap) GetConfig() config.Config {
	return a.Config
}

// AddModule thêm module vào danh sách
func (a *AppBootstrap) AddModule(module Module) {
	a.modulesMu.Lock()
	defer a.modulesMu.Unlock()
	a.modules = append(a.modules, module)
}

// GetModules trả về danh sách modules đã nạp
func (a *AppBootstrap) GetModules() []Module {
	a.modulesMu.RLock()
	defer a.modulesMu.RUnlock()
	return a.modules
}

// GetModule trả về module theo tên
func (a *AppBootstrap) GetModule(name string) (Module, bool) {
	a.modulesMu.RLock()
	defer a.modulesMu.RUnlock()
	for _, module := range a.modules {
		if module.Name() == name {
			return module, true
		}
	}
	return nil, false
}

// AddPlugin thêm plugin vào danh sách
func (a *AppBootstrap) AddPlugin(plugin plugins.Plugin) {
	a.pluginsMu.Lock()
	defer a.pluginsMu.Unlock()
	a.plugins = append(a.plugins, plugin)
}

// GetPlugins trả về danh sách plugins đã nạp
func (a *AppBootstrap) GetPlugins() []plugins.Plugin {
	a.pluginsMu.RLock()
	defer a.pluginsMu.RUnlock()
	return a.plugins
}

// GetPlugin trả về plugin theo tên
func (a *AppBootstrap) GetPlugin(name string) (plugins.Plugin, bool) {
	a.pluginsMu.RLock()
	defer a.pluginsMu.RUnlock()
	for _, plugin := range a.plugins {
		if plugin.Name() == name {
			return plugin, true
		}
	}
	return nil, false
}

// Initialize khởi tạo các modules và plugins
func (a *AppBootstrap) Initialize() error {
	a.Logger.Info("Initializing application")

	// Khởi tạo plugins nếu được cấu hình
	if err := a.initializePlugins(); err != nil {
		return err
	}

	// Nạp danh sách module được kích hoạt
	enabledModules := a.Config.GetStringSlice("MODULES_ENABLED")
	if len(enabledModules) == 0 {
		a.Logger.Warn("No modules enabled")
		return nil
	}

	// Khởi tạo các module
	for _, moduleName := range enabledModules {
		// Lấy module factory
		factory, ok := GlobalModuleRegistry.Get(moduleName)
		if !ok {
			a.Logger.Warn("Module factory not found", "module", moduleName)
			continue
		}

		// Lấy cấu hình module
		moduleConfig := make(map[string]interface{})
		if configGetter, ok := a.Config.(interface {
			GetModuleSettings(string) map[string]interface{}
		}); ok {
			moduleConfig = configGetter.GetModuleSettings(moduleName)
		}

		// Tạo module
		module, err := factory(a, moduleConfig)
		if err != nil {
			return fmt.Errorf("failed to create module %s: %w", moduleName, err)
		}

		// Khởi tạo module
		if err := module.Init(a.ctx); err != nil {
			return fmt.Errorf("failed to initialize module %s: %w", moduleName, err)
		}

		// Lưu module vào danh sách
		a.AddModule(module)

		a.Logger.Info("Module initialized", "module", moduleName)
	}

	return nil
}

// initializePlugins khởi tạo các plugin
func (a *AppBootstrap) initializePlugins() error {
	// Lấy cấu hình plugins từ biến môi trường
	enabledPlugins := a.Config.GetStringSlice("PLUGINS_ENABLED")
	if len(enabledPlugins) == 0 {
		a.Logger.Info("No plugins configured")
		return nil
	}

	a.Logger.Info("Plugins được cấu hình:", "count", len(enabledPlugins))

	// Khởi tạo từng plugin được cấu hình
	for _, pluginName := range enabledPlugins {
		a.Logger.Info("Đang xử lý plugin", "plugin", pluginName)

		// Lấy plugin config từ biến môi trường
		var pluginConfig map[string]interface{}
		if configGetter, ok := a.Config.(interface {
			GetStringMap(string) map[string]interface{}
		}); ok {
			pluginConfig = configGetter.GetStringMap("PLUGIN_" + pluginName)
		}

		// Tạo và khởi tạo plugin
		plugin, err := plugins.GlobalRegistry.CreateAndInitialize(a.ctx, pluginName, pluginConfig)
		if err != nil {
			a.Logger.Error("Failed to initialize plugin", "plugin", pluginName, "error", err.Error())
			continue
		}

		// Thêm vào danh sách plugins đã khởi tạo
		a.AddPlugin(plugin)

		a.Logger.Info("Plugin initialized", "plugin", pluginName)
	}

	return nil
}

// Shutdown dừng ứng dụng và giải phóng tài nguyên
func (a *AppBootstrap) Shutdown(ctx context.Context) error {
	a.Logger.Info("Shutting down application bootstrap")

	// Dừng các module
	for _, module := range a.GetModules() {
		if err := module.Cleanup(ctx); err != nil {
			a.Logger.Error("Error cleaning up module", "module", module.Name(), "error", err.Error())
		}
	}

	// Dừng các plugin
	for _, plugin := range a.GetPlugins() {
		if err := plugin.Shutdown(ctx); err != nil {
			a.Logger.Error("Error shutting down plugin", "plugin", plugin.Name(), "error", err.Error())
		}
	}

	return nil
}
