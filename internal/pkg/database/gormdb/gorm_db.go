// internal/pkg/database/gormdb/gorm_db.go
package gormdb

import (
	"database/sql"
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"

	"github.com/jmoiron/sqlx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// GORMDBManager là triển khai của DBManager sử dụng GORM
type GORMDBManager struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewGORMDBManager tạo một instance mới của GORMDBManager
func NewGORMDBManager(
	dsnWrite string,
	dsnRead string,
	logger logger.Logger,
) (*database.DBManager, error) {
	// Stub implementation - chỉ để demo/test
	logger.Info("Creating mock DB Manager for testing")

	// Tạo mock db
	mockDB := &sql.DB{}
	mockSqlx := sqlx.NewDb(mockDB, "mysql")

	// Tạo mock gorm.DB
	mockGorm, _ := gorm.Open(mysql.New(mysql.Config{
		SkipInitializeWithVersion: true, // Để tránh lỗi khi kiểm tra phiên bản MySQL
	}), &gorm.Config{
		DisableAutomaticPing: true, // Tắt ping tự động
	})

	// Tạo DBManager với các mock
	dbManager := database.NewDBManager(mockGorm, mockGorm, logger)
	dbManager.SetSqlxDB(mockSqlx)

	return dbManager, nil
}
