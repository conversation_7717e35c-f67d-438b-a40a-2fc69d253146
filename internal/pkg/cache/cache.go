// internal/pkg/cache/cache.go
package cache

import (
	"context"
	"errors"
	"time"
)

// ErrCacheMiss được trả về khi key không tồn tại trong cache
var ErrCacheMiss = errors.New("cache miss")

// Cache định nghĩa interface cho cache với context support
type Cache interface {
	// Get lấy giá trị từ cache
	Get(ctx context.Context, key string) (interface{}, error)

	// Set đặt giá trị vào cache
	Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error

	// Delete xóa giá trị khỏi cache
	Delete(ctx context.Context, key string) error

	// DeletePattern xóa tất cả key khớp với pattern
	DeletePattern(ctx context.Context, pattern string) error

	// IsErrCacheMiss kiểm tra xem lỗi có phải là cache miss không
	IsErrCacheMiss(err error) bool
}

// LegacyCache định nghĩa interface cũ cho backward compatibility
type LegacyCache interface {
	// Get lấy giá trị từ cache
	Get(key string) (interface{}, bool)

	// Set đặt giá trị vào cache
	Set(key string, value interface{}, ttl time.Duration)

	// Delete xóa giá trị khỏi cache
	Delete(key string)

	// Clear xóa tất cả giá trị trong cache
	Clear()
}
