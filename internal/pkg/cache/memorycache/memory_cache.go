// internal/pkg/cache/memorycache/memory_cache.go
package memorycache

import (
	"context"
	"errors"
	"strings"
	"sync"
	"time"

	"wnapi/internal/pkg/cache"
)

type cacheItem struct {
	value      interface{}
	expiration time.Time
}

// MemoryCache là một triển khai cache đơn giản sử dụng memory
type MemoryCache struct {
	items map[string]cacheItem
	mu    sync.RWMutex
}

// NewMemoryCache tạo một instance mới của MemoryCache
func NewMemoryCache() cache.Cache {
	return &MemoryCache{
		items: make(map[string]cacheItem),
	}
}

// Get lấy giá trị từ cache
func (c *MemoryCache) Get(ctx context.Context, key string) (interface{}, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	item, found := c.items[key]
	if !found {
		return nil, cache.ErrCacheMiss
	}

	if item.expiration.Before(time.Now()) {
		delete(c.items, key)
		return nil, cache.ErrCacheMiss
	}

	return item.value, nil
}

// Set đặt giá trị vào cache
func (c *MemoryCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.items[key] = cacheItem{
		value:      value,
		expiration: time.Now().Add(ttl),
	}

	return nil
}

// Delete xóa giá trị khỏi cache
func (c *MemoryCache) Delete(ctx context.Context, key string) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	delete(c.items, key)
	return nil
}

// DeletePattern xóa tất cả key khớp với pattern
func (c *MemoryCache) DeletePattern(ctx context.Context, pattern string) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	// Giả sử pattern chỉ có dạng "prefix:*"
	if pattern == "*" {
		c.items = make(map[string]cacheItem)
		return nil
	}

	prefix := ""
	if len(pattern) > 0 && pattern[len(pattern)-1] == '*' {
		prefix = pattern[:len(pattern)-1]
	} else {
		// Nếu không có wildcard, xử lý như key thông thường
		delete(c.items, pattern)
		return nil
	}

	// Xóa tất cả key bắt đầu bằng prefix
	for k := range c.items {
		if strings.HasPrefix(k, prefix) {
			delete(c.items, k)
		}
	}

	return nil
}

// IsErrCacheMiss kiểm tra xem lỗi có phải là cache miss không
func (c *MemoryCache) IsErrCacheMiss(err error) bool {
	return errors.Is(err, cache.ErrCacheMiss)
}
