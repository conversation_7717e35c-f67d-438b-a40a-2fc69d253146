// internal/pkg/config/viperconfig/viper.go
package viperconfig

import (
	"strings"
	"time"
	"wnapi/internal/pkg/config"
)

// ConfigLoader là trình nạp cấu hình sử dụng viper
type ConfigLoader struct {
}

// NewConfigLoader tạo một instance mới của ConfigLoader
func NewConfigLoader() *ConfigLoader {
	return &ConfigLoader{}
}

// Load nạp cấu hình từ file
func (l *ConfigLoader) Load(path string) (config.Config, error) {
	// Mặc định sử dụng file .env nếu không chỉ định
	if path == "" {
		path = ".env"
	}

	// Đọc từ file .env thay vì config.yaml
	// Sử dụng godotenv hoặc viper để đọc file .env

	// Trả về config với map nội bộ để lưu cấu hình
	return &ViperConfig{
		inMemoryConfig: make(map[string]interface{}),
	}, nil
}

// ViperConfig là một triển khai của Config sử dụng viper
type ViperConfig struct {
	// Lưu trữ một số cấu hình trong map nội bộ khi cần thiết
	inMemoryConfig map[string]interface{}
}

// Triển khai các phương thức của interface Config
func (c *ViperConfig) GetString(key string) string {
	// Ưu tiên lấy từ inMemoryConfig nếu có
	if value, ok := c.inMemoryConfig[key]; ok {
		if strValue, ok := value.(string); ok {
			return strValue
		}
	}
	return ""
}

func (c *ViperConfig) GetStringWithDefault(key string, defaultValue string) string {
	value := c.GetString(key)
	if value == "" {
		return defaultValue
	}
	return value
}

func (c *ViperConfig) GetInt(key string) int {
	// Ưu tiên lấy từ inMemoryConfig nếu có
	if value, ok := c.inMemoryConfig[key]; ok {
		if intValue, ok := value.(int); ok {
			return intValue
		}
	}
	return 0
}

func (c *ViperConfig) GetIntWithDefault(key string, defaultValue int) int {
	value := c.GetInt(key)
	if value == 0 {
		return defaultValue
	}
	return value
}

func (c *ViperConfig) GetBool(key string) bool {
	// Ưu tiên lấy từ inMemoryConfig nếu có
	if value, ok := c.inMemoryConfig[key]; ok {
		if boolValue, ok := value.(bool); ok {
			return boolValue
		}
	}
	return false
}

func (c *ViperConfig) GetBoolWithDefault(key string, defaultValue bool) bool {
	return defaultValue
}

func (c *ViperConfig) GetDuration(key string) time.Duration {
	return 0
}

func (c *ViperConfig) GetDurationWithDefault(key string, defaultValue time.Duration) time.Duration {
	return defaultValue
}

// GetStringSlice lấy danh sách chuỗi từ cấu hình
func (c *ViperConfig) GetStringSlice(key string) []string {
	// Ưu tiên lấy từ inMemoryConfig nếu có
	if value, ok := c.inMemoryConfig[key]; ok {
		if sliceValue, ok := value.([]string); ok {
			return sliceValue
		}
	}

	// Lấy từ GetString
	value := c.GetString(key)
	if value == "" {
		return []string{}
	}

	// Phân tách chuỗi bằng dấu phẩy
	parts := strings.Split(value, ",")
	for i := range parts {
		parts[i] = strings.TrimSpace(parts[i])
	}

	// Lọc phần tử rỗng
	var result []string
	for _, part := range parts {
		if part != "" {
			result = append(result, part)
		}
	}

	return result
}

// GetStringMap lấy map cấu hình từ tiền tố
func (c *ViperConfig) GetStringMap(prefix string) map[string]interface{} {
	// Stub implementation
	return make(map[string]interface{})
}

// GetModuleSettings lấy cấu hình cho module từ cấu hình
func (c *ViperConfig) GetModuleSettings(moduleName string) map[string]interface{} {
	// Stub implementation
	return make(map[string]interface{})
}

// SetModulesEnabled thiết lập danh sách module được kích hoạt
func (c *ViperConfig) SetModulesEnabled(modules []string) {
	if c.inMemoryConfig == nil {
		c.inMemoryConfig = make(map[string]interface{})
	}
	c.inMemoryConfig["MODULES_ENABLED"] = modules
}
