// internal/pkg/config/config.go
package config

import (
	"time"
)

// Config định nghĩa interface cho cấu hình ứng dụng
type Config interface {
	// GetString lấy giá trị chuỗi từ cấu hình
	GetString(key string) string

	// GetStringWithDefault lấy giá trị chuỗi từ cấu hình, nếu không có thì trả về giá trị mặc định
	GetStringWithDefault(key string, defaultValue string) string

	// GetInt lấy giá trị số nguyên từ cấu hình
	GetInt(key string) int

	// GetIntWithDefault lấy giá trị số nguyên từ cấu hình, nếu không có thì trả về giá trị mặc định
	GetIntWithDefault(key string, defaultValue int) int

	// GetBool lấy giá trị boolean từ cấu hình
	<PERSON>ool(key string) bool

	// GetBoolWithDefault lấy giá trị boolean từ cấu hình, nếu không có thì trả về giá trị mặc định
	GetBoolWithDefault(key string, defaultValue bool) bool

	// GetDuration lấy giá trị duration từ cấu hình
	GetDuration(key string) time.Duration

	// GetDurationWithDefault lấy giá trị duration từ cấu hình, nếu không có thì trả về giá trị mặc định
	GetDurationWithDefault(key string, defaultValue time.Duration) time.Duration

	// GetStringSlice lấy danh sách chuỗi từ cấu hình
	GetStringSlice(key string) []string

	// GetStringMap lấy map cấu hình từ tiền tố
	GetStringMap(prefix string) map[string]interface{}

	// GetModuleSettings lấy cấu hình cho module từ cấu hình
	GetModuleSettings(moduleName string) map[string]interface{}
}
