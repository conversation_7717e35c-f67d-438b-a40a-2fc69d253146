package seed

import (
	"fmt"
	"sort"
	"sync"

	"wnapi/internal/pkg/errors"
)

// Registry quản lý việc đăng ký và discovery của module seeds
type Registry struct {
	moduleSeeds map[string]ModuleSeed
	mutex       sync.RWMutex
}

// GlobalRegistry là instance global của registry
var GlobalRegistry = NewRegistry()

// NewRegistry tạo một registry mới
func NewRegistry() *Registry {
	return &Registry{
		moduleSeeds: make(map[string]ModuleSeed),
	}
}

// RegisterModuleSeed đăng ký seed cho module
func (r *Registry) RegisterModuleSeed(name string, seed ModuleSeed) error {
	if name == "" {
		return errors.New(errors.ErrCodeValidationFailed, "tên module không được để trống", nil)
	}

	if seed == nil {
		return errors.New(errors.ErrCodeValidationFailed, "module seed không được để trống", nil)
	}

	r.mutex.Lock()
	defer r.mutex.Unlock()

	// Kiểm tra xem module đã được đăng ký chưa
	if _, exists := r.moduleSeeds[name]; exists {
		return errors.New(errors.ErrCodeConflict, 
			fmt.Sprintf("module seed '%s' đã được đăng ký", name), nil)
	}

	r.moduleSeeds[name] = seed
	return nil
}

// GetModuleSeed lấy seed của module
func (r *Registry) GetModuleSeed(name string) (ModuleSeed, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	seed, exists := r.moduleSeeds[name]
	if !exists {
		return nil, errors.New(errors.ErrCodeNotFound, 
			fmt.Sprintf("không tìm thấy module seed '%s'", name), nil)
	}

	return seed, nil
}

// GetAllModuleSeeds lấy tất cả module seeds
func (r *Registry) GetAllModuleSeeds() map[string]ModuleSeed {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	// Tạo copy để tránh race condition
	result := make(map[string]ModuleSeed)
	for name, seed := range r.moduleSeeds {
		result[name] = seed
	}

	return result
}

// ListAvailableSeeds list tất cả seeders available
func (r *Registry) ListAvailableSeeds() map[string][]string {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	result := make(map[string][]string)
	for moduleName, moduleSeeder := range r.moduleSeeds {
		var seederNames []string
		for _, seeder := range moduleSeeder.GetSeeders() {
			seederNames = append(seederNames, seeder.Name())
		}
		
		// Sort seeder names for consistent output
		sort.Strings(seederNames)
		result[moduleName] = seederNames
	}

	return result
}

// GetModuleNames trả về danh sách tên modules đã đăng ký
func (r *Registry) GetModuleNames() []string {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var names []string
	for name := range r.moduleSeeds {
		names = append(names, name)
	}

	// Sort for consistent output
	sort.Strings(names)
	return names
}

// IsModuleRegistered kiểm tra xem module có được đăng ký không
func (r *Registry) IsModuleRegistered(name string) bool {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	_, exists := r.moduleSeeds[name]
	return exists
}

// UnregisterModuleSeed hủy đăng ký module seed (chủ yếu cho testing)
func (r *Registry) UnregisterModuleSeed(name string) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if _, exists := r.moduleSeeds[name]; !exists {
		return errors.New(errors.ErrCodeNotFound, 
			fmt.Sprintf("module seed '%s' không tồn tại", name), nil)
	}

	delete(r.moduleSeeds, name)
	return nil
}

// Clear xóa tất cả module seeds (chủ yếu cho testing)
func (r *Registry) Clear() {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	r.moduleSeeds = make(map[string]ModuleSeed)
}

// GetSeederInfo trả về thông tin chi tiết về seeder
func (r *Registry) GetSeederInfo(moduleName, seederName string) (*SeederInfo, error) {
	moduleSeed, err := r.GetModuleSeed(moduleName)
	if err != nil {
		return nil, err
	}

	for _, seeder := range moduleSeed.GetSeeders() {
		if seeder.Name() == seederName {
			return &SeederInfo{
				ModuleName:   moduleName,
				SeederName:   seeder.Name(),
				Description:  seeder.Description(),
				Dependencies: seeder.Dependencies(),
			}, nil
		}
	}

	return nil, errors.New(errors.ErrCodeNotFound, 
		fmt.Sprintf("không tìm thấy seeder '%s' trong module '%s'", seederName, moduleName), nil)
}

// SeederInfo chứa thông tin về seeder
type SeederInfo struct {
	ModuleName   string   `json:"module_name"`
	SeederName   string   `json:"seeder_name"`
	Description  string   `json:"description"`
	Dependencies []string `json:"dependencies"`
}

// GetAllSeederInfo trả về thông tin tất cả seeders
func (r *Registry) GetAllSeederInfo() []SeederInfo {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var infos []SeederInfo
	for moduleName, moduleSeed := range r.moduleSeeds {
		for _, seeder := range moduleSeed.GetSeeders() {
			infos = append(infos, SeederInfo{
				ModuleName:   moduleName,
				SeederName:   seeder.Name(),
				Description:  seeder.Description(),
				Dependencies: seeder.Dependencies(),
			})
		}
	}

	// Sort by module name then seeder name
	sort.Slice(infos, func(i, j int) bool {
		if infos[i].ModuleName == infos[j].ModuleName {
			return infos[i].SeederName < infos[j].SeederName
		}
		return infos[i].ModuleName < infos[j].ModuleName
	})

	return infos
}

// ValidateDependencies kiểm tra dependencies giữa các seeders
func (r *Registry) ValidateDependencies() error {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	// Tạo map tất cả seeders
	allSeeders := make(map[string]bool)
	for _, moduleSeed := range r.moduleSeeds {
		for _, seeder := range moduleSeed.GetSeeders() {
			allSeeders[seeder.Name()] = true
		}
	}

	// Kiểm tra dependencies
	for _, moduleSeed := range r.moduleSeeds {
		for _, seeder := range moduleSeed.GetSeeders() {
			for _, dep := range seeder.Dependencies() {
				if !allSeeders[dep] {
					return errors.New(errors.ErrCodeValidationFailed, 
						fmt.Sprintf("seeder '%s' phụ thuộc vào '%s' nhưng không tìm thấy", 
							seeder.Name(), dep), nil)
				}
			}
		}
	}

	return nil
}

// Global convenience functions

// RegisterModuleSeed đăng ký module seed với global registry
func RegisterModuleSeed(name string, seed ModuleSeed) error {
	return GlobalRegistry.RegisterModuleSeed(name, seed)
}

// GetModuleSeed lấy module seed từ global registry
func GetModuleSeed(name string) (ModuleSeed, error) {
	return GlobalRegistry.GetModuleSeed(name)
}

// GetAllModuleSeeds lấy tất cả module seeds từ global registry
func GetAllModuleSeeds() map[string]ModuleSeed {
	return GlobalRegistry.GetAllModuleSeeds()
}

// ListAvailableSeeds list tất cả seeders từ global registry
func ListAvailableSeeds() map[string][]string {
	return GlobalRegistry.ListAvailableSeeds()
}

// GetModuleNames lấy danh sách tên modules từ global registry
func GetModuleNames() []string {
	return GlobalRegistry.GetModuleNames()
}

// IsModuleRegistered kiểm tra module registration từ global registry
func IsModuleRegistered(name string) bool {
	return GlobalRegistry.IsModuleRegistered(name)
}

// GetSeederInfo lấy thông tin seeder từ global registry
func GetSeederInfo(moduleName, seederName string) (*SeederInfo, error) {
	return GlobalRegistry.GetSeederInfo(moduleName, seederName)
}

// GetAllSeederInfo lấy thông tin tất cả seeders từ global registry
func GetAllSeederInfo() []SeederInfo {
	return GlobalRegistry.GetAllSeederInfo()
}

// ValidateDependencies kiểm tra dependencies từ global registry
func ValidateDependencies() error {
	return GlobalRegistry.ValidateDependencies()
}
