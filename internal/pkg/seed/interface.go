// Package seed provides core interfaces and utilities for database seeding system
package seed

import "context"

// Seeder interface định nghĩa contract cho mỗi seeder
type Seeder interface {
	// Name trả về tên của seeder
	Name() string

	// Dependencies tr<PERSON> về danh sách seeder phụ thuộc (phải chạy trướ<PERSON>)
	Dependencies() []string

	// Run thực thi seed data
	Run(ctx context.Context) error

	// Rollback xóa seed data (optional)
	Rollback(ctx context.Context) error

	// Description mô tả chức năng của seeder
	Description() string
}

// ModuleSeed interface cho mỗi module
type ModuleSeed interface {
	// ModuleName trả về tên module
	ModuleName() string

	// GetSeeders trả về danh sách seeders của module
	GetSeeders() []Seeder

	// SeedAll chạy tất cả seeders trong module
	SeedAll(ctx context.Context) error

	// SeedSpecific chạy seeder cụ thể
	SeedSpecific(ctx context.Context, seederName string) error
}

// SeedRunner interface cho việc thực thi seed operations
type SeedRunner interface {
	// SeedAllModules chạy tất cả module seeds
	SeedAllModules(ctx context.Context) error

	// SeedModule chạy seed cho module cụ thể
	SeedModule(ctx context.Context, moduleName string) error

	// SeedSpecific chạy seeder cụ thể trong module
	SeedSpecific(ctx context.Context, moduleName, seederName string) error

	// ListAvailableSeeds trả về danh sách tất cả seeds available
	ListAvailableSeeds() map[string][]string

	// RollbackModule rollback tất cả seeders trong module
	RollbackModule(ctx context.Context, moduleName string) error

	// RollbackSpecific rollback seeder cụ thể
	RollbackSpecific(ctx context.Context, moduleName, seederName string) error
}

// SeederFactory interface cho việc tạo seeders với dependencies
type SeederFactory interface {
	// CreateSeeder tạo seeder với dependencies được inject
	CreateSeeder(seederType string, deps interface{}) (Seeder, error)
}

// SeedValidator interface cho việc validate seed data
type SeedValidator interface {
	// ValidateData validate cấu trúc và nội dung của seed data
	ValidateData(data interface{}) error

	// ValidateDependencies validate dependencies giữa các seeders
	ValidateDependencies(seeders []Seeder) error
}

// SeedProgress interface cho việc track progress của seeding operations
type SeedProgress interface {
	// Start bắt đầu tracking progress
	Start(totalSteps int)

	// Step báo hiệu hoàn thành một step
	Step(stepName string)

	// Finish kết thúc tracking progress
	Finish()

	// Error báo hiệu có lỗi xảy ra
	Error(err error)
}

// SeedContext interface cho việc quản lý context trong seeding operations
type SeedContext interface {
	// GetEnvironment trả về environment hiện tại (dev, staging, prod)
	GetEnvironment() string

	// GetTenantID trả về tenant ID cho multi-tenant operations
	GetTenantID() uint

	// GetUserID trả về user ID cho audit purposes
	GetUserID() *uint

	// IsDryRun kiểm tra xem có phải dry run mode không
	IsDryRun() bool

	// GetBatchSize trả về batch size cho bulk operations
	GetBatchSize() int

	// ShouldSkipExisting kiểm tra xem có skip existing data không
	ShouldSkipExisting() bool
}
