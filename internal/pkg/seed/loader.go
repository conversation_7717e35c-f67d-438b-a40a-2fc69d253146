package seed

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"text/template"

	"wnapi/internal/pkg/errors"
)

// DataLoader interface cho việc load seed data
type DataLoader interface {
	LoadSeedData(dataPath, environment, fileName string) (*SeedData, error)
	LoadDataWithFallback(dataPath, environment, fileName string) (*SeedData, error)
	ValidateSeedData(data *SeedData) error
	ParseTemplateData(data []byte, vars map[string]interface{}) ([]byte, error)
}

// FileDataLoader implements DataLoader interface
type FileDataLoader struct {
	config *Config
}

// NewFileDataLoader tạo một FileDataLoader mới
func NewFileDataLoader(config *Config) *FileDataLoader {
	return &FileDataLoader{
		config: config,
	}
}

// LoadSeedData load data từ file theo environment
func (l *FileDataLoader) LoadSeedData(dataPath, environment, fileName string) (*SeedData, error) {
	// Thử load file environment-specific trước
	envFileName := l.getEnvironmentFileName(fileName, environment)
	envFilePath := filepath.Join(dataPath, envFileName)
	
	if data, err := l.loadFromFile(envFilePath); err == nil {
		return data, nil
	}

	// Fallback to default file
	defaultFilePath := filepath.Join(dataPath, fileName)
	return l.loadFromFile(defaultFilePath)
}

// LoadDataWithFallback load data với fallback to default environment
func (l *FileDataLoader) LoadDataWithFallback(dataPath, environment, fileName string) (*SeedData, error) {
	environments := l.config.GetEnvironmentPriority()
	
	var lastErr error
	for _, env := range environments {
		if data, err := l.LoadSeedData(dataPath, env, fileName); err == nil {
			return data, nil
		} else {
			lastErr = err
		}
	}

	return nil, fmt.Errorf("không thể load seed data từ file %s cho bất kỳ environment nào: %w", fileName, lastErr)
}

// ValidateSeedData validate format và required fields
func (l *FileDataLoader) ValidateSeedData(data *SeedData) error {
	if data == nil {
		return errors.New(errors.ErrCodeValidationFailed, "seed data không được để trống", nil)
	}

	if data.Data == nil {
		return errors.New(errors.ErrCodeValidationFailed, "data field không được để trống", nil)
	}

	// Validate metadata
	if err := l.validateMetadata(&data.Metadata); err != nil {
		return err
	}

	// Validate environment
	if data.Environment == "" {
		data.Environment = l.config.Environment
	}

	return nil
}

// ParseTemplateData support template trong data files
func (l *FileDataLoader) ParseTemplateData(data []byte, vars map[string]interface{}) ([]byte, error) {
	tmpl, err := template.New("seed").Parse(string(data))
	if err != nil {
		return nil, fmt.Errorf("lỗi parse template: %w", err)
	}

	var result strings.Builder
	if err := tmpl.Execute(&result, vars); err != nil {
		return nil, fmt.Errorf("lỗi execute template: %w", err)
	}

	return []byte(result.String()), nil
}

// loadFromFile load data từ file cụ thể
func (l *FileDataLoader) loadFromFile(filePath string) (*SeedData, error) {
	// Kiểm tra file có tồn tại không
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("file không tồn tại: %s", filePath)
	}

	// Đọc file
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("lỗi đọc file %s: %w", filePath, err)
	}

	// Parse JSON
	var seedData SeedData
	if err := json.Unmarshal(data, &seedData); err != nil {
		return nil, fmt.Errorf("lỗi parse JSON từ file %s: %w", filePath, err)
	}

	// Validate data
	if err := l.ValidateSeedData(&seedData); err != nil {
		return nil, fmt.Errorf("lỗi validate seed data từ file %s: %w", filePath, err)
	}

	return &seedData, nil
}

// getEnvironmentFileName tạo tên file theo environment
func (l *FileDataLoader) getEnvironmentFileName(fileName, environment string) string {
	ext := filepath.Ext(fileName)
	name := strings.TrimSuffix(fileName, ext)
	return fmt.Sprintf("%s.%s%s", name, environment, ext)
}

// validateMetadata validate metadata structure
func (l *FileDataLoader) validateMetadata(metadata *Metadata) error {
	if metadata == nil {
		return nil // Metadata is optional
	}

	// Validate version format
	if metadata.Version != "" && !l.isValidVersion(metadata.Version) {
		return errors.New(errors.ErrCodeValidationFailed, "version format không hợp lệ", nil)
	}

	// Validate dependencies
	for _, dep := range metadata.Dependencies {
		if strings.TrimSpace(dep) == "" {
			return errors.New(errors.ErrCodeValidationFailed, "dependency name không được để trống", nil)
		}
	}

	// Validate tenant scope
	if metadata.TenantScope != "" {
		validScopes := []string{"global", "tenant-specific", "multi-tenant"}
		if !l.contains(validScopes, metadata.TenantScope) {
			return errors.New(errors.ErrCodeValidationFailed, 
				fmt.Sprintf("tenant_scope không hợp lệ: %s. Phải là một trong: %v", 
					metadata.TenantScope, validScopes), nil)
		}
	}

	return nil
}

// isValidVersion kiểm tra format version (semantic versioning)
func (l *FileDataLoader) isValidVersion(version string) bool {
	// Simple validation for semantic versioning (x.y.z)
	parts := strings.Split(version, ".")
	if len(parts) != 3 {
		return false
	}
	
	for _, part := range parts {
		if strings.TrimSpace(part) == "" {
			return false
		}
	}
	
	return true
}

// contains kiểm tra xem slice có chứa item không
func (l *FileDataLoader) contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// GetDataFilePath trả về đường dẫn đầy đủ đến data file
func GetDataFilePath(config *Config, moduleName, fileName string) string {
	return filepath.Join(config.DataPath, "modules", moduleName, "seeds", "data", fileName)
}

// GetModuleDataPath trả về đường dẫn đến thư mục data của module
func GetModuleDataPath(config *Config, moduleName string) string {
	return filepath.Join(config.DataPath, "modules", moduleName, "seeds", "data")
}

// ListDataFiles trả về danh sách tất cả data files trong module
func ListDataFiles(config *Config, moduleName string) ([]string, error) {
	dataPath := GetModuleDataPath(config, moduleName)
	
	files, err := os.ReadDir(dataPath)
	if err != nil {
		return nil, fmt.Errorf("lỗi đọc thư mục %s: %w", dataPath, err)
	}

	var dataFiles []string
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".json") {
			dataFiles = append(dataFiles, file.Name())
		}
	}

	return dataFiles, nil
}
