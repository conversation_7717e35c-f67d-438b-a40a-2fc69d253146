package seed

import (
	"context"
	"fmt"
	"reflect"

	"wnapi/internal/pkg/errors"
	"wnapi/internal/pkg/logger"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// DatabaseOperations interface cho các thao tác database trong seeding
type DatabaseOperations interface {
	BatchInsert(ctx context.Context, tableName string, data []interface{}, batchSize int) error
	UpsertData(ctx context.Context, tableName string, data interface{}, conflictColumns []string) error
	CheckDataExists(ctx context.Context, tableName string, conditions map[string]interface{}) (bool, error)
	BulkUpsert(ctx context.Context, tableName string, data []interface{}, conflictColumns []string, batchSize int) error
	DeleteByConditions(ctx context.Context, tableName string, conditions map[string]interface{}) error
	CountRecords(ctx context.Context, tableName string, conditions map[string]interface{}) (int64, error)
}

// GormDatabaseOperations implements DatabaseOperations using GORM
type GormDatabaseOperations struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewGormDatabaseOperations tạo một GormDatabaseOperations mới
func NewGormDatabaseOperations(db *gorm.DB, logger logger.Logger) *GormDatabaseOperations {
	return &GormDatabaseOperations{
		db:     db,
		logger: logger,
	}
}

// BatchInsert insert data theo batch để tối ưu performance
func (g *GormDatabaseOperations) BatchInsert(ctx context.Context, tableName string, data []interface{}, batchSize int) error {
	if len(data) == 0 {
		return nil
	}

	// Validate batch size
	if batchSize <= 0 {
		batchSize = 50 // Default batch size
	}

	g.logger.Info("Bắt đầu batch insert",
		logger.String("table", tableName),
		logger.Int("total_records", len(data)),
		logger.Int("batch_size", batchSize))

	// Process in batches
	for i := 0; i < len(data); i += batchSize {
		end := i + batchSize
		if end > len(data) {
			end = len(data)
		}

		batch := data[i:end]

		// Execute batch insert
		if err := g.db.WithContext(ctx).Table(tableName).Create(batch).Error; err != nil {
			g.logger.Error("Lỗi batch insert",
				logger.String("table", tableName),
				logger.Int("batch_start", i),
				logger.Int("batch_end", end),
				logger.String("error", err.Error()))
			return fmt.Errorf("lỗi batch insert vào table %s (batch %d-%d): %w", tableName, i, end, err)
		}

		g.logger.Debug("Hoàn thành batch",
			logger.String("table", tableName),
			logger.Int("batch_start", i),
			logger.Int("batch_end", end))
	}

	g.logger.Info("Hoàn thành batch insert",
		logger.String("table", tableName),
		logger.Int("total_records", len(data)))

	return nil
}

// UpsertData insert hoặc update nếu data đã tồn tại
func (g *GormDatabaseOperations) UpsertData(ctx context.Context, tableName string, data interface{}, conflictColumns []string) error {
	if data == nil {
		return errors.New(errors.ErrCodeValidationFailed, "data không được để trống", nil)
	}

	g.logger.Debug("Thực hiện upsert",
		"table", tableName,
		"conflict_columns", fmt.Sprintf("%v", conflictColumns))

	// Build conflict clause
	var conflictClause clause.OnConflict
	if len(conflictColumns) > 0 {
		conflictClause.Columns = make([]clause.Column, len(conflictColumns))
		for i, col := range conflictColumns {
			conflictClause.Columns[i] = clause.Column{Name: col}
		}
		conflictClause.DoUpdates = clause.AssignmentColumns(conflictColumns)
	} else {
		conflictClause.DoNothing = true
	}

	// Execute upsert
	if err := g.db.WithContext(ctx).Table(tableName).Clauses(conflictClause).Create(data).Error; err != nil {
		g.logger.Error("Lỗi upsert data",
			logger.String("table", tableName),
			logger.String("error", err.Error()))
		return fmt.Errorf("lỗi upsert data vào table %s: %w", tableName, err)
	}

	return nil
}

// CheckDataExists kiểm tra data đã tồn tại chưa
func (g *GormDatabaseOperations) CheckDataExists(ctx context.Context, tableName string, conditions map[string]interface{}) (bool, error) {
	if len(conditions) == 0 {
		return false, errors.New(errors.ErrCodeValidationFailed, "conditions không được để trống", nil)
	}

	var count int64
	query := g.db.WithContext(ctx).Table(tableName)

	// Add conditions
	for key, value := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", key), value)
	}

	if err := query.Count(&count).Error; err != nil {
		g.logger.Error("Lỗi kiểm tra data existence",
			"table", tableName,
			"conditions", fmt.Sprintf("%v", conditions),
			"error", err.Error())
		return false, fmt.Errorf("lỗi kiểm tra data existence trong table %s: %w", tableName, err)
	}

	return count > 0, nil
}

// BulkUpsert thực hiện bulk upsert với batch processing
func (g *GormDatabaseOperations) BulkUpsert(ctx context.Context, tableName string, data []interface{}, conflictColumns []string, batchSize int) error {
	if len(data) == 0 {
		return nil
	}

	// Validate batch size
	if batchSize <= 0 {
		batchSize = 50
	}

	g.logger.Info("Bắt đầu bulk upsert",
		"table", tableName,
		"total_records", len(data),
		"batch_size", batchSize,
		"conflict_columns", fmt.Sprintf("%v", conflictColumns))

	// Build conflict clause
	var conflictClause clause.OnConflict
	if len(conflictColumns) > 0 {
		conflictClause.Columns = make([]clause.Column, len(conflictColumns))
		for i, col := range conflictColumns {
			conflictClause.Columns[i] = clause.Column{Name: col}
		}
		conflictClause.DoUpdates = clause.AssignmentColumns(conflictColumns)
	} else {
		conflictClause.DoNothing = true
	}

	// Process in batches
	for i := 0; i < len(data); i += batchSize {
		end := i + batchSize
		if end > len(data) {
			end = len(data)
		}

		batch := data[i:end]

		// Execute batch upsert
		if err := g.db.WithContext(ctx).Table(tableName).Clauses(conflictClause).Create(batch).Error; err != nil {
			g.logger.Error("Lỗi bulk upsert batch",
				logger.String("table", tableName),
				logger.Int("batch_start", i),
				logger.Int("batch_end", end),
				logger.String("error", err.Error()))
			return fmt.Errorf("lỗi bulk upsert vào table %s (batch %d-%d): %w", tableName, i, end, err)
		}

		g.logger.Debug("Hoàn thành upsert batch",
			logger.String("table", tableName),
			logger.Int("batch_start", i),
			logger.Int("batch_end", end))
	}

	g.logger.Info("Hoàn thành bulk upsert",
		logger.String("table", tableName),
		logger.Int("total_records", len(data)))

	return nil
}

// DeleteByConditions xóa records theo conditions
func (g *GormDatabaseOperations) DeleteByConditions(ctx context.Context, tableName string, conditions map[string]interface{}) error {
	if len(conditions) == 0 {
		return errors.New(errors.ErrCodeValidationFailed, "conditions không được để trống để tránh xóa toàn bộ table", nil)
	}

	query := g.db.WithContext(ctx).Table(tableName)

	// Add conditions
	for key, value := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", key), value)
	}

	result := query.Delete(nil)
	if result.Error != nil {
		g.logger.Error("Lỗi delete records",
			"table", tableName,
			"conditions", fmt.Sprintf("%v", conditions),
			"error", result.Error.Error())
		return fmt.Errorf("lỗi delete records từ table %s: %w", tableName, result.Error)
	}

	g.logger.Info("Đã xóa records",
		"table", tableName,
		"conditions", fmt.Sprintf("%v", conditions),
		"affected_rows", result.RowsAffected)

	return nil
}

// CountRecords đếm số records theo conditions
func (g *GormDatabaseOperations) CountRecords(ctx context.Context, tableName string, conditions map[string]interface{}) (int64, error) {
	var count int64
	query := g.db.WithContext(ctx).Table(tableName)

	// Add conditions
	for key, value := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", key), value)
	}

	if err := query.Count(&count).Error; err != nil {
		g.logger.Error("Lỗi count records",
			"table", tableName,
			"conditions", fmt.Sprintf("%v", conditions),
			"error", err.Error())
		return 0, fmt.Errorf("lỗi count records trong table %s: %w", tableName, err)
	}

	return count, nil
}

// WithTransaction thực hiện operations trong transaction
func (g *GormDatabaseOperations) WithTransaction(ctx context.Context, fn func(*gorm.DB) error) error {
	return g.db.WithContext(ctx).Transaction(fn)
}

// GetTableName lấy tên table từ model struct
func GetTableName(model interface{}) string {
	if tabler, ok := model.(interface{ TableName() string }); ok {
		return tabler.TableName()
	}

	// Fallback to reflect
	modelType := reflect.TypeOf(model)
	if modelType.Kind() == reflect.Ptr {
		modelType = modelType.Elem()
	}

	return modelType.Name()
}
