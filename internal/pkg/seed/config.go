package seed

import (
	"os"
	"strconv"
	"time"
)

// Config chứa cấu hình cho seed system
type Config struct {
	Environment string            `yaml:"environment" json:"environment"` // dev, staging, prod
	DataPath    string            `yaml:"data_path" json:"data_path"`     // Đường dẫn đến thư mục data
	BatchSize   int               `yaml:"batch_size" json:"batch_size"`   // Số record insert mỗi batch
	SkipExists  bool              `yaml:"skip_exists" json:"skip_exists"` // Skip nếu data đã tồn tại
	DryRun      bool              `yaml:"dry_run" json:"dry_run"`         // Dry run mode
	Verbose     bool              `yaml:"verbose" json:"verbose"`         // Verbose logging
	Modules     map[string]bool   `yaml:"modules" json:"modules"`         // Modules được phép seed
	TenantID    uint              `yaml:"tenant_id" json:"tenant_id"`     // Default tenant ID
	UserID      *uint             `yaml:"user_id" json:"user_id"`         // User ID cho audit
}

// SeedData chứa dữ liệu seed từ JSON files
type SeedData struct {
	Environment string      `json:"environment"` // env này data áp dụng cho
	Data        interface{} `json:"data"`        // Actual data
	Metadata    Metadata    `json:"metadata"`    // Thông tin metadata
}

// Metadata chứa thông tin metadata của seed data
type Metadata struct {
	Version      string            `json:"version"`
	CreatedAt    string            `json:"created_at"`
	Description  string            `json:"description"`
	Dependencies []string          `json:"dependencies"` // Dependencies với seeder khác
	Tags         []string          `json:"tags"`         // Tags để filter
	Author       string            `json:"author"`       // Tác giả của seed data
	Environment  []string          `json:"environment"`  // Environments áp dụng
	TenantScope  string            `json:"tenant_scope"` // global, tenant-specific, multi-tenant
}

// DefaultConfig trả về cấu hình mặc định
func DefaultConfig() *Config {
	return &Config{
		Environment: "dev",
		DataPath:    ".",
		BatchSize:   50,
		SkipExists:  true,
		DryRun:      false,
		Verbose:     false,
		Modules:     make(map[string]bool),
		TenantID:    1, // Default tenant
		UserID:      nil,
	}
}

// LoadConfig load cấu hình từ environment variables
func LoadConfig() *Config {
	config := DefaultConfig()

	// Load từ environment variables
	if env := os.Getenv("SEED_ENVIRONMENT"); env != "" {
		config.Environment = env
	}

	if dataPath := os.Getenv("SEED_DATA_PATH"); dataPath != "" {
		config.DataPath = dataPath
	}

	if batchSizeStr := os.Getenv("SEED_BATCH_SIZE"); batchSizeStr != "" {
		if batchSize, err := strconv.Atoi(batchSizeStr); err == nil {
			config.BatchSize = batchSize
		}
	}

	if skipExistsStr := os.Getenv("SEED_SKIP_EXISTS"); skipExistsStr != "" {
		if skipExists, err := strconv.ParseBool(skipExistsStr); err == nil {
			config.SkipExists = skipExists
		}
	}

	if dryRunStr := os.Getenv("SEED_DRY_RUN"); dryRunStr != "" {
		if dryRun, err := strconv.ParseBool(dryRunStr); err == nil {
			config.DryRun = dryRun
		}
	}

	if verboseStr := os.Getenv("SEED_VERBOSE"); verboseStr != "" {
		if verbose, err := strconv.ParseBool(verboseStr); err == nil {
			config.Verbose = verbose
		}
	}

	if tenantIDStr := os.Getenv("SEED_TENANT_ID"); tenantIDStr != "" {
		if tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32); err == nil {
			config.TenantID = uint(tenantID)
		}
	}

	if userIDStr := os.Getenv("SEED_USER_ID"); userIDStr != "" {
		if userID, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
			uid := uint(userID)
			config.UserID = &uid
		}
	}

	return config
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.Environment == "" {
		c.Environment = "dev"
	}

	if c.DataPath == "" {
		c.DataPath = "."
	}

	if c.BatchSize <= 0 {
		c.BatchSize = 50
	}

	if c.TenantID == 0 {
		c.TenantID = 1
	}

	return nil
}

// GetEnvironmentPriority trả về danh sách environments theo thứ tự ưu tiên
func (c *Config) GetEnvironmentPriority() []string {
	return []string{
		c.Environment,           // Environment cụ thể
		"common",               // Common data
		"default",              // Default fallback
	}
}

// IsModuleEnabled kiểm tra xem module có được enable không
func (c *Config) IsModuleEnabled(moduleName string) bool {
	if len(c.Modules) == 0 {
		return true // Nếu không có config, enable tất cả
	}
	enabled, exists := c.Modules[moduleName]
	return exists && enabled
}

// SetModuleEnabled enable/disable module
func (c *Config) SetModuleEnabled(moduleName string, enabled bool) {
	if c.Modules == nil {
		c.Modules = make(map[string]bool)
	}
	c.Modules[moduleName] = enabled
}

// Clone tạo một bản copy của config
func (c *Config) Clone() *Config {
	clone := *c
	clone.Modules = make(map[string]bool)
	for k, v := range c.Modules {
		clone.Modules[k] = v
	}
	return &clone
}

// WithEnvironment tạo config mới với environment khác
func (c *Config) WithEnvironment(env string) *Config {
	clone := c.Clone()
	clone.Environment = env
	return clone
}

// WithTenantID tạo config mới với tenant ID khác
func (c *Config) WithTenantID(tenantID uint) *Config {
	clone := c.Clone()
	clone.TenantID = tenantID
	return clone
}

// WithDryRun tạo config mới với dry run mode
func (c *Config) WithDryRun(dryRun bool) *Config {
	clone := c.Clone()
	clone.DryRun = dryRun
	return clone
}

// GetCurrentTime trả về thời gian hiện tại theo format chuẩn
func GetCurrentTime() string {
	return time.Now().Format(time.RFC3339)
}
