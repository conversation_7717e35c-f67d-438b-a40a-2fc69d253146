package seed

import (
	"context"
	"fmt"
	"sort"

	"wnapi/internal/pkg/errors"
	"wnapi/internal/pkg/logger"

	"gorm.io/gorm"
)

// Runner implements SeedRunner interface
type Runner struct {
	db       *gorm.DB
	logger   logger.Logger
	config   *Config
	registry *Registry
	dbOps    *GormDatabaseOperations
}

// NewRunner tạo một Runner mới
func NewRunner(db *gorm.DB, logger logger.Logger, config *Config) *Runner {
	if config == nil {
		config = DefaultConfig()
	}

	return &Runner{
		db:       db,
		logger:   logger,
		config:   config,
		registry: GlobalRegistry,
		dbOps:    NewGormDatabaseOperations(db, logger),
	}
}

// SeedAllModules chạy tất cả module seeds
func (r *Runner) SeedAllModules(ctx context.Context) error {
	r.logger.Info("Bắt đầu seed tất cả modules", "environment", r.config.Environment)

	moduleSeeds := r.registry.GetAllModuleSeeds()
	if len(moduleSeeds) == 0 {
		r.logger.Warn("Không có module seeds nào được đăng ký")
		return nil
	}

	// Validate dependencies trước khi chạy
	if err := r.registry.ValidateDependencies(); err != nil {
		return fmt.Errorf("lỗi validate dependencies: %w", err)
	}

	// Sort modules để đảm bảo thứ tự consistent
	var moduleNames []string
	for name := range moduleSeeds {
		if r.config.IsModuleEnabled(name) {
			moduleNames = append(moduleNames, name)
		}
	}
	sort.Strings(moduleNames)

	// Chạy từng module
	for _, moduleName := range moduleNames {
		moduleSeed := moduleSeeds[moduleName]
		
		r.logger.Info("Bắt đầu seed module", "module", moduleName)
		
		if err := r.seedModuleWithTransaction(ctx, moduleSeed); err != nil {
			r.logger.Error("Lỗi seed module", "module", moduleName, "error", err.Error())
			return fmt.Errorf("lỗi seed module %s: %w", moduleName, err)
		}
		
		r.logger.Info("Hoàn thành seed module", "module", moduleName)
	}

	r.logger.Info("Hoàn thành seed tất cả modules")
	return nil
}

// SeedModule chạy seed cho module cụ thể
func (r *Runner) SeedModule(ctx context.Context, moduleName string) error {
	r.logger.Info("Bắt đầu seed module", "module", moduleName, "environment", r.config.Environment)

	if !r.config.IsModuleEnabled(moduleName) {
		return errors.New(errors.ErrCodeForbidden, 
			fmt.Sprintf("module '%s' không được enable", moduleName), nil)
	}

	moduleSeed, err := r.registry.GetModuleSeed(moduleName)
	if err != nil {
		return err
	}

	return r.seedModuleWithTransaction(ctx, moduleSeed)
}

// SeedSpecific chạy seeder cụ thể trong module
func (r *Runner) SeedSpecific(ctx context.Context, moduleName, seederName string) error {
	r.logger.Info("Bắt đầu seed specific", 
		"module", moduleName, 
		"seeder", seederName, 
		"environment", r.config.Environment)

	if !r.config.IsModuleEnabled(moduleName) {
		return errors.New(errors.ErrCodeForbidden, 
			fmt.Sprintf("module '%s' không được enable", moduleName), nil)
	}

	moduleSeed, err := r.registry.GetModuleSeed(moduleName)
	if err != nil {
		return err
	}

	return r.dbOps.WithTransaction(ctx, func(tx *gorm.DB) error {
		return moduleSeed.SeedSpecific(ctx, seederName)
	})
}

// ListAvailableSeeds trả về danh sách tất cả seeds available
func (r *Runner) ListAvailableSeeds() map[string][]string {
	return r.registry.ListAvailableSeeds()
}

// RollbackModule rollback tất cả seeders trong module
func (r *Runner) RollbackModule(ctx context.Context, moduleName string) error {
	r.logger.Info("Bắt đầu rollback module", "module", moduleName)

	moduleSeed, err := r.registry.GetModuleSeed(moduleName)
	if err != nil {
		return err
	}

	return r.dbOps.WithTransaction(ctx, func(tx *gorm.DB) error {
		seeders := moduleSeed.GetSeeders()
		
		// Rollback theo thứ tự ngược lại
		for i := len(seeders) - 1; i >= 0; i-- {
			seeder := seeders[i]
			r.logger.Info("Rollback seeder", "module", moduleName, "seeder", seeder.Name())
			
			if err := seeder.Rollback(ctx); err != nil {
				r.logger.Error("Lỗi rollback seeder", 
					"module", moduleName, 
					"seeder", seeder.Name(), 
					"error", err.Error())
				return fmt.Errorf("lỗi rollback seeder %s: %w", seeder.Name(), err)
			}
		}
		
		return nil
	})
}

// RollbackSpecific rollback seeder cụ thể
func (r *Runner) RollbackSpecific(ctx context.Context, moduleName, seederName string) error {
	r.logger.Info("Bắt đầu rollback specific", "module", moduleName, "seeder", seederName)

	moduleSeed, err := r.registry.GetModuleSeed(moduleName)
	if err != nil {
		return err
	}

	// Tìm seeder cụ thể
	var targetSeeder Seeder
	for _, seeder := range moduleSeed.GetSeeders() {
		if seeder.Name() == seederName {
			targetSeeder = seeder
			break
		}
	}

	if targetSeeder == nil {
		return errors.New(errors.ErrCodeNotFound, 
			fmt.Sprintf("không tìm thấy seeder '%s' trong module '%s'", seederName, moduleName), nil)
	}

	return r.dbOps.WithTransaction(ctx, func(tx *gorm.DB) error {
		return targetSeeder.Rollback(ctx)
	})
}

// seedModuleWithTransaction chạy seed module trong transaction
func (r *Runner) seedModuleWithTransaction(ctx context.Context, moduleSeed ModuleSeed) error {
	if r.config.DryRun {
		r.logger.Info("Dry run mode - không thực hiện thay đổi database", "module", moduleSeed.ModuleName())
		return r.validateModuleSeed(ctx, moduleSeed)
	}

	return r.dbOps.WithTransaction(ctx, func(tx *gorm.DB) error {
		return moduleSeed.SeedAll(ctx)
	})
}

// validateModuleSeed validate module seed trong dry run mode
func (r *Runner) validateModuleSeed(ctx context.Context, moduleSeed ModuleSeed) error {
	seeders := moduleSeed.GetSeeders()
	
	r.logger.Info("Validating module seeders", 
		"module", moduleSeed.ModuleName(), 
		"seeder_count", len(seeders))

	// Validate dependencies
	for _, seeder := range seeders {
		for _, dep := range seeder.Dependencies() {
			found := false
			for _, s := range seeders {
				if s.Name() == dep {
					found = true
					break
				}
			}
			if !found {
				return errors.New(errors.ErrCodeValidationFailed, 
					fmt.Sprintf("seeder '%s' phụ thuộc vào '%s' nhưng không tìm thấy trong module '%s'", 
						seeder.Name(), dep, moduleSeed.ModuleName()), nil)
			}
		}
	}

	r.logger.Info("Module validation passed", "module", moduleSeed.ModuleName())
	return nil
}

// GetConfig trả về configuration hiện tại
func (r *Runner) GetConfig() *Config {
	return r.config
}

// SetConfig cập nhật configuration
func (r *Runner) SetConfig(config *Config) {
	r.config = config
}

// GetDatabaseOperations trả về database operations instance
func (r *Runner) GetDatabaseOperations() *GormDatabaseOperations {
	return r.dbOps
}

// GetStats trả về thống kê về seeds
func (r *Runner) GetStats() SeedStats {
	moduleSeeds := r.registry.GetAllModuleSeeds()
	
	stats := SeedStats{
		TotalModules: len(moduleSeeds),
		TotalSeeders: 0,
		EnabledModules: 0,
	}

	for moduleName, moduleSeed := range moduleSeeds {
		stats.TotalSeeders += len(moduleSeed.GetSeeders())
		if r.config.IsModuleEnabled(moduleName) {
			stats.EnabledModules++
		}
	}

	return stats
}

// SeedStats chứa thống kê về seeds
type SeedStats struct {
	TotalModules   int `json:"total_modules"`
	TotalSeeders   int `json:"total_seeders"`
	EnabledModules int `json:"enabled_modules"`
}
