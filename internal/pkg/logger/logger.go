package logger

import (
	"fmt"
	"io"
	"log"
	"os"
	"strings"
	"time"
)

// Level xác định mức độ logging
type Level int

const (
	LevelDebug Level = iota
	LevelInfo
	LevelWarn
	LevelError
	LevelFatal
)

// String chuyển đổi Level thành chuỗi
func (l Level) String() string {
	switch l {
	case LevelDebug:
		return "DEBUG"
	case LevelInfo:
		return "INFO"
	case LevelWarn:
		return "WARN"
	case LevelError:
		return "ERROR"
	case LevelFatal:
		return "FATAL"
	default:
		return "UNKNOWN"
	}
}

// Logger định nghĩa interface cho logger
type Logger interface {
	// Debug ghi log debug
	Debug(msg string, args ...interface{})

	// Info ghi log info
	Info(msg string, args ...interface{})

	// Warn ghi log warn
	Warn(msg string, args ...interface{})

	// Error ghi log error
	Error(msg string, args ...interface{})

	// Fatal ghi log fatal
	Fatal(msg string, args ...interface{})
}

// ConsoleLogger triển khai logger in ra console
type ConsoleLogger struct {
	name      string
	level     Level
	logger    *log.Logger
	withColor bool
}

// NewConsoleLogger tạo một ConsoleLogger mới
func NewConsoleLogger(name string, level Level) *ConsoleLogger {
	return &ConsoleLogger{
		name:      name,
		level:     level,
		logger:    log.New(os.Stdout, "", 0),
		withColor: true,
	}
}

// NewConsoleLoggerWithWriter tạo một ConsoleLogger với writer tùy chỉnh
func NewConsoleLoggerWithWriter(name string, level Level, writer io.Writer) *ConsoleLogger {
	return &ConsoleLogger{
		name:      name,
		level:     level,
		logger:    log.New(writer, "", 0),
		withColor: false,
	}
}

// Debug logs debug message
func (l *ConsoleLogger) Debug(msg string, keysAndValues ...interface{}) {
	if l.level <= LevelDebug {
		l.log(LevelDebug, msg, keysAndValues...)
	}
}

// Info logs info message
func (l *ConsoleLogger) Info(msg string, keysAndValues ...interface{}) {
	if l.level <= LevelInfo {
		l.log(LevelInfo, msg, keysAndValues...)
	}
}

// Warn logs warning message
func (l *ConsoleLogger) Warn(msg string, keysAndValues ...interface{}) {
	if l.level <= LevelWarn {
		l.log(LevelWarn, msg, keysAndValues...)
	}
}

// Error logs error message
func (l *ConsoleLogger) Error(msg string, keysAndValues ...interface{}) {
	if l.level <= LevelError {
		l.log(LevelError, msg, keysAndValues...)
	}
}

// Fatal logs fatal message and exits
func (l *ConsoleLogger) Fatal(msg string, keysAndValues ...interface{}) {
	if l.level <= LevelFatal {
		l.log(LevelFatal, msg, keysAndValues...)
		os.Exit(1)
	}
}

// log xử lý việc ghi log với format nhất quán
func (l *ConsoleLogger) log(level Level, msg string, keysAndValues ...interface{}) {
	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	levelStr := level.String()

	if l.withColor {
		levelStr = l.colorize(level, levelStr)
	}

	kvStr := ""
	if len(keysAndValues) > 0 {
		kvStr = l.formatKeyValues(keysAndValues...)
	}

	log := fmt.Sprintf("%s [%s] %s: %s%s", timestamp, levelStr, l.name, msg, kvStr)
	l.logger.Println(log)
}

// formatKeyValues format key-value pairs thành chuỗi
func (l *ConsoleLogger) formatKeyValues(keysAndValues ...interface{}) string {
	if len(keysAndValues) == 0 {
		return ""
	}

	var sb strings.Builder
	sb.WriteString(" {")

	for i := 0; i < len(keysAndValues); i += 2 {
		if i > 0 {
			sb.WriteString(", ")
		}

		// Key
		key := ""
		if i < len(keysAndValues) {
			key = fmt.Sprintf("%v", keysAndValues[i])
		}

		// Value
		value := ""
		if i+1 < len(keysAndValues) {
			value = fmt.Sprintf("%v", keysAndValues[i+1])
		}

		sb.WriteString(fmt.Sprintf("%s: %s", key, value))
	}

	sb.WriteString("}")
	return sb.String()
}

// colorize thêm màu sắc cho level
func (l *ConsoleLogger) colorize(level Level, text string) string {
	var color string
	switch level {
	case LevelDebug:
		color = "\033[37m" // Gray
	case LevelInfo:
		color = "\033[32m" // Green
	case LevelWarn:
		color = "\033[33m" // Yellow
	case LevelError:
		color = "\033[31m" // Red
	case LevelFatal:
		color = "\033[35m" // Purple
	default:
		color = "\033[0m" // Reset
	}

	reset := "\033[0m"
	return fmt.Sprintf("%s%s%s", color, text, reset)
}

// String tạo key-value pair cho string
func String(key string, value string) interface{} {
	return []interface{}{key, value}
}

// Int tạo key-value pair cho int
func Int(key string, value int) interface{} {
	return []interface{}{key, value}
}
