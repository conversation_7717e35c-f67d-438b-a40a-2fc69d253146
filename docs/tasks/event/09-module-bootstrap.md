# Task 09: Event Module Bootstrap Setup

## M<PERSON><PERSON> tiêu
Tạo event module theo pattern của các modules kh<PERSON><PERSON> trong hệ thống, bao gồm module structure, bootstrap functions, và integration với core framework.

## Input/Output
**Input**: 
- Tasks 01-08 (Event core infrastructure)
- Existing module patterns từ auth/rbac modules
**Output**: 
- `modules/event/` directory structure
- `modules/event/bootstrap.go` - Module bootstrap
- `modules/event/module.go` - Module implementation
- Integration với core framework

## Yêu cầu
1. Tuân thủ module structure pattern từ existing modules
2. Integration với core.App và core.Server
3. Module registration và lifecycle management
4. Configuration loading và validation
5. Dependency injection pattern
6. Multi-tenant support built-in

## C<PERSON>c bước thực hiện

### 1. Tạo cấu trúc thư mục module
```bash
mkdir -p modules/event
mkdir -p modules/event/api
mkdir -p modules/event/api/handlers
mkdir -p modules/event/internal
mkdir -p modules/event/internal/service
mkdir -p modules/event/repository
mkdir -p modules/event/repository/mysql
mkdir -p modules/event/service
mkdir -p modules/event/models
mkdir -p modules/event/migrations
mkdir -p modules/event/configs
mkdir -p modules/event/events
```

### 2. Tạo file `modules/event/module.go`
```go
package event

import (
	"context"
	"fmt"
	"path/filepath"
	"time"

	"wnapi/internal/core"
	"wnapi/internal/pkg/events"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/event/api"
	"wnapi/modules/event/configs"
	"wnapi/modules/event/internal/service"

	"gorm.io/gorm"
)

func init() {
	core.RegisterModuleFactory("event", NewModule)
}

// Module triển khai event module
type Module struct {
	name     string
	logger   logger.Logger
	config   map[string]interface{}
	app      *core.App
	handler  *api.Handler
	eventBus events.EventBus
	
	// Services
	eventService *service.EventService
	
	// Module state
	initialized bool
	started     bool
}

// NewModule tạo module mới
func NewModule(app *core.App, config map[string]interface{}) (core.Module, error) {
	module := &Module{
		name:   "event",
		logger: app.Logger(),
		config: config,
		app:    app,
	}

	if err := module.initialize(); err != nil {
		return nil, fmt.Errorf("failed to initialize event module: %w", err)
	}

	return module, nil
}

// initialize khởi tạo module components
func (m *Module) initialize() error {
	m.logger.Info("Initializing event module")

	// Load module configuration
	moduleConfig, err := m.loadModuleConfig()
	if err != nil {
		return fmt.Errorf("failed to load module config: %w", err)
	}

	// Load event system configuration
	eventConfig, err := events.LoadConfigFromEnv()
	if err != nil {
		return fmt.Errorf("failed to load event config: %w", err)
	}

	// Create event bus
	eventBus, err := events.NewEventBusFromConfig(*eventConfig, m.logger, nil)
	if err != nil {
		return fmt.Errorf("failed to create event bus: %w", err)
	}
	m.eventBus = eventBus

	// Initialize services
	if err := m.initializeServices(); err != nil {
		return fmt.Errorf("failed to initialize services: %w", err)
	}

	// Initialize API handler
	if err := m.initializeHandler(); err != nil {
		return fmt.Errorf("failed to initialize handler: %w", err)
	}

	m.initialized = true
	m.logger.Info("Event module initialized successfully")
	return nil
}

// loadModuleConfig tải cấu hình module
func (m *Module) loadModuleConfig() (*configs.Config, error) {
	// Load from environment or config file
	config := &configs.Config{
		Module: configs.ModuleConfig{
			Enabled: true,
			Name:    "event",
		},
		API: configs.APIConfig{
			Enabled: true,
			Prefix:  "/api/v1/events",
		},
		Events: configs.EventConfig{
			Enabled:           true,
			ProcessingEnabled: true,
			MonitoringEnabled: true,
		},
	}

	return config, nil
}

// initializeServices khởi tạo các services
func (m *Module) initializeServices() error {
	// Get database connection
	db := m.app.DBManager().DB()
	if db == nil {
		return fmt.Errorf("database connection not available")
	}

	// Initialize event service
	m.eventService = service.NewEventService(
		m.eventBus,
		m.logger,
	)

	return nil
}

// initializeHandler khởi tạo API handler
func (m *Module) initializeHandler() error {
	if m.eventService == nil {
		return fmt.Errorf("event service not initialized")
	}

	m.handler = api.NewHandler(
		m.eventService,
		m.eventBus,
		m.logger,
	)

	return nil
}

// Core.Module interface implementation

// Name trả về tên module
func (m *Module) Name() string {
	return m.name
}

// Init khởi tạo module
func (m *Module) Init(ctx context.Context) error {
	if !m.initialized {
		return fmt.Errorf("module not initialized")
	}

	m.logger.Info("Starting event module initialization")

	// Start event bus
	if err := m.eventBus.Start(ctx); err != nil {
		return fmt.Errorf("failed to start event bus: %w", err)
	}

	// Register default event handlers
	if err := m.registerDefaultHandlers(ctx); err != nil {
		return fmt.Errorf("failed to register default handlers: %w", err)
	}

	m.started = true
	m.logger.Info("Event module started successfully")
	return nil
}

// RegisterRoutes đăng ký các route của module
func (m *Module) RegisterRoutes(server *core.Server) error {
	if m.handler == nil {
		m.logger.Warn("Event handler is not initialized, skipping route registration")
		return nil
	}

	err := registerRoutes(server, m.handler)
	if err != nil {
		return fmt.Errorf("failed to register routes: %w", err)
	}

	m.logger.Info("Event module routes registered")
	return nil
}

// Cleanup dọn dẹp tài nguyên của module
func (m *Module) Cleanup(ctx context.Context) error {
	m.logger.Info("Cleaning up event module")

	if m.eventBus != nil {
		// Stop event bus
		if err := m.eventBus.Stop(ctx); err != nil {
			m.logger.Error("Failed to stop event bus", "error", err)
		}

		// Close event bus
		if err := m.eventBus.Close(); err != nil {
			m.logger.Error("Failed to close event bus", "error", err)
		}
	}

	m.started = false
	m.logger.Info("Event module cleanup completed")
	return nil
}

// GetMigrationPath trả về đường dẫn chứa migrations
func (m *Module) GetMigrationPath() string {
	return filepath.Join("modules", "event", "migrations")
}

// Module-specific methods

// GetEventBus trả về event bus instance
func (m *Module) GetEventBus() events.EventBus {
	return m.eventBus
}

// GetEventService trả về event service instance
func (m *Module) GetEventService() *service.EventService {
	return m.eventService
}

// IsStarted kiểm tra module đã start chưa
func (m *Module) IsStarted() bool {
	return m.started
}

// registerDefaultHandlers đăng ký các event handlers mặc định
func (m *Module) registerDefaultHandlers(ctx context.Context) error {
	// Register system event handlers
	systemHandler := events.NewBaseEventHandler(
		"system_event_handler",
		[]string{"system.*"},
		m.handleSystemEvent,
	)

	if err := m.eventBus.Subscribe(ctx, "system.*", systemHandler); err != nil {
		return fmt.Errorf("failed to register system event handler: %w", err)
	}

	// Register monitoring event handler
	monitoringHandler := events.NewBaseEventHandler(
		"monitoring_event_handler",
		[]string{"*"}, // Listen to all events for monitoring
		m.handleMonitoringEvent,
	)
	monitoringHandler.SetPriority(-1) // Low priority

	if err := m.eventBus.Subscribe(ctx, "*", monitoringHandler); err != nil {
		return fmt.Errorf("failed to register monitoring event handler: %w", err)
	}

	m.logger.Info("Default event handlers registered")
	return nil
}

// handleSystemEvent xử lý system events
func (m *Module) handleSystemEvent(ctx context.Context, event events.Event) error {
	m.logger.Info("Processing system event",
		"event_type", event.Type(),
		"event_id", event.ID(),
		"tenant_id", event.TenantID(),
	)

	// Process system events (health checks, maintenance, etc.)
	switch event.Type() {
	case "system.health_check":
		return m.handleHealthCheckEvent(ctx, event)
	case "system.maintenance_mode":
		return m.handleMaintenanceModeEvent(ctx, event)
	default:
		m.logger.Debug("Unknown system event type", "event_type", event.Type())
	}

	return nil
}

// handleMonitoringEvent xử lý monitoring cho tất cả events
func (m *Module) handleMonitoringEvent(ctx context.Context, event events.Event) error {
	// Log event for monitoring/analytics
	m.logger.Debug("Event processed for monitoring",
		"event_type", event.Type(),
		"event_id", event.ID(),
		"tenant_id", event.TenantID(),
		"website_id", event.WebsiteID(),
		"timestamp", event.Timestamp(),
	)

	// TODO: Send to analytics/monitoring system
	// TODO: Update metrics
	// TODO: Store in event store if enabled

	return nil
}

// handleHealthCheckEvent xử lý health check events
func (m *Module) handleHealthCheckEvent(ctx context.Context, event events.Event) error {
	// Respond to health check
	m.logger.Debug("Health check event received", "event_id", event.ID())
	return nil
}

// handleMaintenanceModeEvent xử lý maintenance mode events
func (m *Module) handleMaintenanceModeEvent(ctx context.Context, event events.Event) error {
	m.logger.Info("Maintenance mode event received", "event_id", event.ID())
	// TODO: Handle maintenance mode logic
	return nil
}
```

### 3. Tạo file `modules/event/bootstrap.go`
```go
package event

import (
	"wnapi/internal/core"
	"wnapi/modules/event/api"
)

// registerRoutes đăng ký các route của module event
func registerRoutes(server *core.Server, handler *api.Handler) error {
	return handler.RegisterRoutes(server)
}
```

### 4. Tạo file `modules/event/configs/config.go`
```go
package configs

import (
	"os"
	"strconv"
)

// Config cấu hình cho Event module
type Config struct {
	Module ModuleConfig `yaml:"module"`
	API    APIConfig    `yaml:"api"`
	Events EventConfig  `yaml:"events"`
}

// ModuleConfig cấu hình module cơ bản
type ModuleConfig struct {
	Enabled bool   `yaml:"enabled"`
	Name    string `yaml:"name"`
}

// APIConfig cấu hình API endpoints
type APIConfig struct {
	Enabled bool   `yaml:"enabled"`
	Prefix  string `yaml:"prefix"`
}

// EventConfig cấu hình event processing
type EventConfig struct {
	Enabled           bool `yaml:"enabled"`
	ProcessingEnabled bool `yaml:"processing_enabled"`
	MonitoringEnabled bool `yaml:"monitoring_enabled"`
}

// LoadEventModuleConfig tải cấu hình từ environment
func LoadEventModuleConfig() (*Config, error) {
	config := &Config{
		Module: ModuleConfig{
			Enabled: getBoolEnv("EVENT_MODULE_ENABLED", true),
			Name:    getStringEnv("EVENT_MODULE_NAME", "event"),
		},
		API: APIConfig{
			Enabled: getBoolEnv("EVENT_API_ENABLED", true),
			Prefix:  getStringEnv("EVENT_API_PREFIX", "/api/v1/events"),
		},
		Events: EventConfig{
			Enabled:           getBoolEnv("EVENT_PROCESSING_ENABLED", true),
			ProcessingEnabled: getBoolEnv("EVENT_PROCESSING_ENABLED", true),
			MonitoringEnabled: getBoolEnv("EVENT_MONITORING_ENABLED", true),
		},
	}

	return config, nil
}

// Helper functions
func getStringEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getBoolEnv(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}
```

### 5. Tạo file `modules/event/internal/service/event_service.go`
```go
package service

import (
	"context"
	"fmt"

	"wnapi/internal/pkg/events"
	"wnapi/internal/pkg/logger"
)

// EventService cung cấp business logic cho event operations
type EventService struct {
	eventBus events.EventBus
	logger   logger.Logger
}

// NewEventService tạo event service mới
func NewEventService(eventBus events.EventBus, logger logger.Logger) *EventService {
	return &EventService{
		eventBus: eventBus,
		logger:   logger,
	}
}

// PublishEvent gửi một event
func (s *EventService) PublishEvent(ctx context.Context, event events.Event) error {
	s.logger.Info("Publishing event",
		"event_type", event.Type(),
		"event_id", event.ID(),
		"tenant_id", event.TenantID(),
	)

	if err := s.eventBus.Publish(ctx, event); err != nil {
		s.logger.Error("Failed to publish event",
			"error", err,
			"event_type", event.Type(),
			"event_id", event.ID(),
		)
		return fmt.Errorf("failed to publish event: %w", err)
	}

	return nil
}

// PublishEventBatch gửi nhiều events
func (s *EventService) PublishEventBatch(ctx context.Context, events []events.Event) error {
	s.logger.Info("Publishing event batch", "count", len(events))

	if err := s.eventBus.PublishBatch(ctx, events); err != nil {
		s.logger.Error("Failed to publish event batch",
			"error", err,
			"count", len(events),
		)
		return fmt.Errorf("failed to publish event batch: %w", err)
	}

	return nil
}

// GetEventBusStats trả về thống kê event bus
func (s *EventService) GetEventBusStats() events.BusStats {
	return s.eventBus.Stats()
}

// GetEventBusHealth kiểm tra health của event bus
func (s *EventService) GetEventBusHealth() error {
	return s.eventBus.Health()
}

// RegisterEventHandler đăng ký event handler
func (s *EventService) RegisterEventHandler(ctx context.Context, eventType string, handler events.EventHandler) error {
	s.logger.Info("Registering event handler",
		"event_type", eventType,
		"handler_name", handler.Name(),
	)

	if err := s.eventBus.Subscribe(ctx, eventType, handler); err != nil {
		s.logger.Error("Failed to register event handler",
			"error", err,
			"event_type", eventType,
			"handler_name", handler.Name(),
		)
		return fmt.Errorf("failed to register event handler: %w", err)
	}

	return nil
}

// UnregisterEventHandler hủy đăng ký event handler
func (s *EventService) UnregisterEventHandler(eventType, handlerName string) error {
	s.logger.Info("Unregistering event handler",
		"event_type", eventType,
		"handler_name", handlerName,
	)

	if err := s.eventBus.UnregisterHandler(eventType, handlerName); err != nil {
		s.logger.Error("Failed to unregister event handler",
			"error", err,
			"event_type", eventType,
			"handler_name", handlerName,
		)
		return fmt.Errorf("failed to unregister event handler: %w", err)
	}

	return nil
}
```

## Kết quả mong đợi
- Event module structure theo pattern của existing modules
- Module registration với core framework
- Bootstrap functions và dependency injection
- Configuration loading và validation
- Event service layer implementation
- Default event handlers registration
- Integration với core.App và core.Server
- Lifecycle management (Init/Cleanup)
- Dự án vẫn biên dịch được

## Thời gian dự kiến
2 giờ làm việc

## Độ ưu tiên
Cao (Cần thiết để integrate event system với framework)

## Phụ thuộc
- Tasks 01-08: Event core infrastructure
- Existing module patterns từ auth/rbac

## Acceptance Criteria
1. ✅ Module structure tuân thủ existing patterns
2. ✅ Module registration với core framework
3. ✅ Bootstrap và dependency injection hoạt động
4. ✅ Configuration loading từ environment
5. ✅ Event service layer implementation
6. ✅ Default handlers registration
7. ✅ Lifecycle management chính xác
8. ✅ Integration với core components
9. ✅ Dự án build thành công với `make build`

## Build Verification
```bash
# Kiểm tra build
make build

# Test module registration
go test ./modules/event/...
```
