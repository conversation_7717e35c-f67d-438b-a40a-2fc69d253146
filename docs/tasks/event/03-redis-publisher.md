# Task 03: Redis Publisher Implementation

## M<PERSON><PERSON> tiêu
<PERSON>ển khai Redis Streams publisher để gửi events đế<PERSON>, bao gồm connection management, batch publishing, error handling, và retry logic.

## Input/Output
**Input**: 
- Task 01 (Core Event Interfaces)
- Task 02 (Event Configuration)
**Output**: 
- `internal/pkg/events/publisher.go` - Publisher interface
- `internal/pkg/events/redis/publisher.go` - Redis publisher implementation
- Connection pooling và error handling

## Yêu cầu
1. Implement Publisher interface từ specification
2. Redis Streams integration với XADD commands
3. Batch publishing cho performance
4. Connection pooling và retry logic
5. Error handling và logging
6. Multi-tenant event routing

## Các bước thực hiện

### 1. Tạo file `internal/pkg/events/publisher.go`
```go
package events

import (
	"context"
)

// Publisher interface cho việc gửi events
type Publisher interface {
	// Publish gửi một event
	Publish(ctx context.Context, event Event) error
	
	// PublishBatch gửi nhiều events cùng lúc
	PublishBatch(ctx context.Context, events []Event) error
	
	// PublishAsync gửi event bất đồng bộ
	PublishAsync(ctx context.Context, event Event) <-chan error
	
	// Close đóng publisher connection
	Close() error
	
	// Health kiểm tra trạng thái publisher
	Health() error
}

// PublisherStats thống kê publisher
type PublisherStats struct {
	EventsPublished     int64 `json:"events_published"`
	EventsPublishFailed int64 `json:"events_publish_failed"`
	BatchesPublished    int64 `json:"batches_published"`
	ConnectionErrors    int64 `json:"connection_errors"`
	RetryAttempts       int64 `json:"retry_attempts"`
}

// PublisherMetrics interface cho metrics collection
type PublisherMetrics interface {
	IncrementEventsPublished(eventType string)
	IncrementEventsPublishFailed(eventType string, reason string)
	IncrementBatchesPublished(batchSize int)
	IncrementConnectionErrors()
	IncrementRetryAttempts(eventType string)
	RecordPublishDuration(eventType string, duration float64)
}

// NullPublisher là implementation rỗng cho testing
type NullPublisher struct{}

func (p *NullPublisher) Publish(ctx context.Context, event Event) error {
	return nil
}

func (p *NullPublisher) PublishBatch(ctx context.Context, events []Event) error {
	return nil
}

func (p *NullPublisher) PublishAsync(ctx context.Context, event Event) <-chan error {
	ch := make(chan error, 1)
	ch <- nil
	close(ch)
	return ch
}

func (p *NullPublisher) Close() error {
	return nil
}

func (p *NullPublisher) Health() error {
	return nil
}
```

### 2. Tạo file `internal/pkg/events/redis/publisher.go`
```go
package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"sync/atomic"
	"time"
	
	"wnapi/internal/pkg/events"
	"wnapi/internal/pkg/logger"
	
	"github.com/go-redis/redis/v8"
)

// RedisPublisher implements Publisher interface using Redis Streams
type RedisPublisher struct {
	client   *redis.Client
	config   events.Config
	logger   logger.Logger
	metrics  events.PublisherMetrics
	stats    *PublisherStatsCollector
	
	// Batch processing
	batchCh    chan events.Event
	batchWg    sync.WaitGroup
	stopCh     chan struct{}
	closed     int32
	
	// Connection management
	connMutex  sync.RWMutex
	connected  bool
}

// PublisherStatsCollector thu thập thống kê
type PublisherStatsCollector struct {
	eventsPublished     int64
	eventsPublishFailed int64
	batchesPublished    int64
	connectionErrors    int64
	retryAttempts       int64
}

// NewRedisPublisher tạo Redis publisher mới
func NewRedisPublisher(config events.Config, logger logger.Logger, metrics events.PublisherMetrics) (*RedisPublisher, error) {
	if !config.Enabled {
		return nil, fmt.Errorf("event system is disabled")
	}
	
	// Tạo Redis client
	client := redis.NewClient(&redis.Options{
		Addr:         config.GetRedisAddr(),
		Password:     config.Redis.Password,
		DB:           config.Redis.DB,
		PoolSize:     config.Redis.PoolSize,
		DialTimeout:  config.Redis.Timeout,
		ReadTimeout:  config.Redis.Timeout,
		WriteTimeout: config.Redis.Timeout,
	})
	
	publisher := &RedisPublisher{
		client:   client,
		config:   config,
		logger:   logger,
		metrics:  metrics,
		stats:    &PublisherStatsCollector{},
		batchCh:  make(chan events.Event, config.Publisher.BufferSize),
		stopCh:   make(chan struct{}),
		connected: false,
	}
	
	// Test connection
	if err := publisher.testConnection(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}
	
	// Start batch processor
	publisher.startBatchProcessor()
	
	return publisher, nil
}

// Publish gửi một event đến Redis Streams
func (p *RedisPublisher) Publish(ctx context.Context, event events.Event) error {
	if atomic.LoadInt32(&p.closed) == 1 {
		return fmt.Errorf("publisher is closed")
	}
	
	// Serialize event
	eventData, err := p.serializeEvent(event)
	if err != nil {
		p.incrementPublishFailed(event.Type(), "serialization_error")
		return fmt.Errorf("failed to serialize event: %w", err)
	}
	
	// Get stream name
	streamName := p.getStreamName(event.Type())
	
	// Publish to Redis with retry
	err = p.publishWithRetry(ctx, streamName, eventData)
	if err != nil {
		p.incrementPublishFailed(event.Type(), "publish_error")
		return err
	}
	
	p.incrementPublished(event.Type())
	return nil
}

// PublishBatch gửi nhiều events cùng lúc
func (p *RedisPublisher) PublishBatch(ctx context.Context, events []events.Event) error {
	if len(events) == 0 {
		return nil
	}
	
	if atomic.LoadInt32(&p.closed) == 1 {
		return fmt.Errorf("publisher is closed")
	}
	
	// Group events by stream
	streamEvents := make(map[string][]events.Event)
	for _, event := range events {
		streamName := p.getStreamName(event.Type())
		streamEvents[streamName] = append(streamEvents[streamName], event)
	}
	
	// Publish to each stream
	var publishErrors []error
	for streamName, streamEventList := range streamEvents {
		if err := p.publishBatchToStream(ctx, streamName, streamEventList); err != nil {
			publishErrors = append(publishErrors, err)
		}
	}
	
	if len(publishErrors) > 0 {
		return fmt.Errorf("batch publish failed: %v", publishErrors)
	}
	
	p.incrementBatchPublished(len(events))
	return nil
}

// PublishAsync gửi event bất đồng bộ
func (p *RedisPublisher) PublishAsync(ctx context.Context, event events.Event) <-chan error {
	errCh := make(chan error, 1)
	
	if atomic.LoadInt32(&p.closed) == 1 {
		errCh <- fmt.Errorf("publisher is closed")
		close(errCh)
		return errCh
	}
	
	// Add to batch channel
	select {
	case p.batchCh <- event:
		errCh <- nil
	case <-ctx.Done():
		errCh <- ctx.Err()
	default:
		errCh <- fmt.Errorf("publisher buffer is full")
	}
	
	close(errCh)
	return errCh
}

// Close đóng publisher
func (p *RedisPublisher) Close() error {
	if !atomic.CompareAndSwapInt32(&p.closed, 0, 1) {
		return nil // Already closed
	}
	
	// Stop batch processor
	close(p.stopCh)
	p.batchWg.Wait()
	
	// Close Redis client
	if err := p.client.Close(); err != nil {
		p.logger.Error("Failed to close Redis client", "error", err)
		return err
	}
	
	p.logger.Info("Redis publisher closed")
	return nil
}

// Health kiểm tra trạng thái publisher
func (p *RedisPublisher) Health() error {
	return p.testConnection()
}

// Private methods

func (p *RedisPublisher) testConnection() error {
	ctx, cancel := context.WithTimeout(context.Background(), p.config.Redis.Timeout)
	defer cancel()
	
	if err := p.client.Ping(ctx).Err(); err != nil {
		p.connMutex.Lock()
		p.connected = false
		p.connMutex.Unlock()
		
		atomic.AddInt64(&p.stats.connectionErrors, 1)
		if p.metrics != nil {
			p.metrics.IncrementConnectionErrors()
		}
		
		return fmt.Errorf("Redis connection failed: %w", err)
	}
	
	p.connMutex.Lock()
	p.connected = true
	p.connMutex.Unlock()
	
	return nil
}

func (p *RedisPublisher) serializeEvent(event events.Event) (map[string]interface{}, error) {
	// Serialize payload
	payloadBytes, err := json.Marshal(event.Payload())
	if err != nil {
		return nil, err
	}
	
	// Serialize metadata
	metadataBytes, err := json.Marshal(event.Metadata().ToMap())
	if err != nil {
		return nil, err
	}
	
	return map[string]interface{}{
		"id":         event.ID(),
		"type":       event.Type(),
		"version":    event.Version(),
		"timestamp":  event.Timestamp().Unix(),
		"tenant_id":  event.TenantID(),
		"website_id": event.WebsiteID(),
		"payload":    string(payloadBytes),
		"metadata":   string(metadataBytes),
	}, nil
}

func (p *RedisPublisher) getStreamName(eventType string) string {
	return fmt.Sprintf("stream:%s", eventType)
}

func (p *RedisPublisher) publishWithRetry(ctx context.Context, streamName string, eventData map[string]interface{}) error {
	var lastErr error
	
	for attempt := 0; attempt <= p.config.Publisher.MaxRetries; attempt++ {
		if attempt > 0 {
			// Wait before retry
			backoff := time.Duration(attempt) * p.config.Retry.InitialInterval
			if backoff > p.config.Retry.MaxInterval {
				backoff = p.config.Retry.MaxInterval
			}
			
			select {
			case <-time.After(backoff):
			case <-ctx.Done():
				return ctx.Err()
			}
			
			atomic.AddInt64(&p.stats.retryAttempts, 1)
			if p.metrics != nil {
				p.metrics.IncrementRetryAttempts(eventData["type"].(string))
			}
		}
		
		// Try to publish
		_, err := p.client.XAdd(ctx, &redis.XAddArgs{
			Stream: streamName,
			Values: eventData,
		}).Result()
		
		if err == nil {
			return nil // Success
		}
		
		lastErr = err
		p.logger.Warn("Failed to publish event, retrying",
			"attempt", attempt+1,
			"max_retries", p.config.Publisher.MaxRetries,
			"error", err,
		)
	}
	
	return fmt.Errorf("failed to publish after %d attempts: %w", p.config.Publisher.MaxRetries+1, lastErr)
}

func (p *RedisPublisher) publishBatchToStream(ctx context.Context, streamName string, events []events.Event) error {
	pipe := p.client.Pipeline()
	
	for _, event := range events {
		eventData, err := p.serializeEvent(event)
		if err != nil {
			p.incrementPublishFailed(event.Type(), "serialization_error")
			continue
		}
		
		pipe.XAdd(ctx, &redis.XAddArgs{
			Stream: streamName,
			Values: eventData,
		})
	}
	
	_, err := pipe.Exec(ctx)
	if err != nil {
		for _, event := range events {
			p.incrementPublishFailed(event.Type(), "batch_publish_error")
		}
		return err
	}
	
	for _, event := range events {
		p.incrementPublished(event.Type())
	}
	
	return nil
}

func (p *RedisPublisher) startBatchProcessor() {
	p.batchWg.Add(1)
	go func() {
		defer p.batchWg.Done()
		
		ticker := time.NewTicker(p.config.Publisher.FlushInterval)
		defer ticker.Stop()
		
		batch := make([]events.Event, 0, p.config.Publisher.BatchSize)
		
		for {
			select {
			case event := <-p.batchCh:
				batch = append(batch, event)
				if len(batch) >= p.config.Publisher.BatchSize {
					p.processBatch(batch)
					batch = batch[:0] // Reset slice
				}
				
			case <-ticker.C:
				if len(batch) > 0 {
					p.processBatch(batch)
					batch = batch[:0] // Reset slice
				}
				
			case <-p.stopCh:
				// Process remaining events
				if len(batch) > 0 {
					p.processBatch(batch)
				}
				return
			}
		}
	}()
}

func (p *RedisPublisher) processBatch(events []events.Event) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := p.PublishBatch(ctx, events); err != nil {
		p.logger.Error("Failed to process event batch", "error", err, "batch_size", len(events))
	}
}

// Metrics helpers
func (p *RedisPublisher) incrementPublished(eventType string) {
	atomic.AddInt64(&p.stats.eventsPublished, 1)
	if p.metrics != nil {
		p.metrics.IncrementEventsPublished(eventType)
	}
}

func (p *RedisPublisher) incrementPublishFailed(eventType, reason string) {
	atomic.AddInt64(&p.stats.eventsPublishFailed, 1)
	if p.metrics != nil {
		p.metrics.IncrementEventsPublishFailed(eventType, reason)
	}
}

func (p *RedisPublisher) incrementBatchPublished(batchSize int) {
	atomic.AddInt64(&p.stats.batchesPublished, 1)
	if p.metrics != nil {
		p.metrics.IncrementBatchesPublished(batchSize)
	}
}

// GetStats trả về thống kê publisher
func (p *RedisPublisher) GetStats() events.PublisherStats {
	return events.PublisherStats{
		EventsPublished:     atomic.LoadInt64(&p.stats.eventsPublished),
		EventsPublishFailed: atomic.LoadInt64(&p.stats.eventsPublishFailed),
		BatchesPublished:    atomic.LoadInt64(&p.stats.batchesPublished),
		ConnectionErrors:    atomic.LoadInt64(&p.stats.connectionErrors),
		RetryAttempts:       atomic.LoadInt64(&p.stats.retryAttempts),
	}
}
```

## Kết quả mong đợi
- Publisher interface được định nghĩa đầy đủ
- Redis Streams publisher implementation hoàn chỉnh
- Batch publishing cho performance tối ưu
- Connection pooling và error handling
- Retry logic với exponential backoff
- Metrics collection và monitoring
- Dự án vẫn biên dịch được

## Thời gian dự kiến
2 giờ làm việc

## Độ ưu tiên
Cao (Core component cho event publishing)

## Phụ thuộc
- Task 01: Core Event Interfaces
- Task 02: Event Configuration

## Acceptance Criteria
1. ✅ Publisher interface implementation đầy đủ
2. ✅ Redis Streams integration hoạt động
3. ✅ Batch publishing performance tốt
4. ✅ Connection management và retry logic
5. ✅ Error handling và logging comprehensive
6. ✅ Metrics collection hoạt động
7. ✅ Dự án build thành công với `make build`

## Build Verification
```bash
# Kiểm tra build
make build

# Test Redis connection (cần Redis running)
go test ./internal/pkg/events/redis/...
```
