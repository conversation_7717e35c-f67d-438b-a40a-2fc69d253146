# Task 01: Core Event Interfaces and Types

## Mụ<PERSON> tiêu
Tạo các interface và type c<PERSON> bản cho hệ thống event, bao gồm Event interface, EventMetadata, và các struct cơ bản để làm nền tảng cho toàn bộ event system.

## Input/Output
**Input**: Event system specification từ docs/event.md
**Output**: 
- `internal/pkg/events/event.go` - Core event interfaces
- `internal/pkg/events/metadata.go` - Event metadata implementation
- `internal/pkg/events/types.go` - Basic event types

## Yêu cầu
1. Tuân thủ multi-tenant architecture với tenantID và websiteID
2. Hỗ trợ event versioning và schema evolution
3. Include correlation ID và trace ID cho distributed tracing
4. Implement event metadata với thread-safe operations
5. Sử dụng INT UNSIGNED cho tất cả ID fields

## Các bước thực hiện

### 1. <PERSON><PERSON><PERSON> <PERSON><PERSON> mụ<PERSON> c<PERSON> bản
```bash
mkdir -p internal/pkg/events
mkdir -p internal/pkg/events/redis
mkdir -p internal/pkg/events/types
mkdir -p internal/pkg/events/testing
```

### 2. Tạo file `internal/pkg/events/event.go`
```go
package events

import (
	"time"
)

// Event là interface chính cho tất cả events trong hệ thống
type Event interface {
	// ID trả về unique identifier của event
	ID() string
	
	// Type trả về loại event (ví dụ: "auth.user.created")
	Type() string
	
	// Payload trả về dữ liệu của event
	Payload() interface{}
	
	// Metadata trả về thông tin metadata
	Metadata() EventMetadata
	
	// TenantID trả về tenant ID (multi-tenant support)
	TenantID() uint
	
	// WebsiteID trả về website ID (multi-website support)  
	WebsiteID() uint
	
	// Timestamp trả về thời gian tạo event
	Timestamp() time.Time
	
	// Version trả về schema version của event
	Version() string
}

// EventMetadata interface cho việc quản lý metadata
type EventMetadata interface {
	// Get lấy giá trị metadata theo key
	Get(key string) (interface{}, bool)
	
	// Set thiết lập giá trị metadata
	Set(key string, value interface{})
	
	// ToMap trả về tất cả metadata dưới dạng map
	ToMap() map[string]interface{}
	
	// CorrelationID trả về correlation ID cho request tracking
	CorrelationID() string
	
	// TraceID trả về trace ID cho distributed tracing
	TraceID() string
	
	// Source trả về service/module tạo ra event
	Source() string
	
	// UserID trả về ID user trigger event (nếu có)
	UserID() *uint
}

// BaseEvent là implementation cơ bản của Event interface
type BaseEvent struct {
	EventID     string                 `json:"event_id"`
	EventType   string                 `json:"event_type"`
	EventVersion string                `json:"event_version"`
	Timestamp   time.Time              `json:"timestamp"`
	TenantID    uint                   `json:"tenant_id,omitempty"`
	WebsiteID   uint                   `json:"website_id,omitempty"`
	Metadata    EventMetadata          `json:"metadata"`
	Payload     interface{}            `json:"payload"`
}

// Implement Event interface
func (e *BaseEvent) ID() string { return e.EventID }
func (e *BaseEvent) Type() string { return e.EventType }
func (e *BaseEvent) Payload() interface{} { return e.Payload }
func (e *BaseEvent) Metadata() EventMetadata { return e.Metadata }
func (e *BaseEvent) TenantID() uint { return e.TenantID }
func (e *BaseEvent) WebsiteID() uint { return e.WebsiteID }
func (e *BaseEvent) Timestamp() time.Time { return e.Timestamp }
func (e *BaseEvent) Version() string { return e.EventVersion }
```

### 3. Tạo file `internal/pkg/events/metadata.go`
```go
package events

import (
	"sync"
)

// StandardMetadata là implementation thread-safe của EventMetadata
type StandardMetadata struct {
	data          map[string]interface{}
	correlationID string
	traceID       string
	source        string
	userID        *uint
	mutex         sync.RWMutex
}

// NewStandardMetadata tạo metadata mới
func NewStandardMetadata() *StandardMetadata {
	return &StandardMetadata{
		data: make(map[string]interface{}),
	}
}

// Get lấy giá trị metadata theo key (thread-safe)
func (m *StandardMetadata) Get(key string) (interface{}, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	val, exists := m.data[key]
	return val, exists
}

// Set thiết lập giá trị metadata (thread-safe)
func (m *StandardMetadata) Set(key string, value interface{}) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.data[key] = value
}

// ToMap trả về copy của tất cả metadata
func (m *StandardMetadata) ToMap() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	result := make(map[string]interface{})
	for k, v := range m.data {
		result[k] = v
	}
	return result
}

// CorrelationID trả về correlation ID
func (m *StandardMetadata) CorrelationID() string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.correlationID
}

// SetCorrelationID thiết lập correlation ID
func (m *StandardMetadata) SetCorrelationID(id string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.correlationID = id
}

// TraceID trả về trace ID
func (m *StandardMetadata) TraceID() string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.traceID
}

// SetTraceID thiết lập trace ID
func (m *StandardMetadata) SetTraceID(id string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.traceID = id
}

// Source trả về source service/module
func (m *StandardMetadata) Source() string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.source
}

// SetSource thiết lập source
func (m *StandardMetadata) SetSource(source string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.source = source
}

// UserID trả về user ID
func (m *StandardMetadata) UserID() *uint {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.userID
}

// SetUserID thiết lập user ID
func (m *StandardMetadata) SetUserID(userID *uint) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.userID = userID
}
```

### 4. Tạo file `internal/pkg/events/types.go`
```go
package events

import (
	"time"
	"github.com/google/uuid"
)

// NewBaseEvent tạo một BaseEvent mới với các giá trị mặc định
func NewBaseEvent(eventType string, payload interface{}) *BaseEvent {
	return &BaseEvent{
		EventID:      uuid.New().String(),
		EventType:    eventType,
		EventVersion: "1.0",
		Timestamp:    time.Now(),
		Metadata:     NewStandardMetadata(),
		Payload:      payload,
	}
}

// NewBaseEventWithTenant tạo BaseEvent với tenant context
func NewBaseEventWithTenant(eventType string, payload interface{}, tenantID uint) *BaseEvent {
	event := NewBaseEvent(eventType, payload)
	event.TenantID = tenantID
	return event
}

// NewBaseEventWithContext tạo BaseEvent với đầy đủ context
func NewBaseEventWithContext(eventType string, payload interface{}, tenantID, websiteID uint) *BaseEvent {
	event := NewBaseEvent(eventType, payload)
	event.TenantID = tenantID
	event.WebsiteID = websiteID
	return event
}

// EventBuilder là helper để xây dựng events một cách fluent
type EventBuilder struct {
	event *BaseEvent
}

// NewEventBuilder tạo builder mới
func NewEventBuilder(eventType string) *EventBuilder {
	return &EventBuilder{
		event: NewBaseEvent(eventType, nil),
	}
}

// WithPayload thiết lập payload
func (b *EventBuilder) WithPayload(payload interface{}) *EventBuilder {
	b.event.Payload = payload
	return b
}

// WithTenant thiết lập tenant ID
func (b *EventBuilder) WithTenant(tenantID uint) *EventBuilder {
	b.event.TenantID = tenantID
	return b
}

// WithWebsite thiết lập website ID
func (b *EventBuilder) WithWebsite(websiteID uint) *EventBuilder {
	b.event.WebsiteID = websiteID
	return b
}

// WithCorrelationID thiết lập correlation ID
func (b *EventBuilder) WithCorrelationID(correlationID string) *EventBuilder {
	b.event.Metadata.SetCorrelationID(correlationID)
	return b
}

// WithTraceID thiết lập trace ID
func (b *EventBuilder) WithTraceID(traceID string) *EventBuilder {
	b.event.Metadata.SetTraceID(traceID)
	return b
}

// WithSource thiết lập source
func (b *EventBuilder) WithSource(source string) *EventBuilder {
	b.event.Metadata.SetSource(source)
	return b
}

// WithUserID thiết lập user ID
func (b *EventBuilder) WithUserID(userID uint) *EventBuilder {
	b.event.Metadata.SetUserID(&userID)
	return b
}

// Build tạo ra event cuối cùng
func (b *EventBuilder) Build() Event {
	return b.event
}
```

## Kết quả mong đợi
- Package `internal/pkg/events` được tạo với các interface cơ bản
- Event interface và EventMetadata interface được định nghĩa đầy đủ
- BaseEvent implementation với thread-safe metadata
- EventBuilder pattern để tạo events dễ dàng
- Hỗ trợ multi-tenant và multi-website architecture
- Dự án vẫn biên dịch được sau khi thêm package này

## Thời gian dự kiến
1-2 giờ làm việc

## Độ ưu tiên
Cao (Foundation task cho toàn bộ event system)

## Phụ thuộc
Không có (đây là task đầu tiên)

## Acceptance Criteria
1. ✅ Tất cả interfaces được định nghĩa theo specification
2. ✅ BaseEvent implementation hoạt động chính xác
3. ✅ Metadata operations là thread-safe
4. ✅ EventBuilder pattern hoạt động fluently
5. ✅ Multi-tenant support được tích hợp
6. ✅ Dự án build thành công với `make build`

## Build Verification
```bash
# Kiểm tra build
make build

# Kiểm tra import
go mod tidy
```
