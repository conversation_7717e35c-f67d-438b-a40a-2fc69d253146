# Task 04: Redis Subscriber Implementation

## M<PERSON><PERSON> tiêu
<PERSON>ể<PERSON> khai Redis Streams subscriber đ<PERSON> nhận và xử lý events từ Redis, bao gồm consumer groups, worker management, acknowledgment handling, và error recovery.

## Input/Output
**Input**: 
- Task 01 (Core Event Interfaces)
- Task 02 (Event Configuration)
- Task 03 (Redis Publisher)
**Output**: 
- `internal/pkg/events/subscriber.go` - Subscriber interface
- `internal/pkg/events/redis/subscriber.go` - Redis subscriber implementation
- Consumer group management và worker pools

## Yêu cầu
1. Implement Subscriber interface từ specification
2. Redis Streams consumer groups với XREADGROUP
3. Worker pool cho parallel processing
4. Automatic acknowledgment và error handling
5. Event deserialization và validation
6. Multi-tenant event filtering

## C<PERSON><PERSON> b<PERSON>ớc thực hiện

### 1. Tạo file `internal/pkg/events/subscriber.go`
```go
package events

import (
	"context"
)

// Subscriber interface cho việc nhận events
type Subscriber interface {
	// Subscribe đăng ký lắng nghe một loại event
	Subscribe(ctx context.Context, eventType string, handler EventHandler) error
	
	// SubscribeMultiple đăng ký lắng nghe nhiều loại events
	SubscribeMultiple(ctx context.Context, eventTypes []string, handler EventHandler) error
	
	// SubscribePattern đăng ký theo pattern (ví dụ: "auth.*")
	SubscribePattern(ctx context.Context, pattern string, handler EventHandler) error
	
	// Unsubscribe hủy đăng ký
	Unsubscribe(eventType string) error
	
	// Start bắt đầu lắng nghe events
	Start(ctx context.Context) error
	
	// Stop dừng lắng nghe events
	Stop(ctx context.Context) error
	
	// Close đóng subscriber connection
	Close() error
	
	// Health kiểm tra trạng thái subscriber
	Health() error
}

// EventHandler interface cho việc xử lý events
type EventHandler interface {
	// Handle xử lý event
	Handle(ctx context.Context, event Event) error
	
	// CanHandle kiểm tra có thể xử lý event type này không
	CanHandle(eventType string) bool
	
	// RetryPolicy trả về retry policy cho handler này
	RetryPolicy() RetryPolicy
	
	// Name trả về tên của handler (cho monitoring)
	Name() string
	
	// Priority trả về độ ưu tiên xử lý (optional)
	Priority() int
}

// RetryPolicy định nghĩa chính sách retry
type RetryPolicy interface {
	// ShouldRetry kiểm tra có nên retry không
	ShouldRetry(attempt int, err error) bool
	
	// NextInterval trả về thời gian chờ cho lần retry tiếp theo
	NextInterval(attempt int) time.Duration
	
	// MaxAttempts trả về số lần retry tối đa
	MaxAttempts() int
}

// SubscriberStats thống kê subscriber
type SubscriberStats struct {
	EventsReceived      int64 `json:"events_received"`
	EventsProcessed     int64 `json:"events_processed"`
	EventsProcessFailed int64 `json:"events_process_failed"`
	HandlersRegistered  int64 `json:"handlers_registered"`
	WorkersActive       int64 `json:"workers_active"`
	ConnectionErrors    int64 `json:"connection_errors"`
}

// SubscriberMetrics interface cho metrics collection
type SubscriberMetrics interface {
	IncrementEventsReceived(eventType string)
	IncrementEventsProcessed(eventType string)
	IncrementEventsProcessFailed(eventType string, reason string)
	IncrementConnectionErrors()
	RecordProcessDuration(eventType string, duration float64)
	SetActiveWorkers(count int64)
	SetRegisteredHandlers(count int64)
}

// BaseEventHandler là implementation cơ bản của EventHandler
type BaseEventHandler struct {
	name         string
	eventTypes   []string
	retryPolicy  RetryPolicy
	priority     int
	handlerFunc  func(ctx context.Context, event Event) error
}

func NewBaseEventHandler(name string, eventTypes []string, handlerFunc func(ctx context.Context, event Event) error) *BaseEventHandler {
	return &BaseEventHandler{
		name:        name,
		eventTypes:  eventTypes,
		retryPolicy: NewDefaultRetryPolicy(),
		priority:    0,
		handlerFunc: handlerFunc,
	}
}

func (h *BaseEventHandler) Handle(ctx context.Context, event Event) error {
	return h.handlerFunc(ctx, event)
}

func (h *BaseEventHandler) CanHandle(eventType string) bool {
	for _, et := range h.eventTypes {
		if et == eventType || et == "*" {
			return true
		}
	}
	return false
}

func (h *BaseEventHandler) RetryPolicy() RetryPolicy {
	return h.retryPolicy
}

func (h *BaseEventHandler) Name() string {
	return h.name
}

func (h *BaseEventHandler) Priority() int {
	return h.priority
}

func (h *BaseEventHandler) SetRetryPolicy(policy RetryPolicy) {
	h.retryPolicy = policy
}

func (h *BaseEventHandler) SetPriority(priority int) {
	h.priority = priority
}
```

### 2. Tạo file `internal/pkg/events/redis/subscriber.go`
```go
package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"sync"
	"sync/atomic"
	"time"
	
	"wnapi/internal/pkg/events"
	"wnapi/internal/pkg/logger"
	
	"github.com/go-redis/redis/v8"
)

// RedisSubscriber implements Subscriber interface using Redis Streams
type RedisSubscriber struct {
	client   *redis.Client
	config   events.Config
	logger   logger.Logger
	metrics  events.SubscriberMetrics
	stats    *SubscriberStatsCollector
	
	// Handler management
	handlers     map[string][]events.EventHandler
	handlerMutex sync.RWMutex
	
	// Worker management
	workers    []*worker
	workerWg   sync.WaitGroup
	stopCh     chan struct{}
	started    int32
	closed     int32
	
	// Consumer group management
	consumerGroup string
	consumerName  string
}

// SubscriberStatsCollector thu thập thống kê
type SubscriberStatsCollector struct {
	eventsReceived      int64
	eventsProcessed     int64
	eventsProcessFailed int64
	handlersRegistered  int64
	workersActive       int64
	connectionErrors    int64
}

// worker xử lý events trong một goroutine riêng
type worker struct {
	id         int
	subscriber *RedisSubscriber
	stopCh     chan struct{}
	logger     logger.Logger
}

// NewRedisSubscriber tạo Redis subscriber mới
func NewRedisSubscriber(config events.Config, logger logger.Logger, metrics events.SubscriberMetrics) (*RedisSubscriber, error) {
	if !config.Enabled {
		return nil, fmt.Errorf("event system is disabled")
	}
	
	// Tạo Redis client
	client := redis.NewClient(&redis.Options{
		Addr:         config.GetRedisAddr(),
		Password:     config.Redis.Password,
		DB:           config.Redis.DB,
		PoolSize:     config.Redis.PoolSize,
		DialTimeout:  config.Redis.Timeout,
		ReadTimeout:  config.Redis.Timeout,
		WriteTimeout: config.Redis.Timeout,
	})
	
	subscriber := &RedisSubscriber{
		client:        client,
		config:        config,
		logger:        logger,
		metrics:       metrics,
		stats:         &SubscriberStatsCollector{},
		handlers:      make(map[string][]events.EventHandler),
		stopCh:        make(chan struct{}),
		consumerGroup: config.Subscriber.ConsumerGroup,
		consumerName:  fmt.Sprintf("%s_%d", config.Subscriber.ConsumerGroup, time.Now().Unix()),
	}
	
	// Test connection
	if err := subscriber.testConnection(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}
	
	return subscriber, nil
}

// Subscribe đăng ký lắng nghe một loại event
func (s *RedisSubscriber) Subscribe(ctx context.Context, eventType string, handler events.EventHandler) error {
	s.handlerMutex.Lock()
	defer s.handlerMutex.Unlock()
	
	if !handler.CanHandle(eventType) {
		return fmt.Errorf("handler cannot handle event type: %s", eventType)
	}
	
	s.handlers[eventType] = append(s.handlers[eventType], handler)
	atomic.AddInt64(&s.stats.handlersRegistered, 1)
	
	if s.metrics != nil {
		s.metrics.SetRegisteredHandlers(atomic.LoadInt64(&s.stats.handlersRegistered))
	}
	
	s.logger.Info("Event handler registered",
		"event_type", eventType,
		"handler_name", handler.Name(),
	)
	
	// Ensure consumer group exists for this stream
	streamName := s.getStreamName(eventType)
	if err := s.ensureConsumerGroup(ctx, streamName); err != nil {
		s.logger.Warn("Failed to create consumer group",
			"stream", streamName,
			"error", err,
		)
	}
	
	return nil
}

// SubscribeMultiple đăng ký lắng nghe nhiều loại events
func (s *RedisSubscriber) SubscribeMultiple(ctx context.Context, eventTypes []string, handler events.EventHandler) error {
	for _, eventType := range eventTypes {
		if err := s.Subscribe(ctx, eventType, handler); err != nil {
			return fmt.Errorf("failed to subscribe to %s: %w", eventType, err)
		}
	}
	return nil
}

// SubscribePattern đăng ký theo pattern (stub implementation)
func (s *RedisSubscriber) SubscribePattern(ctx context.Context, pattern string, handler events.EventHandler) error {
	// TODO: Implement pattern matching for Redis Streams
	return fmt.Errorf("pattern subscription not yet implemented")
}

// Unsubscribe hủy đăng ký
func (s *RedisSubscriber) Unsubscribe(eventType string) error {
	s.handlerMutex.Lock()
	defer s.handlerMutex.Unlock()
	
	if handlers, exists := s.handlers[eventType]; exists {
		atomic.AddInt64(&s.stats.handlersRegistered, -int64(len(handlers)))
		delete(s.handlers, eventType)
		
		if s.metrics != nil {
			s.metrics.SetRegisteredHandlers(atomic.LoadInt64(&s.stats.handlersRegistered))
		}
		
		s.logger.Info("Event handlers unregistered", "event_type", eventType)
	}
	
	return nil
}

// Start bắt đầu lắng nghe events
func (s *RedisSubscriber) Start(ctx context.Context) error {
	if !atomic.CompareAndSwapInt32(&s.started, 0, 1) {
		return fmt.Errorf("subscriber already started")
	}
	
	if atomic.LoadInt32(&s.closed) == 1 {
		return fmt.Errorf("subscriber is closed")
	}
	
	// Start workers
	s.workers = make([]*worker, s.config.Subscriber.WorkerCount)
	for i := 0; i < s.config.Subscriber.WorkerCount; i++ {
		worker := &worker{
			id:         i,
			subscriber: s,
			stopCh:     make(chan struct{}),
			logger:     s.logger,
		}
		s.workers[i] = worker
		
		s.workerWg.Add(1)
		go worker.run()
	}
	
	atomic.StoreInt64(&s.stats.workersActive, int64(s.config.Subscriber.WorkerCount))
	if s.metrics != nil {
		s.metrics.SetActiveWorkers(int64(s.config.Subscriber.WorkerCount))
	}
	
	s.logger.Info("Redis subscriber started",
		"worker_count", s.config.Subscriber.WorkerCount,
		"consumer_group", s.consumerGroup,
	)
	
	return nil
}

// Stop dừng lắng nghe events
func (s *RedisSubscriber) Stop(ctx context.Context) error {
	if !atomic.CompareAndSwapInt32(&s.started, 1, 0) {
		return nil // Already stopped
	}
	
	// Stop all workers
	for _, worker := range s.workers {
		close(worker.stopCh)
	}
	
	// Wait for workers to finish
	done := make(chan struct{})
	go func() {
		s.workerWg.Wait()
		close(done)
	}()
	
	select {
	case <-done:
		s.logger.Info("All workers stopped")
	case <-ctx.Done():
		s.logger.Warn("Timeout waiting for workers to stop")
		return ctx.Err()
	}
	
	atomic.StoreInt64(&s.stats.workersActive, 0)
	if s.metrics != nil {
		s.metrics.SetActiveWorkers(0)
	}
	
	return nil
}

// Close đóng subscriber
func (s *RedisSubscriber) Close() error {
	if !atomic.CompareAndSwapInt32(&s.closed, 0, 1) {
		return nil // Already closed
	}
	
	// Stop if running
	if atomic.LoadInt32(&s.started) == 1 {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()
		s.Stop(ctx)
	}
	
	// Close Redis client
	if err := s.client.Close(); err != nil {
		s.logger.Error("Failed to close Redis client", "error", err)
		return err
	}
	
	s.logger.Info("Redis subscriber closed")
	return nil
}

// Health kiểm tra trạng thái subscriber
func (s *RedisSubscriber) Health() error {
	return s.testConnection()
}

// Private methods

func (s *RedisSubscriber) testConnection() error {
	ctx, cancel := context.WithTimeout(context.Background(), s.config.Redis.Timeout)
	defer cancel()
	
	if err := s.client.Ping(ctx).Err(); err != nil {
		atomic.AddInt64(&s.stats.connectionErrors, 1)
		if s.metrics != nil {
			s.metrics.IncrementConnectionErrors()
		}
		return fmt.Errorf("Redis connection failed: %w", err)
	}
	
	return nil
}

func (s *RedisSubscriber) getStreamName(eventType string) string {
	return fmt.Sprintf("stream:%s", eventType)
}

func (s *RedisSubscriber) ensureConsumerGroup(ctx context.Context, streamName string) error {
	// Try to create consumer group (ignore error if already exists)
	err := s.client.XGroupCreateMkStream(ctx, streamName, s.consumerGroup, "0").Err()
	if err != nil && err.Error() != "BUSYGROUP Consumer Group name already exists" {
		return err
	}
	return nil
}

func (s *RedisSubscriber) getHandlers(eventType string) []events.EventHandler {
	s.handlerMutex.RLock()
	defer s.handlerMutex.RUnlock()
	
	handlers := make([]events.EventHandler, 0)
	
	// Get specific handlers
	if eventHandlers, exists := s.handlers[eventType]; exists {
		handlers = append(handlers, eventHandlers...)
	}
	
	// Get wildcard handlers
	if wildcardHandlers, exists := s.handlers["*"]; exists {
		handlers = append(handlers, wildcardHandlers...)
	}
	
	// Sort by priority (higher priority first)
	sort.Slice(handlers, func(i, j int) bool {
		return handlers[i].Priority() > handlers[j].Priority()
	})
	
	return handlers
}

func (s *RedisSubscriber) deserializeEvent(streamData map[string]interface{}) (events.Event, error) {
	// Extract basic fields
	eventID, _ := streamData["id"].(string)
	eventType, _ := streamData["type"].(string)
	version, _ := streamData["version"].(string)
	timestampStr, _ := streamData["timestamp"].(string)
	tenantIDStr, _ := streamData["tenant_id"].(string)
	websiteIDStr, _ := streamData["website_id"].(string)
	payloadStr, _ := streamData["payload"].(string)
	metadataStr, _ := streamData["metadata"].(string)
	
	// Parse timestamp
	timestamp := time.Now()
	if timestampStr != "" {
		if ts, err := time.Parse(time.RFC3339, timestampStr); err == nil {
			timestamp = ts
		}
	}
	
	// Parse tenant and website IDs
	var tenantID, websiteID uint
	if tenantIDStr != "" {
		if id, err := strconv.ParseUint(tenantIDStr, 10, 32); err == nil {
			tenantID = uint(id)
		}
	}
	if websiteIDStr != "" {
		if id, err := strconv.ParseUint(websiteIDStr, 10, 32); err == nil {
			websiteID = uint(id)
		}
	}
	
	// Parse payload
	var payload interface{}
	if payloadStr != "" {
		if err := json.Unmarshal([]byte(payloadStr), &payload); err != nil {
			return nil, fmt.Errorf("failed to unmarshal payload: %w", err)
		}
	}
	
	// Parse metadata
	metadata := events.NewStandardMetadata()
	if metadataStr != "" {
		var metadataMap map[string]interface{}
		if err := json.Unmarshal([]byte(metadataStr), &metadataMap); err == nil {
			for k, v := range metadataMap {
				metadata.Set(k, v)
			}
		}
	}
	
	// Create event
	event := &events.BaseEvent{
		EventID:      eventID,
		EventType:    eventType,
		EventVersion: version,
		Timestamp:    timestamp,
		TenantID:     tenantID,
		WebsiteID:    websiteID,
		Metadata:     metadata,
		Payload:      payload,
	}
	
	return event, nil
}

// Worker implementation

func (w *worker) run() {
	defer w.subscriber.workerWg.Done()
	
	w.logger.Debug("Worker started", "worker_id", w.id)
	defer w.logger.Debug("Worker stopped", "worker_id", w.id)
	
	for {
		select {
		case <-w.stopCh:
			return
		default:
			w.processEvents()
		}
	}
}

func (w *worker) processEvents() {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	// Get all registered event types
	w.subscriber.handlerMutex.RLock()
	eventTypes := make([]string, 0, len(w.subscriber.handlers))
	for eventType := range w.subscriber.handlers {
		if eventType != "*" { // Skip wildcard
			eventTypes = append(eventTypes, eventType)
		}
	}
	w.subscriber.handlerMutex.RUnlock()
	
	if len(eventTypes) == 0 {
		time.Sleep(1 * time.Second)
		return
	}
	
	// Build streams for XREADGROUP
	streams := make([]string, 0, len(eventTypes)*2)
	for _, eventType := range eventTypes {
		streamName := w.subscriber.getStreamName(eventType)
		streams = append(streams, streamName, ">")
	}
	
	// Read from streams
	result, err := w.subscriber.client.XReadGroup(ctx, &redis.XReadGroupArgs{
		Group:    w.subscriber.consumerGroup,
		Consumer: fmt.Sprintf("%s_%d", w.subscriber.consumerName, w.id),
		Streams:  streams,
		Count:    int64(w.subscriber.config.Subscriber.BatchSize),
		Block:    w.subscriber.config.Subscriber.AckTimeout,
	}).Result()
	
	if err != nil {
		if err != redis.Nil {
			w.logger.Error("Failed to read from streams", "error", err, "worker_id", w.id)
		}
		return
	}
	
	// Process messages
	for _, stream := range result {
		for _, message := range stream.Messages {
			w.processMessage(ctx, stream.Stream, message)
		}
	}
}

func (w *worker) processMessage(ctx context.Context, streamName string, message redis.XMessage) {
	// Deserialize event
	event, err := w.subscriber.deserializeEvent(message.Values)
	if err != nil {
		w.logger.Error("Failed to deserialize event",
			"error", err,
			"stream", streamName,
			"message_id", message.ID,
		)
		// Acknowledge failed message to avoid reprocessing
		w.acknowledgeMessage(ctx, streamName, message.ID)
		return
	}
	
	atomic.AddInt64(&w.subscriber.stats.eventsReceived, 1)
	if w.subscriber.metrics != nil {
		w.subscriber.metrics.IncrementEventsReceived(event.Type())
	}
	
	// Get handlers for this event type
	handlers := w.subscriber.getHandlers(event.Type())
	if len(handlers) == 0 {
		w.logger.Debug("No handlers for event type", "event_type", event.Type())
		w.acknowledgeMessage(ctx, streamName, message.ID)
		return
	}
	
	// Process with each handler
	processed := false
	for _, handler := range handlers {
		if err := w.processWithHandler(ctx, event, handler); err != nil {
			w.logger.Error("Handler failed to process event",
				"error", err,
				"handler", handler.Name(),
				"event_type", event.Type(),
				"event_id", event.ID(),
			)
			
			atomic.AddInt64(&w.subscriber.stats.eventsProcessFailed, 1)
			if w.subscriber.metrics != nil {
				w.subscriber.metrics.IncrementEventsProcessFailed(event.Type(), err.Error())
			}
		} else {
			processed = true
			atomic.AddInt64(&w.subscriber.stats.eventsProcessed, 1)
			if w.subscriber.metrics != nil {
				w.subscriber.metrics.IncrementEventsProcessed(event.Type())
			}
		}
	}
	
	// Acknowledge message if at least one handler succeeded
	if processed {
		w.acknowledgeMessage(ctx, streamName, message.ID)
	}
}

func (w *worker) processWithHandler(ctx context.Context, event events.Event, handler events.EventHandler) error {
	start := time.Now()
	defer func() {
		duration := time.Since(start).Seconds()
		if w.subscriber.metrics != nil {
			w.subscriber.metrics.RecordProcessDuration(event.Type(), duration)
		}
	}()
	
	// Process with retry policy
	retryPolicy := handler.RetryPolicy()
	var lastErr error
	
	for attempt := 0; attempt < retryPolicy.MaxAttempts(); attempt++ {
		if attempt > 0 {
			// Wait before retry
			interval := retryPolicy.NextInterval(attempt)
			select {
			case <-time.After(interval):
			case <-ctx.Done():
				return ctx.Err()
			}
		}
		
		// Try to handle event
		if err := handler.Handle(ctx, event); err != nil {
			lastErr = err
			if !retryPolicy.ShouldRetry(attempt, err) {
				break
			}
			continue
		}
		
		return nil // Success
	}
	
	return lastErr
}

func (w *worker) acknowledgeMessage(ctx context.Context, streamName, messageID string) {
	err := w.subscriber.client.XAck(ctx, streamName, w.subscriber.consumerGroup, messageID).Err()
	if err != nil {
		w.logger.Error("Failed to acknowledge message",
			"error", err,
			"stream", streamName,
			"message_id", messageID,
		)
	}
}

// GetStats trả về thống kê subscriber
func (s *RedisSubscriber) GetStats() events.SubscriberStats {
	return events.SubscriberStats{
		EventsReceived:      atomic.LoadInt64(&s.stats.eventsReceived),
		EventsProcessed:     atomic.LoadInt64(&s.stats.eventsProcessed),
		EventsProcessFailed: atomic.LoadInt64(&s.stats.eventsProcessFailed),
		HandlersRegistered:  atomic.LoadInt64(&s.stats.handlersRegistered),
		WorkersActive:       atomic.LoadInt64(&s.stats.workersActive),
		ConnectionErrors:    atomic.LoadInt64(&s.stats.connectionErrors),
	}
}
```

## Kết quả mong đợi
- Subscriber interface được định nghĩa đầy đủ
- Redis Streams subscriber implementation hoàn chỉnh
- Consumer group management và worker pools
- Event handler framework với retry policies
- Automatic acknowledgment và error handling
- Event deserialization và validation
- Metrics collection và monitoring
- Dự án vẫn biên dịch được

## Thời gian dự kiến
2 giờ làm việc

## Độ ưu tiên
Cao (Core component cho event processing)

## Phụ thuộc
- Task 01: Core Event Interfaces
- Task 02: Event Configuration
- Task 03: Redis Publisher

## Acceptance Criteria
1. ✅ Subscriber interface implementation đầy đủ
2. ✅ Redis Streams consumer groups hoạt động
3. ✅ Worker pool processing hiệu quả
4. ✅ Event handler framework với retry logic
5. ✅ Acknowledgment và error handling
6. ✅ Event deserialization chính xác
7. ✅ Metrics collection hoạt động
8. ✅ Dự án build thành công với `make build`

## Build Verification
```bash
# Kiểm tra build
make build

# Test Redis subscriber (cần Redis running)
go test ./internal/pkg/events/redis/...
```
