# Task 05: Event Bus Implementation

## M<PERSON><PERSON> tiêu
Triển khai Event Bus chính để kết hợp Publisher và Subscriber, cung cấp unified interface cho event publishing và subscription, lifecycle management, và health monitoring.

## Input/Output
**Input**: 
- Task 01 (Core Event Interfaces)
- Task 02 (Event Configuration)
- Task 03 (Redis Publisher)
- Task 04 (Redis Subscriber)
**Output**: 
- `internal/pkg/events/bus.go` - Event bus implementation
- Unified event system interface
- Lifecycle management và health checks

## Yêu cầu
1. Implement EventBus interface từ specification
2. <PERSON>ết hợp Publisher và Subscriber functionality
3. Lifecycle management (Start/Stop/Health)
4. Handler registration và management
5. Statistics và monitoring
6. Graceful shutdown handling

## C<PERSON><PERSON> bước thực hiện

### 1. Tạo file `internal/pkg/events/bus.go`
```go
package events

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"
	
	"wnapi/internal/pkg/logger"
)

// EventBus interface kết hợp Publisher và Subscriber
type EventBus interface {
	Publisher
	Subscriber
	
	// Start khởi động event bus
	Start(ctx context.Context) error
	
	// Stop dừng event bus
	Stop(ctx context.Context) error
	
	// Health check event bus status
	Health() error
	
	// Stats trả về thống kê event bus
	Stats() BusStats
	
	// RegisterHandler đăng ký event handler
	RegisterHandler(eventType string, handler EventHandler) error
	
	// UnregisterHandler hủy đăng ký event handler
	UnregisterHandler(eventType string, handlerName string) error
	
	// IsRunning kiểm tra event bus có đang chạy không
	IsRunning() bool
}

// BusStats thống kê tổng hợp của event bus
type BusStats struct {
	// Publisher stats
	EventsPublished     int64 `json:"events_published"`
	EventsPublishFailed int64 `json:"events_publish_failed"`
	BatchesPublished    int64 `json:"batches_published"`
	
	// Subscriber stats
	EventsReceived      int64 `json:"events_received"`
	EventsProcessed     int64 `json:"events_processed"`
	EventsProcessFailed int64 `json:"events_process_failed"`
	
	// General stats
	HandlersRegistered  int64     `json:"handlers_registered"`
	WorkersActive       int64     `json:"workers_active"`
	ConnectionErrors    int64     `json:"connection_errors"`
	Uptime             time.Duration `json:"uptime"`
	StartTime          time.Time `json:"start_time"`
}

// DefaultEventBus là implementation mặc định của EventBus
type DefaultEventBus struct {
	publisher  Publisher
	subscriber Subscriber
	config     Config
	logger     logger.Logger
	
	// State management
	started   int32
	startTime time.Time
	mutex     sync.RWMutex
	
	// Handler management
	handlers map[string]map[string]EventHandler // eventType -> handlerName -> handler
}

// NewEventBus tạo event bus mới
func NewEventBus(publisher Publisher, subscriber Subscriber, config Config, logger logger.Logger) EventBus {
	return &DefaultEventBus{
		publisher:  publisher,
		subscriber: subscriber,
		config:     config,
		logger:     logger,
		handlers:   make(map[string]map[string]EventHandler),
	}
}

// Publisher interface implementation

func (bus *DefaultEventBus) Publish(ctx context.Context, event Event) error {
	if !bus.IsRunning() {
		return fmt.Errorf("event bus is not running")
	}
	return bus.publisher.Publish(ctx, event)
}

func (bus *DefaultEventBus) PublishBatch(ctx context.Context, events []Event) error {
	if !bus.IsRunning() {
		return fmt.Errorf("event bus is not running")
	}
	return bus.publisher.PublishBatch(ctx, events)
}

func (bus *DefaultEventBus) PublishAsync(ctx context.Context, event Event) <-chan error {
	if !bus.IsRunning() {
		errCh := make(chan error, 1)
		errCh <- fmt.Errorf("event bus is not running")
		close(errCh)
		return errCh
	}
	return bus.publisher.PublishAsync(ctx, event)
}

// Subscriber interface implementation

func (bus *DefaultEventBus) Subscribe(ctx context.Context, eventType string, handler EventHandler) error {
	if !bus.IsRunning() {
		return fmt.Errorf("event bus is not running")
	}
	
	// Register handler in our registry
	if err := bus.RegisterHandler(eventType, handler); err != nil {
		return err
	}
	
	// Subscribe with underlying subscriber
	return bus.subscriber.Subscribe(ctx, eventType, handler)
}

func (bus *DefaultEventBus) SubscribeMultiple(ctx context.Context, eventTypes []string, handler EventHandler) error {
	if !bus.IsRunning() {
		return fmt.Errorf("event bus is not running")
	}
	
	// Register handler for all event types
	for _, eventType := range eventTypes {
		if err := bus.RegisterHandler(eventType, handler); err != nil {
			return err
		}
	}
	
	return bus.subscriber.SubscribeMultiple(ctx, eventTypes, handler)
}

func (bus *DefaultEventBus) SubscribePattern(ctx context.Context, pattern string, handler EventHandler) error {
	if !bus.IsRunning() {
		return fmt.Errorf("event bus is not running")
	}
	
	// Register handler for pattern
	if err := bus.RegisterHandler(pattern, handler); err != nil {
		return err
	}
	
	return bus.subscriber.SubscribePattern(ctx, pattern, handler)
}

func (bus *DefaultEventBus) Unsubscribe(eventType string) error {
	// Unsubscribe from underlying subscriber
	if err := bus.subscriber.Unsubscribe(eventType); err != nil {
		return err
	}
	
	// Remove from our registry
	bus.mutex.Lock()
	defer bus.mutex.Unlock()
	
	delete(bus.handlers, eventType)
	return nil
}

// EventBus specific methods

func (bus *DefaultEventBus) Start(ctx context.Context) error {
	if !atomic.CompareAndSwapInt32(&bus.started, 0, 1) {
		return fmt.Errorf("event bus already started")
	}
	
	bus.startTime = time.Now()
	
	bus.logger.Info("Starting event bus")
	
	// Start subscriber
	if err := bus.subscriber.Start(ctx); err != nil {
		atomic.StoreInt32(&bus.started, 0)
		return fmt.Errorf("failed to start subscriber: %w", err)
	}
	
	bus.logger.Info("Event bus started successfully")
	return nil
}

func (bus *DefaultEventBus) Stop(ctx context.Context) error {
	if !atomic.CompareAndSwapInt32(&bus.started, 1, 0) {
		return nil // Already stopped
	}
	
	bus.logger.Info("Stopping event bus")
	
	// Stop subscriber
	if err := bus.subscriber.Stop(ctx); err != nil {
		bus.logger.Error("Failed to stop subscriber", "error", err)
		return err
	}
	
	bus.logger.Info("Event bus stopped successfully")
	return nil
}

func (bus *DefaultEventBus) Close() error {
	// Stop if running
	if bus.IsRunning() {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()
		if err := bus.Stop(ctx); err != nil {
			bus.logger.Error("Failed to stop event bus during close", "error", err)
		}
	}
	
	// Close publisher
	if err := bus.publisher.Close(); err != nil {
		bus.logger.Error("Failed to close publisher", "error", err)
		return err
	}
	
	// Close subscriber
	if err := bus.subscriber.Close(); err != nil {
		bus.logger.Error("Failed to close subscriber", "error", err)
		return err
	}
	
	bus.logger.Info("Event bus closed")
	return nil
}

func (bus *DefaultEventBus) Health() error {
	if !bus.IsRunning() {
		return fmt.Errorf("event bus is not running")
	}
	
	// Check publisher health
	if err := bus.publisher.Health(); err != nil {
		return fmt.Errorf("publisher health check failed: %w", err)
	}
	
	// Check subscriber health
	if err := bus.subscriber.Health(); err != nil {
		return fmt.Errorf("subscriber health check failed: %w", err)
	}
	
	return nil
}

func (bus *DefaultEventBus) Stats() BusStats {
	stats := BusStats{
		StartTime: bus.startTime,
	}
	
	if bus.IsRunning() {
		stats.Uptime = time.Since(bus.startTime)
	}
	
	// Get publisher stats if available
	if publisherWithStats, ok := bus.publisher.(interface{ GetStats() PublisherStats }); ok {
		pubStats := publisherWithStats.GetStats()
		stats.EventsPublished = pubStats.EventsPublished
		stats.EventsPublishFailed = pubStats.EventsPublishFailed
		stats.BatchesPublished = pubStats.BatchesPublished
		stats.ConnectionErrors += pubStats.ConnectionErrors
	}
	
	// Get subscriber stats if available
	if subscriberWithStats, ok := bus.subscriber.(interface{ GetStats() SubscriberStats }); ok {
		subStats := subscriberWithStats.GetStats()
		stats.EventsReceived = subStats.EventsReceived
		stats.EventsProcessed = subStats.EventsProcessed
		stats.EventsProcessFailed = subStats.EventsProcessFailed
		stats.HandlersRegistered = subStats.HandlersRegistered
		stats.WorkersActive = subStats.WorkersActive
		stats.ConnectionErrors += subStats.ConnectionErrors
	}
	
	return stats
}

func (bus *DefaultEventBus) RegisterHandler(eventType string, handler EventHandler) error {
	bus.mutex.Lock()
	defer bus.mutex.Unlock()
	
	if bus.handlers[eventType] == nil {
		bus.handlers[eventType] = make(map[string]EventHandler)
	}
	
	handlerName := handler.Name()
	if _, exists := bus.handlers[eventType][handlerName]; exists {
		return fmt.Errorf("handler %s already registered for event type %s", handlerName, eventType)
	}
	
	bus.handlers[eventType][handlerName] = handler
	
	bus.logger.Info("Handler registered",
		"event_type", eventType,
		"handler_name", handlerName,
	)
	
	return nil
}

func (bus *DefaultEventBus) UnregisterHandler(eventType string, handlerName string) error {
	bus.mutex.Lock()
	defer bus.mutex.Unlock()
	
	if bus.handlers[eventType] == nil {
		return fmt.Errorf("no handlers registered for event type %s", eventType)
	}
	
	if _, exists := bus.handlers[eventType][handlerName]; !exists {
		return fmt.Errorf("handler %s not found for event type %s", handlerName, eventType)
	}
	
	delete(bus.handlers[eventType], handlerName)
	
	// Clean up empty event type map
	if len(bus.handlers[eventType]) == 0 {
		delete(bus.handlers, eventType)
	}
	
	bus.logger.Info("Handler unregistered",
		"event_type", eventType,
		"handler_name", handlerName,
	)
	
	return nil
}

func (bus *DefaultEventBus) IsRunning() bool {
	return atomic.LoadInt32(&bus.started) == 1
}

// GetRegisteredHandlers trả về danh sách handlers đã đăng ký
func (bus *DefaultEventBus) GetRegisteredHandlers() map[string][]string {
	bus.mutex.RLock()
	defer bus.mutex.RUnlock()
	
	result := make(map[string][]string)
	for eventType, handlers := range bus.handlers {
		handlerNames := make([]string, 0, len(handlers))
		for handlerName := range handlers {
			handlerNames = append(handlerNames, handlerName)
		}
		result[eventType] = handlerNames
	}
	
	return result
}

// GetHandler trả về handler cụ thể
func (bus *DefaultEventBus) GetHandler(eventType, handlerName string) (EventHandler, bool) {
	bus.mutex.RLock()
	defer bus.mutex.RUnlock()
	
	if handlers, exists := bus.handlers[eventType]; exists {
		if handler, exists := handlers[handlerName]; exists {
			return handler, true
		}
	}
	
	return nil, false
}
```

### 2. Tạo factory function để khởi tạo event bus
```go
// internal/pkg/events/factory.go
package events

import (
	"fmt"
	
	"wnapi/internal/pkg/events/redis"
	"wnapi/internal/pkg/logger"
)

// NewEventBusFromConfig tạo event bus từ configuration
func NewEventBusFromConfig(config Config, logger logger.Logger, metrics EventMetrics) (EventBus, error) {
	if !config.Enabled {
		logger.Info("Event system is disabled")
		return NewNullEventBus(), nil
	}
	
	// Create publisher
	publisher, err := createPublisher(config, logger, metrics)
	if err != nil {
		return nil, fmt.Errorf("failed to create publisher: %w", err)
	}
	
	// Create subscriber
	subscriber, err := createSubscriber(config, logger, metrics)
	if err != nil {
		return nil, fmt.Errorf("failed to create subscriber: %w", err)
	}
	
	// Create event bus
	bus := NewEventBus(publisher, subscriber, config, logger)
	
	logger.Info("Event bus created successfully",
		"broker_type", config.Broker.Type,
		"enabled", config.Enabled,
	)
	
	return bus, nil
}

func createPublisher(config Config, logger logger.Logger, metrics EventMetrics) (Publisher, error) {
	switch config.Broker.Type {
	case "redis":
		var publisherMetrics PublisherMetrics
		if metrics != nil {
			publisherMetrics = metrics
		}
		return redis.NewRedisPublisher(config, logger, publisherMetrics)
	default:
		return nil, fmt.Errorf("unsupported broker type: %s", config.Broker.Type)
	}
}

func createSubscriber(config Config, logger logger.Logger, metrics EventMetrics) (Subscriber, error) {
	switch config.Broker.Type {
	case "redis":
		var subscriberMetrics SubscriberMetrics
		if metrics != nil {
			subscriberMetrics = metrics
		}
		return redis.NewRedisSubscriber(config, logger, subscriberMetrics)
	default:
		return nil, fmt.Errorf("unsupported broker type: %s", config.Broker.Type)
	}
}

// EventMetrics interface kết hợp cả Publisher và Subscriber metrics
type EventMetrics interface {
	PublisherMetrics
	SubscriberMetrics
}

// NullEventBus implementation cho khi event system bị disable
type NullEventBus struct {
	*NullPublisher
	*NullSubscriber
}

func NewNullEventBus() EventBus {
	return &NullEventBus{
		NullPublisher:  &NullPublisher{},
		NullSubscriber: &NullSubscriber{},
	}
}

func (bus *NullEventBus) Start(ctx context.Context) error { return nil }
func (bus *NullEventBus) Stop(ctx context.Context) error  { return nil }
func (bus *NullEventBus) Health() error                   { return nil }
func (bus *NullEventBus) IsRunning() bool                 { return true }

func (bus *NullEventBus) Stats() BusStats {
	return BusStats{}
}

func (bus *NullEventBus) RegisterHandler(eventType string, handler EventHandler) error {
	return nil
}

func (bus *NullEventBus) UnregisterHandler(eventType string, handlerName string) error {
	return nil
}

// NullSubscriber implementation
type NullSubscriber struct{}

func (s *NullSubscriber) Subscribe(ctx context.Context, eventType string, handler EventHandler) error {
	return nil
}

func (s *NullSubscriber) SubscribeMultiple(ctx context.Context, eventTypes []string, handler EventHandler) error {
	return nil
}

func (s *NullSubscriber) SubscribePattern(ctx context.Context, pattern string, handler EventHandler) error {
	return nil
}

func (s *NullSubscriber) Unsubscribe(eventType string) error {
	return nil
}

func (s *NullSubscriber) Start(ctx context.Context) error {
	return nil
}

func (s *NullSubscriber) Stop(ctx context.Context) error {
	return nil
}

func (s *NullSubscriber) Close() error {
	return nil
}

func (s *NullSubscriber) Health() error {
	return nil
}
```

## Kết quả mong đợi
- EventBus interface implementation hoàn chỉnh
- Unified interface cho publishing và subscription
- Lifecycle management (Start/Stop/Health)
- Handler registration và management
- Statistics collection và monitoring
- Factory functions để tạo event bus từ config
- Null implementations cho testing và disabled state
- Graceful shutdown handling
- Dự án vẫn biên dịch được

## Thời gian dự kiến
1-2 giờ làm việc

## Độ ưu tiên
Cao (Central component kết hợp tất cả event functionality)

## Phụ thuộc
- Task 01: Core Event Interfaces
- Task 02: Event Configuration
- Task 03: Redis Publisher
- Task 04: Redis Subscriber

## Acceptance Criteria
1. ✅ EventBus interface implementation đầy đủ
2. ✅ Publisher và Subscriber integration hoạt động
3. ✅ Lifecycle management chính xác
4. ✅ Handler registration system hoạt động
5. ✅ Statistics collection comprehensive
6. ✅ Factory functions tạo bus từ config
7. ✅ Null implementations cho edge cases
8. ✅ Graceful shutdown handling
9. ✅ Dự án build thành công với `make build`

## Build Verification
```bash
# Kiểm tra build
make build

# Test event bus functionality
go test ./internal/pkg/events/...
```
