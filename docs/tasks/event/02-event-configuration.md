# Task 02: Event Configuration System

## <PERSON><PERSON><PERSON> tiêu
Tạ<PERSON> hệ thống cấu hình cho event system, bao gồm config structures, environment variable loading, và validation để hỗ trợ Redis Streams, retry policies, và monitoring settings.

## Input/Output
**Input**: Task 01 (Core Event Interfaces)
**Output**: 
- `internal/pkg/events/config.go` - Configuration structures
- `internal/pkg/events/redis/config.go` - Redis-specific configuration
- Environment variable definitions

## Yêu cầu
1. Hỗ trợ tất cả config options từ specification
2. Environment variable loading với default values
3. Configuration validation
4. Redis connection configuration
5. Retry và error handling configuration
6. Monitoring và tracing configuration

## Cá<PERSON> bước thực hiện

### 1. Tạo file `internal/pkg/events/config.go`
```go
package events

import (
	"fmt"
	"time"
)

// Config là cấu hình chính cho event system
type Config struct {
	// Enabled bật/tắt event system
	Enabled bool `env:"EVENT_ENABLED" envDefault:"true"`
	
	// Broker configuration
	Broker BrokerConfig `env:",prefix=EVENT_BROKER_"`
	
	// Redis configuration
	Redis RedisConfig `env:",prefix=EVENT_REDIS_"`
	
	// Publisher settings
	Publisher PublisherConfig `env:",prefix=EVENT_PUBLISHER_"`
	
	// Subscriber settings
	Subscriber SubscriberConfig `env:",prefix=EVENT_SUBSCRIBER_"`
	
	// Retry configuration
	Retry RetryConfig `env:",prefix=EVENT_RETRY_"`
	
	// Dead letter queue settings
	DLQ DLQConfig `env:",prefix=EVENT_DLQ_"`
	
	// Monitoring settings
	Monitoring MonitoringConfig `env:",prefix=EVENT_"`
}

// BrokerConfig cấu hình cho message broker
type BrokerConfig struct {
	Type string `env:"TYPE" envDefault:"redis"`
	URL  string `env:"URL" envDefault:"redis://localhost:6379/0"`
}

// RedisConfig cấu hình cho Redis Streams
type RedisConfig struct {
	Host     string        `env:"HOST" envDefault:"localhost"`
	Port     int           `env:"PORT" envDefault:"6379"`
	DB       int           `env:"DB" envDefault:"0"`
	Password string        `env:"PASSWORD"`
	PoolSize int           `env:"POOL_SIZE" envDefault:"10"`
	Timeout  time.Duration `env:"TIMEOUT" envDefault:"5s"`
}

// PublisherConfig cấu hình cho event publisher
type PublisherConfig struct {
	BufferSize    int           `env:"BUFFER_SIZE" envDefault:"1000"`
	BatchSize     int           `env:"BATCH_SIZE" envDefault:"100"`
	FlushInterval time.Duration `env:"FLUSH_INTERVAL" envDefault:"1s"`
	MaxRetries    int           `env:"MAX_RETRIES" envDefault:"3"`
}

// SubscriberConfig cấu hình cho event subscriber
type SubscriberConfig struct {
	ConsumerGroup string        `env:"CONSUMER_GROUP" envDefault:"wnapi_consumers"`
	WorkerCount   int           `env:"WORKER_COUNT" envDefault:"5"`
	BatchSize     int           `env:"BATCH_SIZE" envDefault:"10"`
	AckTimeout    time.Duration `env:"ACK_TIMEOUT" envDefault:"30s"`
}

// RetryConfig cấu hình cho retry mechanism
type RetryConfig struct {
	MaxAttempts     int           `env:"MAX_ATTEMPTS" envDefault:"3"`
	InitialInterval time.Duration `env:"INITIAL_INTERVAL" envDefault:"1s"`
	MaxInterval     time.Duration `env:"MAX_INTERVAL" envDefault:"30s"`
	Multiplier      float64       `env:"MULTIPLIER" envDefault:"2.0"`
}

// DLQConfig cấu hình cho Dead Letter Queue
type DLQConfig struct {
	Enabled    bool          `env:"ENABLED" envDefault:"true"`
	MaxRetries int           `env:"MAX_RETRIES" envDefault:"5"`
	Retention  time.Duration `env:"RETENTION" envDefault:"168h"` // 7 days
}

// MonitoringConfig cấu hình cho monitoring và observability
type MonitoringConfig struct {
	TracingEnabled bool   `env:"TRACING_ENABLED" envDefault:"true"`
	MetricsEnabled bool   `env:"METRICS_ENABLED" envDefault:"true"`
	LoggingEnabled bool   `env:"LOGGING_ENABLED" envDefault:"true"`
	LogLevel       string `env:"LOG_LEVEL" envDefault:"info"`
}

// Validate kiểm tra tính hợp lệ của configuration
func (c *Config) Validate() error {
	if !c.Enabled {
		return nil // Skip validation if disabled
	}
	
	// Validate broker config
	if c.Broker.Type == "" {
		return fmt.Errorf("broker type is required")
	}
	
	if c.Broker.Type == "redis" && c.Broker.URL == "" {
		return fmt.Errorf("broker URL is required for Redis")
	}
	
	// Validate Redis config
	if c.Redis.Host == "" {
		return fmt.Errorf("Redis host is required")
	}
	
	if c.Redis.Port <= 0 || c.Redis.Port > 65535 {
		return fmt.Errorf("Redis port must be between 1 and 65535")
	}
	
	if c.Redis.PoolSize <= 0 {
		return fmt.Errorf("Redis pool size must be positive")
	}
	
	// Validate publisher config
	if c.Publisher.BufferSize <= 0 {
		return fmt.Errorf("publisher buffer size must be positive")
	}
	
	if c.Publisher.BatchSize <= 0 {
		return fmt.Errorf("publisher batch size must be positive")
	}
	
	if c.Publisher.MaxRetries < 0 {
		return fmt.Errorf("publisher max retries cannot be negative")
	}
	
	// Validate subscriber config
	if c.Subscriber.ConsumerGroup == "" {
		return fmt.Errorf("subscriber consumer group is required")
	}
	
	if c.Subscriber.WorkerCount <= 0 {
		return fmt.Errorf("subscriber worker count must be positive")
	}
	
	// Validate retry config
	if c.Retry.MaxAttempts < 0 {
		return fmt.Errorf("retry max attempts cannot be negative")
	}
	
	if c.Retry.Multiplier <= 0 {
		return fmt.Errorf("retry multiplier must be positive")
	}
	
	return nil
}

// GetRedisAddr trả về địa chỉ Redis đầy đủ
func (c *Config) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Redis.Host, c.Redis.Port)
}

// IsRedisPasswordProtected kiểm tra Redis có password không
func (c *Config) IsRedisPasswordProtected() bool {
	return c.Redis.Password != ""
}
```

### 2. Tạo file `internal/pkg/events/redis/config.go`
```go
package redis

import (
	"fmt"
	"time"
	
	"wnapi/internal/pkg/events"
)

// RedisStreamsConfig cấu hình cụ thể cho Redis Streams
type RedisStreamsConfig struct {
	// Stream naming configuration
	StreamPrefix    string `env:"STREAM_PREFIX" envDefault:"stream"`
	DLQStreamName   string `env:"DLQ_STREAM_NAME" envDefault:"dlq"`
	
	// Consumer group configuration
	ConsumerGroup   string `env:"CONSUMER_GROUP" envDefault:"wnapi_consumers"`
	ConsumerName    string `env:"CONSUMER_NAME" envDefault:"wnapi_consumer"`
	
	// Stream management
	MaxStreamLength int64  `env:"MAX_STREAM_LENGTH" envDefault:"10000"`
	TrimStrategy    string `env:"TRIM_STRATEGY" envDefault:"MAXLEN"`
	
	// Read configuration
	ReadTimeout     time.Duration `env:"READ_TIMEOUT" envDefault:"5s"`
	ReadCount       int64         `env:"READ_COUNT" envDefault:"10"`
	BlockTime       time.Duration `env:"BLOCK_TIME" envDefault:"1s"`
	
	// Acknowledgment configuration
	AckTimeout      time.Duration `env:"ACK_TIMEOUT" envDefault:"30s"`
	MaxRetries      int           `env:"MAX_RETRIES" envDefault:"3"`
}

// BuildRedisOptions tạo Redis options từ config
func BuildRedisOptions(cfg events.Config) *RedisOptions {
	return &RedisOptions{
		Addr:         cfg.GetRedisAddr(),
		Password:     cfg.Redis.Password,
		DB:           cfg.Redis.DB,
		PoolSize:     cfg.Redis.PoolSize,
		DialTimeout:  cfg.Redis.Timeout,
		ReadTimeout:  cfg.Redis.Timeout,
		WriteTimeout: cfg.Redis.Timeout,
	}
}

// RedisOptions cấu hình cho Redis client
type RedisOptions struct {
	Addr         string
	Password     string
	DB           int
	PoolSize     int
	DialTimeout  time.Duration
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
}

// GetStreamName tạo tên stream từ event type
func (c *RedisStreamsConfig) GetStreamName(eventType string) string {
	return fmt.Sprintf("%s:%s", c.StreamPrefix, eventType)
}

// GetDLQStreamName trả về tên stream cho Dead Letter Queue
func (c *RedisStreamsConfig) GetDLQStreamName() string {
	return fmt.Sprintf("%s:%s", c.StreamPrefix, c.DLQStreamName)
}

// GetConsumerGroupName trả về tên consumer group
func (c *RedisStreamsConfig) GetConsumerGroupName() string {
	return c.ConsumerGroup
}

// GetConsumerName tạo tên consumer unique
func (c *RedisStreamsConfig) GetConsumerName(suffix string) string {
	if suffix == "" {
		return c.ConsumerName
	}
	return fmt.Sprintf("%s_%s", c.ConsumerName, suffix)
}

// Validate kiểm tra tính hợp lệ của Redis Streams config
func (c *RedisStreamsConfig) Validate() error {
	if c.StreamPrefix == "" {
		return fmt.Errorf("stream prefix is required")
	}
	
	if c.DLQStreamName == "" {
		return fmt.Errorf("DLQ stream name is required")
	}
	
	if c.ConsumerGroup == "" {
		return fmt.Errorf("consumer group is required")
	}
	
	if c.ConsumerName == "" {
		return fmt.Errorf("consumer name is required")
	}
	
	if c.MaxStreamLength <= 0 {
		return fmt.Errorf("max stream length must be positive")
	}
	
	if c.ReadCount <= 0 {
		return fmt.Errorf("read count must be positive")
	}
	
	if c.MaxRetries < 0 {
		return fmt.Errorf("max retries cannot be negative")
	}
	
	return nil
}
```

### 3. Tạo config loader utility
```go
// internal/pkg/events/config_loader.go
package events

import (
	"os"
	"strconv"
	"time"
)

// LoadConfigFromEnv tải cấu hình từ environment variables
func LoadConfigFromEnv() (*Config, error) {
	config := &Config{
		Enabled: getBoolEnv("EVENT_ENABLED", true),
		Broker: BrokerConfig{
			Type: getStringEnv("EVENT_BROKER_TYPE", "redis"),
			URL:  getStringEnv("EVENT_BROKER_URL", "redis://localhost:6379/0"),
		},
		Redis: RedisConfig{
			Host:     getStringEnv("EVENT_REDIS_HOST", "localhost"),
			Port:     getIntEnv("EVENT_REDIS_PORT", 6379),
			DB:       getIntEnv("EVENT_REDIS_DB", 0),
			Password: getStringEnv("EVENT_REDIS_PASSWORD", ""),
			PoolSize: getIntEnv("EVENT_REDIS_POOL_SIZE", 10),
			Timeout:  getDurationEnv("EVENT_REDIS_TIMEOUT", 5*time.Second),
		},
		Publisher: PublisherConfig{
			BufferSize:    getIntEnv("EVENT_PUBLISHER_BUFFER_SIZE", 1000),
			BatchSize:     getIntEnv("EVENT_PUBLISHER_BATCH_SIZE", 100),
			FlushInterval: getDurationEnv("EVENT_PUBLISHER_FLUSH_INTERVAL", 1*time.Second),
			MaxRetries:    getIntEnv("EVENT_PUBLISHER_MAX_RETRIES", 3),
		},
		Subscriber: SubscriberConfig{
			ConsumerGroup: getStringEnv("EVENT_SUBSCRIBER_CONSUMER_GROUP", "wnapi_consumers"),
			WorkerCount:   getIntEnv("EVENT_SUBSCRIBER_WORKER_COUNT", 5),
			BatchSize:     getIntEnv("EVENT_SUBSCRIBER_BATCH_SIZE", 10),
			AckTimeout:    getDurationEnv("EVENT_SUBSCRIBER_ACK_TIMEOUT", 30*time.Second),
		},
		Retry: RetryConfig{
			MaxAttempts:     getIntEnv("EVENT_RETRY_MAX_ATTEMPTS", 3),
			InitialInterval: getDurationEnv("EVENT_RETRY_INITIAL_INTERVAL", 1*time.Second),
			MaxInterval:     getDurationEnv("EVENT_RETRY_MAX_INTERVAL", 30*time.Second),
			Multiplier:      getFloat64Env("EVENT_RETRY_MULTIPLIER", 2.0),
		},
		DLQ: DLQConfig{
			Enabled:    getBoolEnv("EVENT_DLQ_ENABLED", true),
			MaxRetries: getIntEnv("EVENT_DLQ_MAX_RETRIES", 5),
			Retention:  getDurationEnv("EVENT_DLQ_RETENTION", 168*time.Hour),
		},
		Monitoring: MonitoringConfig{
			TracingEnabled: getBoolEnv("EVENT_TRACING_ENABLED", true),
			MetricsEnabled: getBoolEnv("EVENT_METRICS_ENABLED", true),
			LoggingEnabled: getBoolEnv("EVENT_LOGGING_ENABLED", true),
			LogLevel:       getStringEnv("EVENT_LOG_LEVEL", "info"),
		},
	}
	
	return config, config.Validate()
}

// Helper functions for environment variable parsing
func getStringEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getIntEnv(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getBoolEnv(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getFloat64Env(key string, defaultValue float64) float64 {
	if value := os.Getenv(key); value != "" {
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			return floatValue
		}
	}
	return defaultValue
}

func getDurationEnv(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}
```

## Kết quả mong đợi
- Hệ thống cấu hình hoàn chỉnh cho event system
- Environment variable loading với validation
- Redis-specific configuration structures
- Configuration validation và error handling
- Helper functions để parse environment variables
- Dự án vẫn biên dịch được sau khi thêm configuration

## Thời gian dự kiến
1-2 giờ làm việc

## Độ ưu tiên
Cao (Cần thiết cho tất cả các components khác)

## Phụ thuộc
- Task 01: Core Event Interfaces

## Acceptance Criteria
1. ✅ Tất cả config structures được định nghĩa theo specification
2. ✅ Environment variable loading hoạt động chính xác
3. ✅ Configuration validation bắt được các lỗi cấu hình
4. ✅ Redis configuration được tách riêng và có validation
5. ✅ Default values hợp lý cho tất cả settings
6. ✅ Dự án build thành công với `make build`

## Build Verification
```bash
# Kiểm tra build
make build

# Test config loading
go test ./internal/pkg/events/...
```
