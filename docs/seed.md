# Thiết kế Seed System Đơn Giản cho WNAPI

## Tổng quan

Seed system là cơ chế để khởi tạo dữ liệu mẫu cho database, hỗ trợ quá trình phát triển và testing. Hệ thống seed này được thiết kế để:

- **Đơn giản**: Dễ sử dụng và bảo trì
- **Module-based**: Mỗi module quản lý seed riêng
- **Manual execution**: Chạy thủ công khi cần thiết
- **Environment-aware**: Hỗ trợ nhiều môi trường khác nhau

## Cấu trúc Seed System

### 1. Cấu trúc thư mục

```
modules/auth/
├── seeds/                           # Thư mục chứa seed data
│   ├── seed.go                      # Interface và logic chính
│   ├── data/                        # Dữ liệu seed
│   │   ├── users.json              # Dữ liệu users mẫu
│   │   ├── roles.json              # Dữ liệu roles mẫu
│   │   └── permissions.json        # Dữ liệu permissions mẫu
│   └── seeders/                     # Logic seed cụ thể
│       ├── user_seeder.go          # Seeder cho users
│       ├── role_seeder.go          # Seeder cho roles
│       └── permission_seeder.go    # Seeder cho permissions

modules/blog/
├── seeds/
│   ├── seed.go
│   ├── data/
│   │   ├── categories.json
│   │   ├── posts.json
│   │   └── tags.json
│   └── seeders/
│       ├── category_seeder.go
│       ├── post_seeder.go
│       └── tag_seeder.go

modules/ecommerce/product/
├── seeds/
│   ├── seed.go
│   ├── data/
│   │   ├── categories.json
│   │   ├── products.json
│   │   └── attributes.json
│   └── seeders/
│       ├── category_seeder.go
│       ├── product_seeder.go
│       └── attribute_seeder.go
```

### 2. Seed command structure

```
cmd/
├── seed/
│   ├── main.go                     # Entry point cho seed command
│   ├── runner.go                   # Seed runner logic
│   └── commands/                   # Các command cụ thể
│       ├── all.go                  # Seed tất cả modules
│       ├── module.go               # Seed 1 module cụ thể
│       └── list.go                 # List available seeds

scripts/
├── seed.sh                         # Script wrapper
└── seed-examples.sh                # Ví dụ usage
```

## Interface và Contracts

### 1. Seed Interface

```go
// internal/pkg/seed/interface.go
package seed

import "context"

// Seeder interface định nghĩa contract cho mỗi seeder
type Seeder interface {
    // Name trả về tên của seeder
    Name() string
    
    // Dependencies trả về danh sách seeder phụ thuộc (phải chạy trước)
    Dependencies() []string
    
    // Run thực thi seed data
    Run(ctx context.Context) error
    
    // Rollback xóa seed data (optional)
    Rollback(ctx context.Context) error
    
    // Description mô tả chức năng của seeder
    Description() string
}

// ModuleSeed interface cho mỗi module
type ModuleSeed interface {
    // ModuleName trả về tên module
    ModuleName() string
    
    // GetSeeders trả về danh sách seeders của module
    GetSeeders() []Seeder
    
    // SeedAll chạy tất cả seeders trong module
    SeedAll(ctx context.Context) error
    
    // SeedSpecific chạy seeder cụ thể
    SeedSpecific(ctx context.Context, seederName string) error
}
```

### 2. Seed Configuration

```go
// internal/pkg/seed/config.go
package seed

type Config struct {
    Environment string            `yaml:"environment"` // dev, staging, prod
    DataPath    string            `yaml:"data_path"`   // Đường dẫn đến thư mục data
    BatchSize   int               `yaml:"batch_size"`  // Số record insert mỗi batch
    SkipExists  bool              `yaml:"skip_exists"` // Skip nếu data đã tồn tại
    Modules     map[string]bool   `yaml:"modules"`     // Modules được phép seed
}

type SeedData struct {
    Environment string      `json:"environment"` // env này data áp dụng cho
    Data        interface{} `json:"data"`        // Actual data
    Metadata    Metadata    `json:"metadata"`    // Thông tin metadata
}

type Metadata struct {
    Version     string            `json:"version"`
    CreatedAt   string            `json:"created_at"`
    Description string            `json:"description"`
    Dependencies []string         `json:"dependencies"` // Dependencies với seeder khác
    Tags        []string          `json:"tags"`         // Tags để filter
}
```

## Cấu trúc File Data

### 1. Format JSON cho seed data

```json
// modules/auth/seeds/data/users.json
{
  "environment": "dev",
  "metadata": {
    "version": "1.0.0",
    "created_at": "2024-03-15T10:00:00Z",
    "description": "Sample users for development",
    "dependencies": ["roles", "permissions"],
    "tags": ["users", "auth", "dev"]
  },
  "data": [
    {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "password_hash": "$2a$10$...",
      "full_name": "Administrator",
      "is_active": true,
      "role_id": 1,
      "created_at": "2024-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "username": "user",
      "email": "<EMAIL>", 
      "password_hash": "$2a$10$...",
      "full_name": "Regular User",
      "is_active": true,
      "role_id": 2,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 2. Environment-specific data

```json
// modules/auth/seeds/data/users.staging.json
{
  "environment": "staging",
  "metadata": {
    "version": "1.0.0", 
    "description": "Users for staging environment",
    "dependencies": ["roles"]
  },
  "data": [
    {
      "username": "staging_admin",
      "email": "<EMAIL>",
      "password_hash": "$2a$10$..."
    }
  ]
}

// modules/auth/seeds/data/users.prod.json  
{
  "environment": "prod",
  "metadata": {
    "description": "Production seed data - minimal set"
  },
  "data": [
    {
      "username": "system",
      "email": "<EMAIL>",
      "is_system": true
    }
  ]
}
```

## Seed Command Usage

### 1. Command line interface

```bash
# Build seed command
go build -o bin/seed cmd/seed/main.go

# Seed tất cả modules
./bin/seed all

# Seed tất cả với environment cụ thể
./bin/seed all --env=dev

# Seed một module cụ thể
./bin/seed module --name=auth

# Seed một seeder cụ thể trong module
./bin/seed module --name=auth --seeder=users

# List available seeds
./bin/seed list

# Seed với options
./bin/seed all --env=dev --batch-size=100 --skip-exists

# Rollback seed data
./bin/seed rollback --module=auth --seeder=users

# Dry run (không thực sự insert data)
./bin/seed all --dry-run
```

### 2. Script wrapper examples

```bash
# scripts/seed.sh
#!/bin/bash

ENVIRONMENT=${1:-dev}
MODULE=${2:-""}
SEEDER=${3:-""}

echo "🌱 Starting seed process..."
echo "Environment: $ENVIRONMENT"

if [ -n "$MODULE" ]; then
    if [ -n "$SEEDER" ]; then
        echo "Seeding specific: $MODULE/$SEEDER"
        ./bin/seed module --name=$MODULE --seeder=$SEEDER --env=$ENVIRONMENT
    else
        echo "Seeding module: $MODULE"
        ./bin/seed module --name=$MODULE --env=$ENVIRONMENT
    fi
else
    echo "Seeding all modules"
    ./bin/seed all --env=$ENVIRONMENT
fi

echo "✅ Seed completed!"
```

## Module Seed Implementation Pattern

### 1. Module seed structure

```go
// modules/auth/seeds/seed.go
package seeds

import (
    "context"
    "wnapi/internal/pkg/seed"
    "wnapi/modules/auth/seeds/seeders"
)

type AuthSeed struct {
    seeders []seed.Seeder
}

func NewAuthSeed(deps Dependencies) *AuthSeed {
    return &AuthSeed{
        seeders: []seed.Seeder{
            seeders.NewRoleSeeder(deps.Repository, deps.Logger),
            seeders.NewPermissionSeeder(deps.Repository, deps.Logger), 
            seeders.NewUserSeeder(deps.Repository, deps.Logger),
        },
    }
}

func (s *AuthSeed) ModuleName() string {
    return "auth"
}

func (s *AuthSeed) GetSeeders() []seed.Seeder {
    return s.seeders
}

func (s *AuthSeed) SeedAll(ctx context.Context) error {
    for _, seeder := range s.seeders {
        if err := seeder.Run(ctx); err != nil {
            return fmt.Errorf("failed to run seeder %s: %w", seeder.Name(), err)
        }
    }
    return nil
}
```

### 2. Individual seeder implementation

```go
// modules/auth/seeds/seeders/user_seeder.go
package seeders

import (
    "context"
    "encoding/json" 
    "path/filepath"
    "wnapi/internal/pkg/seed"
    "wnapi/modules/auth/internal"
)

type UserSeeder struct {
    repo   internal.Repository
    logger logger.Logger
    config seed.Config
}

func NewUserSeeder(repo internal.Repository, logger logger.Logger) *UserSeeder {
    return &UserSeeder{
        repo:   repo,
        logger: logger,
        config: seed.LoadConfig(),
    }
}

func (s *UserSeeder) Name() string {
    return "users"
}

func (s *UserSeeder) Dependencies() []string {
    return []string{"roles", "permissions"}
}

func (s *UserSeeder) Description() string {
    return "Seed sample users for authentication testing"
}

func (s *UserSeeder) Run(ctx context.Context) error {
    // Load data từ file JSON
    dataPath := filepath.Join(s.config.DataPath, "auth/seeds/data")
    seedData, err := s.loadSeedData(dataPath, s.config.Environment)
    if err != nil {
        return err
    }

    // Parse và validate data
    var users []*internal.User
    if err := json.Unmarshal(seedData.Data, &users); err != nil {
        return err
    }

    // Insert data với batch processing
    return s.insertUsers(ctx, users)
}

func (s *UserSeeder) Rollback(ctx context.Context) error {
    // Logic để xóa seed data
    return s.repo.DeleteSeedUsers(ctx)
}
```

## Registry và Discovery

### 1. Seed registry

```go
// internal/pkg/seed/registry.go
package seed

var moduleSeeds = make(map[string]ModuleSeed)

// RegisterModuleSeed đăng ký seed cho module
func RegisterModuleSeed(name string, seed ModuleSeed) {
    moduleSeeds[name] = seed
}

// GetModuleSeed lấy seed của module
func GetModuleSeed(name string) (ModuleSeed, bool) {
    seed, exists := moduleSeeds[name]
    return seed, exists
}

// GetAllModuleSeeds lấy tất cả module seeds
func GetAllModuleSeeds() map[string]ModuleSeed {
    return moduleSeeds
}

// ListAvailableSeeds list tất cả seeders available
func ListAvailableSeeds() map[string][]string {
    result := make(map[string][]string)
    for moduleName, moduleSeeder := range moduleSeeds {
        var seederNames []string
        for _, seeder := range moduleSeeder.GetSeeders() {
            seederNames = append(seederNames, seeder.Name())
        }
        result[moduleName] = seederNames
    }
    return result
}
```

### 2. Auto registration trong module

```go
// modules/auth/seeds/init.go
package seeds

import (
    "wnapi/internal/pkg/seed"
)

func init() {
    // Tự động đăng ký auth seed khi import module
    seed.RegisterModuleSeed("auth", NewAuthSeed)
}
```

## Configuration và Environment

### 1. Seed configuration file

```yaml
# config/seed.yaml
seed:
  environment: dev
  data_path: "."
  batch_size: 50
  skip_exists: true
  
  # Module-specific settings
  modules:
    auth:
      enabled: true
      seeders:
        - users
        - roles
        - permissions
    
    blog:
      enabled: true
      seeders:
        - categories
        - posts
        
    ecommerce:
      enabled: false  # Disable cho env này
```

### 2. Environment overrides

```bash
# Environment variables
SEED_ENVIRONMENT=staging
SEED_BATCH_SIZE=100
SEED_SKIP_EXISTS=false
SEED_MODULES_AUTH_ENABLED=true
SEED_MODULES_BLOG_ENABLED=false
```

## Utilities và Helpers

### 1. Data loading utilities

```go
// internal/pkg/seed/loader.go
package seed

// LoadSeedData load data từ file theo environment
func LoadSeedData(dataPath, environment, fileName string) (*SeedData, error)

// LoadDataWithFallback load data với fallback to default environment  
func LoadDataWithFallback(dataPath, environment, fileName string) (*SeedData, error)

// ValidateSeedData validate format và required fields
func ValidateSeedData(data *SeedData) error

// ParseTemplateData support template trong data files
func ParseTemplateData(data []byte, vars map[string]interface{}) ([]byte, error)
```

### 2. Database utilities

```go
// internal/pkg/seed/database.go
package seed

// BatchInsert insert data theo batch
func BatchInsert(ctx context.Context, db *gorm.DB, tableName string, data []interface{}, batchSize int) error

// UpsertData insert hoặc update nếu data đã tồn tại
func UpsertData(ctx context.Context, db *gorm.DB, tableName string, data interface{}, conflictColumns []string) error

// CheckDataExists kiểm tra data đã tồn tại chưa
func CheckDataExists(ctx context.Context, db *gorm.DB, tableName string, conditions map[string]interface{}) (bool, error)
```

## Best Practices

### 1. Seed data principles

- **Idempotent**: Có thể chạy nhiều lần mà không gây lỗi
- **Environment-aware**: Data khác nhau cho từng environment
- **Dependency management**: Quản lý dependencies giữa các seeders
- **Minimal production data**: Ít data nhất có thể cho production
- **Realistic development data**: Data gần với thực tế cho development

### 2. File organization

- **Separate by entity**: Mỗi entity có file riêng
- **Environment-specific files**: File riêng cho từng environment
- **Version control**: Track changes trong seed data
- **Documentation**: Comment và metadata đầy đủ

### 3. Performance considerations

- **Batch processing**: Insert theo batch để tối ưu performance
- **Skip existing**: Skip data đã tồn tại
- **Transaction support**: Wrap trong transaction để đảm bảo consistency
- **Progress reporting**: Hiển thị progress cho large datasets

Hệ thống seed này cung cấp foundation đơn giản nhưng linh hoạt để quản lý seed data cho toàn bộ ứng dụng WNAPI, hỗ trợ development và testing hiệu quả.