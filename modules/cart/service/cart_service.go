package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"time"

	"wnapi/modules/cart/dto/request"
	"wnapi/modules/cart/dto/response"
	"wnapi/modules/cart/models"
	"wnapi/modules/cart/repository"
	"wnapi/modules/cart/service/processor"
)

// Ensure CartServiceImpl implements CartService
var _ CartService = (*CartServiceImpl)(nil)

// CartServiceImpl implements the CartService interface
type CartServiceImpl struct {
	cartRepo          repository.CartRepository
	cartItemRepo      repository.CartItemRepository
	cartItemAddonRepo repository.CartItemAddonRepository
	cartSummaryRepo   repository.CartSummaryRepository
	itemProcessors    map[string]processor.ItemProcessor
	addonProcessors   map[string]processor.AddonProcessor
}

// NewCartService creates a new cart service instance
func NewCartService(
	cartRepo repository.CartRepository,
	cartItemRepo repository.CartItemRepository,
	cartItemAddonRepo repository.CartItemAddonRepository,
	cartSummaryRepo repository.CartSummaryRepository,
) CartService {
	service := &CartServiceImpl{
		cartRepo:          cartRepo,
		cartItemRepo:      cartItemRepo,
		cartItemAddonRepo: cartItemAddonRepo,
		cartSummaryRepo:   cartSummaryRepo,
		itemProcessors:    make(map[string]processor.ItemProcessor),
		addonProcessors:   make(map[string]processor.AddonProcessor),
	}

	// Register item processors
	service.RegisterItemProcessor("ecom_product", processor.NewEcomProductProcessor())
	service.RegisterItemProcessor("dinein_product", processor.NewDineInProductProcessor())
	service.RegisterItemProcessor("service", processor.NewServiceProcessor())

	// Register addon processors
	service.RegisterAddonProcessor("topping", processor.NewToppingProcessor())
	service.RegisterAddonProcessor("option", processor.NewOptionProcessor())
	service.RegisterAddonProcessor("customization", processor.NewCustomizationProcessor())

	return service
}

// RegisterItemProcessor registers a new item processor for a specific type
func (s *CartServiceImpl) RegisterItemProcessor(itemType string, proc processor.ItemProcessor) {
	s.itemProcessors[itemType] = proc
}

// RegisterAddonProcessor registers a new addon processor for a specific type
func (s *CartServiceImpl) RegisterAddonProcessor(addonType string, proc processor.AddonProcessor) {
	s.addonProcessors[addonType] = proc
}

// GetOrCreateCart gets an existing cart or creates a new one
func (s *CartServiceImpl) GetOrCreateCart(ctx context.Context, req request.GetOrCreateCartRequest) (*response.CartResponse, error) {
	var cart *models.Cart
	var err error

	// Try to find existing cart based on user ID or session ID
	if req.UserID > 0 {
		cart, err = s.cartRepo.GetByUserID(ctx, uint64(req.TenantID), uint64(req.UserID))
	} else if req.SessionID != "" {
		cart, err = s.cartRepo.GetBySessionID(ctx, uint64(req.TenantID), req.SessionID)
	}

	// If cart exists and is active, return it
	if err == nil && cart != nil && cart.Status == models.CartStatusActive {
		return s.getCartWithItemsAndSummary(ctx, int64(cart.ID))
	}

	// Create new cart if not found or not active
	cart = &models.Cart{
		TenantID:  uint64(req.TenantID),
		UserID:    convertInt64ToUint64Ptr(req.UserID),
		SessionID: &req.SessionID,
		CartType:  req.CartType,
		Status:    models.CartStatusActive,
		Currency:  req.Currency,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Set expiration time if specified
	if req.ExpiresInMinutes > 0 {
		expiresAt := time.Now().Add(time.Duration(req.ExpiresInMinutes) * time.Minute)
		cart.ExpiresAt = &expiresAt
	}

	if err := s.cartRepo.Create(ctx, cart); err != nil {
		return nil, fmt.Errorf("failed to create cart: %w", err)
	}

	// Create empty summary for the cart
	summary := &models.CartSummary{
		CartID:    cart.ID,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	if err := s.cartSummaryRepo.Create(ctx, summary); err != nil {
		return nil, fmt.Errorf("failed to create cart summary: %w", err)
	}

	return s.getCartResponse(ctx, cart)
}

// GetCartByID retrieves a cart by its ID
func (s *CartServiceImpl) GetCartByID(ctx context.Context, cartID int64) (*response.CartResponse, error) {
	cart, err := s.cartRepo.GetByID(ctx, uint64(cartID))
	if err != nil {
		return nil, fmt.Errorf("failed to get cart: %w", err)
	}

	return s.getCartResponse(ctx, cart)
}

// GetCartWithItems retrieves a cart with its items
func (s *CartServiceImpl) GetCartWithItems(ctx context.Context, cartID int64) (*response.CartResponse, error) {
	return s.getCartWithItemsAndSummary(ctx, cartID)
}

// UpdateCart updates a cart
func (s *CartServiceImpl) UpdateCart(ctx context.Context, cartID int64, req request.UpdateCartRequest) (*response.CartResponse, error) {
	cart, err := s.cartRepo.GetByID(ctx, uint64(cartID))
	if err != nil {
		return nil, fmt.Errorf("failed to get cart: %w", err)
	}

	// Update cart fields
	if req.Currency != "" {
		cart.Currency = req.Currency
	}
	if req.Notes != nil {
		cart.Notes = *req.Notes
	}
	if req.Metadata != nil {
		var metadata models.JSON
		if err := json.Unmarshal(req.Metadata, &metadata); err != nil {
			return nil, fmt.Errorf("failed to parse metadata: %w", err)
		}
		cart.Metadata = metadata
	}
	cart.UpdatedAt = time.Now()

	if err := s.cartRepo.Update(ctx, cart); err != nil {
		return nil, fmt.Errorf("failed to update cart: %w", err)
	}

	return s.getCartResponse(ctx, cart)
}

// DeleteCart deletes a cart
func (s *CartServiceImpl) DeleteCart(ctx context.Context, cartID int64) error {
	// Delete cart items and addons first
	items, err := s.cartItemRepo.GetByCartID(ctx, uint64(cartID))
	if err != nil {
		return fmt.Errorf("failed to get cart items: %w", err)
	}

	for _, item := range items {
		if err := s.cartItemAddonRepo.DeleteByCartItemID(ctx, item.ID); err != nil {
			return fmt.Errorf("failed to delete cart item addons: %w", err)
		}
	}

	if err := s.cartItemRepo.DeleteByCartID(ctx, uint64(cartID)); err != nil {
		return fmt.Errorf("failed to delete cart items: %w", err)
	}

	// Delete cart summary
	if err := s.cartSummaryRepo.Delete(ctx, uint64(cartID)); err != nil {
		return fmt.Errorf("failed to delete cart summary: %w", err)
	}

	// Delete the cart
	if err := s.cartRepo.Delete(ctx, uint64(cartID)); err != nil {
		return fmt.Errorf("failed to delete cart: %w", err)
	}

	return nil
}

// ListCarts lists carts based on the given criteria
func (s *CartServiceImpl) ListCarts(ctx context.Context, tenantID int64, req request.ListCartRequest) (*response.CartListResponse, error) {
	carts, nextCursor, hasMore, err := s.cartRepo.List(ctx, uint64(tenantID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to list carts: %w", err)
	}

	cartResponses := make([]*response.CartResponse, 0, len(carts))
	for _, cart := range carts {
		resp, err := s.getCartResponse(ctx, cart)
		if err != nil {
			return nil, err
		}
		cartResponses = append(cartResponses, resp)
	}

	return &response.CartListResponse{
		Items:      cartResponses,
		NextCursor: nextCursor,
		HasMore:    hasMore,
	}, nil
}

// AddItemToCart adds an item to a cart
func (s *CartServiceImpl) AddItemToCart(ctx context.Context, cartID int64, req request.AddCartItemRequest) (*response.CartItemResponse, error) {
	cart, err := s.cartRepo.GetByID(ctx, uint64(cartID))
	if err != nil {
		return nil, fmt.Errorf("failed to get cart: %w", err)
	}

	// Get the appropriate item processor
	proc, ok := s.itemProcessors[req.ItemType]
	if !ok {
		return nil, fmt.Errorf("unsupported item type: %s", req.ItemType)
	}

	// Validate the item
	if err := proc.ValidateItem(ctx, int64(cart.TenantID), req.ReferenceID, int(req.Quantity)); err != nil {
		return nil, fmt.Errorf("item validation failed: %w", err)
	}

	// Calculate item price
	options := make(map[string]interface{})
	if req.Options != nil {
		if err := json.Unmarshal(req.Options, &options); err != nil {
			return nil, fmt.Errorf("failed to parse options: %w", err)
		}
	}

	unitPrice, err := proc.CalculateItemPrice(ctx, req.ReferenceID, int(req.Quantity), options)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate item price: %w", err)
	}

	// Fetch item data for storage
	itemData, err := proc.FetchItemData(ctx, req.ReferenceID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch item data: %w", err)
	}

	// Create cart item
	cartItem := &models.CartItem{
		CartID:        uint64(cartID),
		ItemType:      req.ItemType,
		ReferenceID:   req.ReferenceID,
		ReferenceType: req.ReferenceType,
		Quantity:      uint(req.Quantity),
		UnitPrice:     unitPrice / float64(req.Quantity),
		TotalPrice:    unitPrice,
		ItemData:      itemData,
		Notes:         req.Notes,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if req.Options != nil {
		var options models.JSON
		if err := json.Unmarshal(req.Options, &options); err != nil {
			return nil, fmt.Errorf("failed to parse options: %w", err)
		}
		cartItem.Options = options
	}

	if err := s.cartItemRepo.Create(ctx, cartItem); err != nil {
		return nil, fmt.Errorf("failed to create cart item: %w", err)
	}

	// Recalculate cart
	if _, err := s.RecalculateCart(ctx, int64(cartID)); err != nil {
		return nil, fmt.Errorf("failed to recalculate cart: %w", err)
	}

	return &response.CartItemResponse{
		ID:            int64(cartItem.ID),
		CartID:        int64(cartItem.CartID),
		ItemType:      cartItem.ItemType,
		ReferenceID:   cartItem.ReferenceID,
		ReferenceType: cartItem.ReferenceType,
		Quantity:      int(cartItem.Quantity),
		UnitPrice:     cartItem.UnitPrice,
		TotalPrice:    cartItem.TotalPrice,
		ItemData:      convertJSONToRawMessage(cartItem.ItemData),
		Options:       convertJSONToRawMessage(cartItem.Options),
		Notes:         cartItem.Notes,
		CreatedAt:     cartItem.CreatedAt,
		UpdatedAt:     cartItem.UpdatedAt,
	}, nil
}

// UpdateCartItem updates a cart item
func (s *CartServiceImpl) UpdateCartItem(ctx context.Context, itemID int64, req request.UpdateCartItemRequest) (*response.CartItemResponse, error) {
	cartItem, err := s.cartItemRepo.GetByID(ctx, uint64(itemID))
	if err != nil {
		return nil, fmt.Errorf("failed to get cart item: %w", err)
	}

	// Get the cart to access tenant ID
	cart, err := s.cartRepo.GetByID(ctx, cartItem.CartID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cart: %w", err)
	}

	// Get the appropriate item processor
	proc, ok := s.itemProcessors[cartItem.ItemType]
	if !ok {
		return nil, fmt.Errorf("unsupported item type: %s", cartItem.ItemType)
	}

	// Update quantity if specified
	if req.Quantity > 0 && req.Quantity != int(cartItem.Quantity) {
		// Validate the new quantity
		if err := proc.ValidateItem(ctx, int64(cart.TenantID), cartItem.ReferenceID, req.Quantity); err != nil {
			return nil, fmt.Errorf("item validation failed: %w", err)
		}

		cartItem.Quantity = uint(req.Quantity)
	}

	// Update options if specified
	if req.Options != nil {
		var options models.JSON
		if err := json.Unmarshal(req.Options, &options); err != nil {
			return nil, fmt.Errorf("failed to parse options: %w", err)
		}
		cartItem.Options = options
	}

	// Update notes if specified
	if req.Notes != "" {
		cartItem.Notes = req.Notes
	}

	// Recalculate price
	unitPrice, err := proc.CalculateItemPrice(ctx, cartItem.ReferenceID, int(cartItem.Quantity), cartItem.Options)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate item price: %w", err)
	}

	cartItem.UnitPrice = unitPrice / float64(cartItem.Quantity)
	cartItem.TotalPrice = unitPrice
	cartItem.UpdatedAt = time.Now()

	if err := s.cartItemRepo.Update(ctx, cartItem); err != nil {
		return nil, fmt.Errorf("failed to update cart item: %w", err)
	}

	// Recalculate cart
	if _, err := s.RecalculateCart(ctx, int64(cartItem.CartID)); err != nil {
		return nil, fmt.Errorf("failed to recalculate cart: %w", err)
	}

	return &response.CartItemResponse{
		ID:            int64(cartItem.ID),
		CartID:        int64(cartItem.CartID),
		ItemType:      cartItem.ItemType,
		ReferenceID:   cartItem.ReferenceID,
		ReferenceType: cartItem.ReferenceType,
		Quantity:      int(cartItem.Quantity),
		UnitPrice:     cartItem.UnitPrice,
		TotalPrice:    cartItem.TotalPrice,
		ItemData:      convertJSONToRawMessage(cartItem.ItemData),
		Options:       convertJSONToRawMessage(cartItem.Options),
		Notes:         cartItem.Notes,
		CreatedAt:     cartItem.CreatedAt,
		UpdatedAt:     cartItem.UpdatedAt,
	}, nil
}

// RemoveCartItem removes an item from a cart
func (s *CartServiceImpl) RemoveCartItem(ctx context.Context, itemID int64) error {
	// Get the item to find its cart ID
	cartItem, err := s.cartItemRepo.GetByID(ctx, uint64(itemID))
	if err != nil {
		return fmt.Errorf("failed to get cart item: %w", err)
	}

	cartID := cartItem.CartID

	// Delete all addons for this item
	if err := s.cartItemAddonRepo.DeleteByCartItemID(ctx, uint64(itemID)); err != nil {
		return fmt.Errorf("failed to delete cart item addons: %w", err)
	}

	// Delete the item
	if err := s.cartItemRepo.Delete(ctx, uint64(itemID)); err != nil {
		return fmt.Errorf("failed to delete cart item: %w", err)
	}

	// Recalculate cart
	if _, err := s.RecalculateCart(ctx, int64(cartID)); err != nil {
		return fmt.Errorf("failed to recalculate cart: %w", err)
	}

	return nil
}

// AddAddonToCartItem adds an addon to a cart item
func (s *CartServiceImpl) AddAddonToCartItem(ctx context.Context, itemID int64, req request.AddCartItemAddonRequest) (*response.CartItemAddonResponse, error) {
	cartItem, err := s.cartItemRepo.GetByID(ctx, uint64(itemID))
	if err != nil {
		return nil, fmt.Errorf("failed to get cart item: %w", err)
	}

	// Get the cart to access tenant ID
	cart, err := s.cartRepo.GetByID(ctx, cartItem.CartID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cart: %w", err)
	}

	// Get the appropriate addon processor
	proc, ok := s.addonProcessors[req.AddonType]
	if !ok {
		return nil, fmt.Errorf("unsupported addon type: %s", req.AddonType)
	}

	// Validate the addon
	if err := proc.ValidateAddon(ctx, int64(cart.TenantID), req.ReferenceID, cartItem.ReferenceID, req.Quantity); err != nil {
		return nil, fmt.Errorf("addon validation failed: %w", err)
	}

	// Calculate addon price
	unitPrice, err := proc.CalculateAddonPrice(ctx, req.ReferenceID, cartItem.ReferenceID, req.Quantity)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate addon price: %w", err)
	}

	// Fetch addon data for storage
	addonData, err := proc.FetchAddonData(ctx, req.ReferenceID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch addon data: %w", err)
	}

	// Create cart item addon
	cartItemAddon := &models.CartItemAddon{
		CartItemID:    uint64(itemID),
		AddonType:     req.AddonType,
		ReferenceID:   req.ReferenceID,
		ReferenceType: req.ReferenceType,
		Quantity:      uint(req.Quantity),
		UnitPrice:     unitPrice / float64(req.Quantity),
		TotalPrice:    unitPrice,
		GroupID:       &req.GroupID,
		AddonData:     addonData,
		CreatedAt:     time.Now(),
	}

	if err := s.cartItemAddonRepo.Create(ctx, cartItemAddon); err != nil {
		return nil, fmt.Errorf("failed to create cart item addon: %w", err)
	}

	// Update cart item price including its addons
	if err := s.updateCartItemPrice(ctx, int64(itemID)); err != nil {
		return nil, fmt.Errorf("failed to update cart item price: %w", err)
	}

	// Recalculate cart
	if _, err := s.RecalculateCart(ctx, int64(cartItem.CartID)); err != nil {
		return nil, fmt.Errorf("failed to recalculate cart: %w", err)
	}

	return &response.CartItemAddonResponse{
		ID:            int64(cartItemAddon.ID),
		CartItemID:    int64(cartItemAddon.CartItemID),
		AddonType:     cartItemAddon.AddonType,
		ReferenceID:   cartItemAddon.ReferenceID,
		ReferenceType: cartItemAddon.ReferenceType,
		Quantity:      int(cartItemAddon.Quantity),
		UnitPrice:     cartItemAddon.UnitPrice,
		TotalPrice:    cartItemAddon.TotalPrice,
		GroupID:       req.GroupID,
		AddonData:     convertJSONToRawMessage(cartItemAddon.AddonData),
		CreatedAt:     cartItemAddon.CreatedAt,
	}, nil
}

// RemoveAddonFromCartItem removes an addon from a cart item
func (s *CartServiceImpl) RemoveAddonFromCartItem(ctx context.Context, addonID int64) error {
	// Get the addon to find its cart item ID
	cartItemAddon, err := s.cartItemAddonRepo.GetByID(ctx, uint64(addonID))
	if err != nil {
		return fmt.Errorf("failed to get cart item addon: %w", err)
	}

	cartItemID := cartItemAddon.CartItemID

	// Get the cart item to find its cart ID
	cartItem, err := s.cartItemRepo.GetByID(ctx, cartItemID)
	if err != nil {
		return fmt.Errorf("failed to get cart item: %w", err)
	}

	cartID := cartItem.CartID

	// Delete the addon
	if err := s.cartItemAddonRepo.Delete(ctx, uint64(addonID)); err != nil {
		return fmt.Errorf("failed to delete cart item addon: %w", err)
	}

	// Update cart item price
	if err := s.updateCartItemPrice(ctx, int64(cartItemID)); err != nil {
		return fmt.Errorf("failed to update cart item price: %w", err)
	}

	// Recalculate cart
	if _, err := s.RecalculateCart(ctx, int64(cartID)); err != nil {
		return fmt.Errorf("failed to recalculate cart: %w", err)
	}

	return nil
}

// RecalculateCart recalculates the cart summary
func (s *CartServiceImpl) RecalculateCart(ctx context.Context, cartID int64) (*response.CartSummaryResponse, error) {
	// Get all items in the cart
	items, err := s.cartItemRepo.GetByCartID(ctx, uint64(cartID))
	if err != nil {
		return nil, fmt.Errorf("failed to get cart items: %w", err)
	}

	// Calculate subtotal
	var subtotal float64
	for _, item := range items {
		subtotal += item.TotalPrice
	}

	// Get current summary or create a new one
	summary, err := s.cartSummaryRepo.GetByCartID(ctx, uint64(cartID))
	if err != nil {
		// Create a new summary if it doesn't exist
		summary = &models.CartSummary{
			CartID:    uint64(cartID),
			CreatedAt: time.Now(),
		}
	}

	// Update summary values
	summary.Subtotal = subtotal
	summary.TotalAmount = subtotal + summary.TaxAmount + summary.ShippingAmount + summary.ServiceFee - summary.DiscountAmount
	summary.UpdatedAt = time.Now()

	// Save the summary
	if summary.ID == 0 {
		if err := s.cartSummaryRepo.Create(ctx, summary); err != nil {
			return nil, fmt.Errorf("failed to create cart summary: %w", err)
		}
	} else {
		if err := s.cartSummaryRepo.Update(ctx, summary); err != nil {
			return nil, fmt.Errorf("failed to update cart summary: %w", err)
		}
	}

	return &response.CartSummaryResponse{
		ID:             int64(summary.ID),
		CartID:         int64(summary.CartID),
		Subtotal:       summary.Subtotal,
		DiscountAmount: summary.DiscountAmount,
		TaxAmount:      summary.TaxAmount,
		ShippingAmount: summary.ShippingAmount,
		ServiceFee:     summary.ServiceFee,
		OtherFees:      convertJSONToRawMessage(summary.OtherFees),
		TotalAmount:    summary.TotalAmount,
		CouponCodes:    convertJSONToRawMessage(summary.CouponCodes),
		CreatedAt:      summary.CreatedAt,
		UpdatedAt:      summary.UpdatedAt,
	}, nil
}

// ApplyCoupon applies a coupon code to the cart
func (s *CartServiceImpl) ApplyCoupon(ctx context.Context, cartID int64, couponCode string) (*response.CartSummaryResponse, error) {
	// For now, this is a placeholder implementation.
	// In a real implementation, you would:
	// 1. Validate the coupon code
	// 2. Calculate the discount
	// 3. Update the cart summary

	// Lấy thông tin của cart summary
	_, err := s.cartSummaryRepo.GetByCartID(ctx, uint64(cartID))
	if err != nil {
		return nil, fmt.Errorf("failed to get cart summary: %w", err)
	}

	// Placeholder for coupon application logic
	// ...

	// Recalculate cart
	return s.RecalculateCart(ctx, cartID)
}

// ExpireCart marks a cart as expired
func (s *CartServiceImpl) ExpireCart(ctx context.Context, cartID int64) error {
	return s.cartRepo.UpdateStatus(ctx, uint64(cartID), models.CartStatusExpired)
}

// ConvertCartToOrder converts a cart to an order
func (s *CartServiceImpl) ConvertCartToOrder(ctx context.Context, cartID int64, orderType string) (string, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Create an order in the order system
	// 2. Transfer all cart items to order items
	// 3. Mark the cart as converted

	if err := s.cartRepo.UpdateStatus(ctx, uint64(cartID), models.CartStatusConverted); err != nil {
		return "", fmt.Errorf("failed to update cart status: %w", err)
	}

	// Return a placeholder order reference
	return fmt.Sprintf("order-%d-%s", cartID, orderType), nil
}

// MergeAnonymousCart merges an anonymous cart into a user's cart
func (s *CartServiceImpl) MergeAnonymousCart(ctx context.Context, sessionID string, userID int64) (*response.CartResponse, error) {
	// Get the anonymous cart
	anonymousCart, err := s.cartRepo.GetBySessionID(ctx, 0, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get anonymous cart: %w", err)
	}

	// Ensure we found an anonymous cart
	if anonymousCart == nil {
		return nil, errors.New("anonymous cart not found")
	}

	// Get the user's cart or create one if it doesn't exist
	tenantID := anonymousCart.TenantID
	userCart, err := s.cartRepo.GetByUserID(ctx, tenantID, uint64(userID))
	if err != nil || userCart == nil || userCart.Status != models.CartStatusActive {
		// Create a new cart for the user
		userCart = &models.Cart{
			TenantID:  tenantID,
			UserID:    convertInt64ToUint64Ptr(userID),
			CartType:  anonymousCart.CartType,
			Status:    models.CartStatusActive,
			Currency:  anonymousCart.Currency,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		if err := s.cartRepo.Create(ctx, userCart); err != nil {
			return nil, fmt.Errorf("failed to create user cart: %w", err)
		}

		// Create empty summary for the new cart
		summary := &models.CartSummary{
			CartID:    userCart.ID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		if err := s.cartSummaryRepo.Create(ctx, summary); err != nil {
			return nil, fmt.Errorf("failed to create cart summary: %w", err)
		}
	}

	// Get all items from the anonymous cart
	anonItems, err := s.cartItemRepo.GetByCartID(ctx, anonymousCart.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get anonymous cart items: %w", err)
	}

	// Add each item from the anonymous cart to the user's cart
	for _, item := range anonItems {
		// Get addons for this item
		addons, err := s.cartItemAddonRepo.GetByCartItemID(ctx, item.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to get cart item addons: %w", err)
		}

		// Create a new item in the user's cart
		newItem := *item // Copy the item
		newItem.ID = 0   // Reset ID for insertion
		newItem.CartID = userCart.ID
		newItem.CreatedAt = time.Now()
		newItem.UpdatedAt = time.Now()

		if err := s.cartItemRepo.Create(ctx, &newItem); err != nil {
			return nil, fmt.Errorf("failed to create cart item in user cart: %w", err)
		}

		// Add addons to the new item
		for _, addon := range addons {
			newAddon := *addon // Copy the addon
			newAddon.ID = 0    // Reset ID for insertion
			newAddon.CartItemID = newItem.ID
			newAddon.CreatedAt = time.Now()

			if err := s.cartItemAddonRepo.Create(ctx, &newAddon); err != nil {
				return nil, fmt.Errorf("failed to create cart item addon in user cart: %w", err)
			}
		}
	}

	// Mark the anonymous cart as expired
	if err := s.cartRepo.UpdateStatus(ctx, anonymousCart.ID, models.CartStatusExpired); err != nil {
		// Not critical, just log it
		log.Printf("failed to expire anonymous cart: %v", err)
	}

	// Recalculate the user's cart
	if _, err := s.RecalculateCart(ctx, int64(userCart.ID)); err != nil {
		return nil, fmt.Errorf("failed to recalculate user cart: %w", err)
	}

	return s.getCartWithItemsAndSummary(ctx, int64(userCart.ID))
}

// Helper functions

// getCartResponse creates a cart response from a cart model
func (s *CartServiceImpl) getCartResponse(ctx context.Context, cart *models.Cart) (*response.CartResponse, error) {
	summary, err := s.cartSummaryRepo.GetByCartID(ctx, cart.ID)
	if err != nil {
		// If summary doesn't exist, create a default one
		summary = &models.CartSummary{
			CartID:    cart.ID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
	}

	// Convert JSON types
	var metadataJSON json.RawMessage
	if cart.Metadata != nil {
		metadataJSON = convertJSONToRawMessage(cart.Metadata)
	}

	var otherFeesJSON json.RawMessage
	if summary.OtherFees != nil {
		otherFeesJSON = convertJSONToRawMessage(summary.OtherFees)
	}

	var couponCodesJSON json.RawMessage
	if summary.CouponCodes != nil {
		couponCodesJSON = convertJSONToRawMessage(summary.CouponCodes)
	}

	return &response.CartResponse{
		ID:        int64(cart.ID),
		TenantID:  int64(cart.TenantID),
		UserID:    convertUint64PtrToInt64Ptr(cart.UserID),
		SessionID: cart.SessionID,
		CartType:  cart.CartType,
		Status:    string(cart.Status),
		Currency:  cart.Currency,
		Notes:     cart.Notes,
		Metadata:  metadataJSON,
		ExpiresAt: cart.ExpiresAt,
		CreatedAt: cart.CreatedAt,
		UpdatedAt: cart.UpdatedAt,
		Summary: &response.CartSummaryResponse{
			ID:             int64(summary.ID),
			CartID:         int64(summary.CartID),
			Subtotal:       summary.Subtotal,
			DiscountAmount: summary.DiscountAmount,
			TaxAmount:      summary.TaxAmount,
			ShippingAmount: summary.ShippingAmount,
			ServiceFee:     summary.ServiceFee,
			OtherFees:      otherFeesJSON,
			TotalAmount:    summary.TotalAmount,
			CouponCodes:    couponCodesJSON,
			CreatedAt:      summary.CreatedAt,
			UpdatedAt:      summary.UpdatedAt,
		},
	}, nil
}

// getCartWithItemsAndSummary fetches a cart with its items and summary
func (s *CartServiceImpl) getCartWithItemsAndSummary(ctx context.Context, cartID int64) (*response.CartResponse, error) {
	// Get cart
	cart, err := s.cartRepo.GetByID(ctx, uint64(cartID))
	if err != nil {
		return nil, fmt.Errorf("failed to get cart: %w", err)
	}

	// Get cart summary
	summary, err := s.cartSummaryRepo.GetByCartID(ctx, uint64(cartID))
	if err != nil {
		// If summary doesn't exist, create a default one
		summary = &models.CartSummary{
			CartID:    uint64(cartID),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
	}

	// Get all items in the cart
	items, err := s.cartItemRepo.GetByCartID(ctx, uint64(cartID))
	if err != nil {
		return nil, fmt.Errorf("failed to get cart items: %w", err)
	}

	// Sử dụng hàm đã tách ra
	return s.buildCartResponseWithItems(ctx, cart, summary, items)
}

// updateCartItemPrice recalculates and updates the price of a cart item including its addons
func (s *CartServiceImpl) updateCartItemPrice(ctx context.Context, itemID int64) error {
	// Get the cart item
	cartItem, err := s.cartItemRepo.GetByID(ctx, uint64(itemID))
	if err != nil {
		return fmt.Errorf("failed to get cart item: %w", err)
	}

	// Get the appropriate item processor
	proc, ok := s.itemProcessors[cartItem.ItemType]
	if !ok {
		return fmt.Errorf("unsupported item type: %s", cartItem.ItemType)
	}

	// Calculate base item price
	basePrice, err := proc.CalculateItemPrice(ctx, cartItem.ReferenceID, int(cartItem.Quantity), cartItem.Options)
	if err != nil {
		return fmt.Errorf("failed to calculate item price: %w", err)
	}

	// Get all addons for this item
	addons, err := s.cartItemAddonRepo.GetByCartItemID(ctx, uint64(itemID))
	if err != nil {
		return fmt.Errorf("failed to get cart item addons: %w", err)
	}

	// Calculate total price including addons
	totalPrice := basePrice
	for _, addon := range addons {
		totalPrice += addon.TotalPrice
	}

	// Update the cart item
	cartItem.UnitPrice = basePrice / float64(cartItem.Quantity)
	cartItem.TotalPrice = totalPrice
	cartItem.UpdatedAt = time.Now()

	if err := s.cartItemRepo.Update(ctx, cartItem); err != nil {
		return fmt.Errorf("failed to update cart item: %w", err)
	}

	return nil
}

// convertInt64ToUint64Ptr chuyển đổi int64 sang con trỏ uint64
func convertInt64ToUint64Ptr(val int64) *uint64 {
	if val <= 0 {
		return nil
	}
	uval := uint64(val)
	return &uval
}

// convertJSONToRawMessage chuyển đổi models.JSON sang json.RawMessage
func convertJSONToRawMessage(data models.JSON) json.RawMessage {
	if data == nil {
		return nil
	}

	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return nil
	}

	return jsonBytes
}

// convertUint64PtrToInt64Ptr chuyển đổi *uint64 sang *int64
func convertUint64PtrToInt64Ptr(val *uint64) *int64 {
	if val == nil {
		return nil
	}
	ival := int64(*val)
	return &ival
}
