package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/jmoiron/sqlx"

	"wnapi/modules/cart/models"
	"wnapi/modules/cart/repository"
)

// CartSummaryRepository implements repository.CartSummaryRepository
type CartSummaryRepository struct {
	db *sqlx.DB
}

// NewCartSummaryRepository creates a new instance of CartSummaryRepository
func NewCartSummaryRepository(db *sqlx.DB) repository.CartSummaryRepository {
	return &CartSummaryRepository{db: db}
}

// Create inserts a new cart summary into the database
func (r *CartSummaryRepository) Create(ctx context.Context, summary *models.CartSummary) error {
	query := `
		INSERT INTO cart_summaries (
			cart_id, subtotal, discount_amount, tax_amount, shipping_amount,
			service_fee, other_fees, total_amount, coupon_codes, created_at, updated_at
		) VALUES (
			:cart_id, :subtotal, :discount_amount, :tax_amount, :shipping_amount,
			:service_fee, :other_fees, :total_amount, :coupon_codes, :created_at, :updated_at
		)
	`

	result, err := r.db.NamedExecContext(ctx, query, map[string]interface{}{
		"cart_id":         summary.CartID,
		"subtotal":        summary.Subtotal,
		"discount_amount": summary.DiscountAmount,
		"tax_amount":      summary.TaxAmount,
		"shipping_amount": summary.ShippingAmount,
		"service_fee":     summary.ServiceFee,
		"other_fees":      summary.OtherFees,
		"total_amount":    summary.TotalAmount,
		"coupon_codes":    summary.CouponCodes,
		"created_at":      summary.CreatedAt,
		"updated_at":      summary.UpdatedAt,
	})
	if err != nil {
		return fmt.Errorf("failed to create cart summary: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get last insert ID: %w", err)
	}

	summary.ID = uint64(id)
	return nil
}

// GetByCartID fetches a cart summary by cart ID
func (r *CartSummaryRepository) GetByCartID(ctx context.Context, cartID uint64) (*models.CartSummary, error) {
	query := `
		SELECT 
			id, cart_id, subtotal, discount_amount, tax_amount, shipping_amount,
			service_fee, other_fees, total_amount, coupon_codes, created_at, updated_at
		FROM cart_summaries
		WHERE cart_id = ?
	`

	var summary models.CartSummary
	err := r.db.GetContext(ctx, &summary, query, cartID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // No summary yet, not an error
		}
		return nil, fmt.Errorf("failed to get cart summary: %w", err)
	}

	return &summary, nil
}

// Update updates an existing cart summary in the database
func (r *CartSummaryRepository) Update(ctx context.Context, summary *models.CartSummary) error {
	query := `
		UPDATE cart_summaries
		SET subtotal = :subtotal,
			discount_amount = :discount_amount,
			tax_amount = :tax_amount,
			shipping_amount = :shipping_amount,
			service_fee = :service_fee,
			other_fees = :other_fees,
			total_amount = :total_amount,
			coupon_codes = :coupon_codes,
			updated_at = :updated_at
		WHERE cart_id = :cart_id
	`

	_, err := r.db.NamedExecContext(ctx, query, map[string]interface{}{
		"cart_id":         summary.CartID,
		"subtotal":        summary.Subtotal,
		"discount_amount": summary.DiscountAmount,
		"tax_amount":      summary.TaxAmount,
		"shipping_amount": summary.ShippingAmount,
		"service_fee":     summary.ServiceFee,
		"other_fees":      summary.OtherFees,
		"total_amount":    summary.TotalAmount,
		"coupon_codes":    summary.CouponCodes,
		"updated_at":      summary.UpdatedAt,
	})
	if err != nil {
		return fmt.Errorf("failed to update cart summary: %w", err)
	}

	return nil
}

// Delete deletes a cart summary from the database
func (r *CartSummaryRepository) Delete(ctx context.Context, cartID uint64) error {
	query := `DELETE FROM cart_summaries WHERE cart_id = ?`

	_, err := r.db.ExecContext(ctx, query, cartID)
	if err != nil {
		return fmt.Errorf("failed to delete cart summary: %w", err)
	}

	return nil
}
