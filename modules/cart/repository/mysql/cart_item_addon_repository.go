package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/jmoiron/sqlx"

	"wnapi/modules/cart/models"
	"wnapi/modules/cart/repository"
)

// CartItemAddonRepository implements repository.CartItemAddonRepository
type CartItemAddonRepository struct {
	db *sqlx.DB
}

// NewCartItemAddonRepository creates a new instance of CartItemAddonRepository
func NewCartItemAddonRepository(db *sqlx.DB) repository.CartItemAddonRepository {
	return &CartItemAddonRepository{db: db}
}

// Create inserts a new cart item addon into the database
func (r *CartItemAddonRepository) Create(ctx context.Context, addon *models.CartItemAddon) error {
	query := `
		INSERT INTO cart_item_addons (
			cart_item_id, addon_type, reference_id, reference_type, quantity,
			unit_price, total_price, group_id, addon_data, created_at
		) VALUES (
			:cart_item_id, :addon_type, :reference_id, :reference_type, :quantity,
			:unit_price, :total_price, :group_id, :addon_data, :created_at
		)
	`

	result, err := r.db.NamedExecContext(ctx, query, map[string]interface{}{
		"cart_item_id":   addon.CartItemID,
		"addon_type":     addon.AddonType,
		"reference_id":   addon.ReferenceID,
		"reference_type": addon.ReferenceType,
		"quantity":       addon.Quantity,
		"unit_price":     addon.UnitPrice,
		"total_price":    addon.TotalPrice,
		"group_id":       addon.GroupID,
		"addon_data":     addon.AddonData,
		"created_at":     addon.CreatedAt,
	})
	if err != nil {
		return fmt.Errorf("failed to create cart item addon: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get last insert ID: %w", err)
	}

	addon.ID = uint64(id)
	return nil
}

// GetByID fetches a cart item addon by ID
func (r *CartItemAddonRepository) GetByID(ctx context.Context, addonID uint64) (*models.CartItemAddon, error) {
	query := `
		SELECT 
			id, cart_item_id, addon_type, reference_id, reference_type, quantity,
			unit_price, total_price, group_id, addon_data, created_at
		FROM cart_item_addons
		WHERE id = ?
	`

	var addon models.CartItemAddon
	err := r.db.GetContext(ctx, &addon, query, addonID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("cart item addon not found with ID %d", addonID)
		}
		return nil, fmt.Errorf("failed to get cart item addon: %w", err)
	}

	return &addon, nil
}

// Update updates an existing cart item addon in the database
func (r *CartItemAddonRepository) Update(ctx context.Context, addon *models.CartItemAddon) error {
	query := `
		UPDATE cart_item_addons
		SET cart_item_id = :cart_item_id,
			addon_type = :addon_type,
			reference_id = :reference_id,
			reference_type = :reference_type,
			quantity = :quantity,
			unit_price = :unit_price,
			total_price = :total_price,
			group_id = :group_id,
			addon_data = :addon_data
		WHERE id = :id
	`

	_, err := r.db.NamedExecContext(ctx, query, map[string]interface{}{
		"id":             addon.ID,
		"cart_item_id":   addon.CartItemID,
		"addon_type":     addon.AddonType,
		"reference_id":   addon.ReferenceID,
		"reference_type": addon.ReferenceType,
		"quantity":       addon.Quantity,
		"unit_price":     addon.UnitPrice,
		"total_price":    addon.TotalPrice,
		"group_id":       addon.GroupID,
		"addon_data":     addon.AddonData,
	})
	if err != nil {
		return fmt.Errorf("failed to update cart item addon: %w", err)
	}

	return nil
}

// Delete deletes a cart item addon from the database
func (r *CartItemAddonRepository) Delete(ctx context.Context, addonID uint64) error {
	query := `DELETE FROM cart_item_addons WHERE id = ?`

	_, err := r.db.ExecContext(ctx, query, addonID)
	if err != nil {
		return fmt.Errorf("failed to delete cart item addon: %w", err)
	}

	return nil
}

// GetByCartItemID fetches all addons for a cart item
func (r *CartItemAddonRepository) GetByCartItemID(ctx context.Context, cartItemID uint64) ([]*models.CartItemAddon, error) {
	query := `
		SELECT 
			id, cart_item_id, addon_type, reference_id, reference_type, quantity,
			unit_price, total_price, group_id, addon_data, created_at
		FROM cart_item_addons
		WHERE cart_item_id = ?
		ORDER BY created_at ASC
	`

	var addons []*models.CartItemAddon
	err := r.db.SelectContext(ctx, &addons, query, cartItemID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cart item addons: %w", err)
	}

	return addons, nil
}

// DeleteByCartItemID deletes all addons for a cart item
func (r *CartItemAddonRepository) DeleteByCartItemID(ctx context.Context, cartItemID uint64) error {
	query := `DELETE FROM cart_item_addons WHERE cart_item_id = ?`

	_, err := r.db.ExecContext(ctx, query, cartItemID)
	if err != nil {
		return fmt.Errorf("failed to delete cart item addons: %w", err)
	}

	return nil
}
