package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"github.com/jmoiron/sqlx"

	"wnapi/modules/cart/dto/request"
	"wnapi/modules/cart/models"
	"wnapi/modules/cart/repository"
)

// CartRepository implements repository.CartRepository
type CartRepository struct {
	db *sqlx.DB
}

// NewCartRepository creates a new instance of CartRepository
func NewCartRepository(db *sqlx.DB) repository.CartRepository {
	return &CartRepository{db: db}
}

// Create inserts a new cart into the database
func (r *CartRepository) Create(ctx context.Context, cart *models.Cart) error {
	query := `
		INSERT INTO carts (
			tenant_id, user_id, session_id, cart_type, status, currency,
			notes, metadata, expires_at, created_at, updated_at
		) VALUES (
			:tenant_id, :user_id, :session_id, :cart_type, :status, :currency,
			:notes, :metadata, :expires_at, :created_at, :updated_at
		)
	`

	result, err := r.db.NamedExecContext(ctx, query, map[string]interface{}{
		"tenant_id":  cart.TenantID,
		"user_id":    cart.UserID,
		"session_id": cart.SessionID,
		"cart_type":  cart.CartType,
		"status":     cart.Status,
		"currency":   cart.Currency,
		"notes":      cart.Notes,
		"metadata":   cart.Metadata,
		"expires_at": cart.ExpiresAt,
		"created_at": cart.CreatedAt,
		"updated_at": cart.UpdatedAt,
	})
	if err != nil {
		return fmt.Errorf("failed to create cart: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get last insert ID: %w", err)
	}

	cart.ID = uint64(id)
	return nil
}

// GetByID fetches a cart by ID
func (r *CartRepository) GetByID(ctx context.Context, cartID uint64) (*models.Cart, error) {
	query := `
		SELECT 
			id, tenant_id, user_id, session_id, cart_type, status, currency,
			notes, metadata, expires_at, created_at, updated_at
		FROM carts
		WHERE id = ?
	`

	var cart models.Cart
	err := r.db.GetContext(ctx, &cart, query, cartID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("cart not found with ID %d", cartID)
		}
		return nil, fmt.Errorf("failed to get cart: %w", err)
	}

	return &cart, nil
}

// GetByUserID fetches an active cart by user ID
func (r *CartRepository) GetByUserID(ctx context.Context, tenantID, userID uint64) (*models.Cart, error) {
	query := `
		SELECT 
			id, tenant_id, user_id, session_id, cart_type, status, currency,
			notes, metadata, expires_at, created_at, updated_at
		FROM carts
		WHERE tenant_id = ? AND user_id = ? AND status = 'active'
		ORDER BY created_at DESC
		LIMIT 1
	`

	var cart models.Cart
	err := r.db.GetContext(ctx, &cart, query, tenantID, userID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // No cart yet, not an error
		}
		return nil, fmt.Errorf("failed to get cart by user ID: %w", err)
	}

	return &cart, nil
}

// GetBySessionID fetches an active cart by session ID
func (r *CartRepository) GetBySessionID(ctx context.Context, tenantID uint64, sessionID string) (*models.Cart, error) {
	query := `
		SELECT 
			id, tenant_id, user_id, session_id, cart_type, status, currency,
			notes, metadata, expires_at, created_at, updated_at
		FROM carts
		WHERE tenant_id = ? AND session_id = ? AND status = 'active'
		ORDER BY created_at DESC
		LIMIT 1
	`

	var cart models.Cart
	err := r.db.GetContext(ctx, &cart, query, tenantID, sessionID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // No cart yet, not an error
		}
		return nil, fmt.Errorf("failed to get cart by session ID: %w", err)
	}

	return &cart, nil
}

// Update updates an existing cart in the database
func (r *CartRepository) Update(ctx context.Context, cart *models.Cart) error {
	query := `
		UPDATE carts
		SET tenant_id = :tenant_id,
			user_id = :user_id,
			session_id = :session_id,
			cart_type = :cart_type,
			status = :status,
			currency = :currency,
			notes = :notes,
			metadata = :metadata,
			expires_at = :expires_at,
			updated_at = :updated_at
		WHERE id = :id
	`

	_, err := r.db.NamedExecContext(ctx, query, map[string]interface{}{
		"id":         cart.ID,
		"tenant_id":  cart.TenantID,
		"user_id":    cart.UserID,
		"session_id": cart.SessionID,
		"cart_type":  cart.CartType,
		"status":     cart.Status,
		"currency":   cart.Currency,
		"notes":      cart.Notes,
		"metadata":   cart.Metadata,
		"expires_at": cart.ExpiresAt,
		"updated_at": cart.UpdatedAt,
	})
	if err != nil {
		return fmt.Errorf("failed to update cart: %w", err)
	}

	return nil
}

// UpdateStatus updates the status of a cart
func (r *CartRepository) UpdateStatus(ctx context.Context, cartID uint64, status models.CartStatus) error {
	query := `
		UPDATE carts
		SET status = ?,
			updated_at = NOW()
		WHERE id = ?
	`

	_, err := r.db.ExecContext(ctx, query, status, cartID)
	if err != nil {
		return fmt.Errorf("failed to update cart status: %w", err)
	}

	return nil
}

// Delete deletes a cart from the database
func (r *CartRepository) Delete(ctx context.Context, cartID uint64) error {
	query := `DELETE FROM carts WHERE id = ?`

	_, err := r.db.ExecContext(ctx, query, cartID)
	if err != nil {
		return fmt.Errorf("failed to delete cart: %w", err)
	}

	return nil
}

// List fetches carts with cursor-based pagination
func (r *CartRepository) List(ctx context.Context, tenantID uint64, req request.ListCartRequest) ([]*models.Cart, string, bool, error) {
	baseQuery := `
		SELECT 
			id, tenant_id, user_id, session_id, cart_type, status, currency,
			notes, metadata, expires_at, created_at, updated_at
		FROM carts
		WHERE tenant_id = ?
	`

	// Build filters
	var filters []string
	var args []interface{}
	args = append(args, tenantID)

	if req.Status != "" {
		filters = append(filters, "status = ?")
		args = append(args, req.Status)
	}

	if req.CartType != "" {
		filters = append(filters, "cart_type = ?")
		args = append(args, req.CartType)
	}

	if req.UserID > 0 {
		filters = append(filters, "user_id = ?")
		args = append(args, req.UserID)
	}

	// Add cursor-based pagination
	if req.Cursor != "" {
		switch req.SortBy {
		case "created_at":
			filters = append(filters, "created_at < ?")
			args = append(args, req.Cursor)
		case "updated_at":
			filters = append(filters, "updated_at < ?")
			args = append(args, req.Cursor)
		default:
			filters = append(filters, "id < ?")
			args = append(args, req.Cursor)
		}
	}

	// Combine all filters
	if len(filters) > 0 {
		baseQuery += " AND " + strings.Join(filters, " AND ")
	}

	// Add ordering
	if req.SortBy != "" {
		baseQuery += fmt.Sprintf(" ORDER BY %s %s", req.SortBy, req.SortDir)
	} else {
		baseQuery += " ORDER BY created_at DESC"
	}

	// Add limit
	limit := 10
	if req.Limit > 0 && req.Limit <= 100 {
		limit = req.Limit
	}
	baseQuery += fmt.Sprintf(" LIMIT %d", limit+1) // Get one extra to check if there are more results

	// Execute query
	var carts []*models.Cart
	err := r.db.SelectContext(ctx, &carts, baseQuery, args...)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to list carts: %w", err)
	}

	// Check if there are more results
	hasMore := false
	if len(carts) > limit {
		hasMore = true
		carts = carts[:limit] // Remove the extra item
	}

	// Get the next cursor
	var nextCursor string
	if len(carts) > 0 && hasMore {
		lastItem := carts[len(carts)-1]
		switch req.SortBy {
		case "created_at":
			nextCursor = lastItem.CreatedAt.Format("2006-01-02 15:04:05")
		case "updated_at":
			nextCursor = lastItem.UpdatedAt.Format("2006-01-02 15:04:05")
		default:
			nextCursor = fmt.Sprintf("%d", lastItem.ID)
		}
	}

	return carts, nextCursor, hasMore, nil
}
