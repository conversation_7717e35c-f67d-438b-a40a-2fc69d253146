package repository

import (
	"context"

	"wnapi/modules/cart/dto/request"
	"wnapi/modules/cart/models"
)

// CartRepository defines the interface for cart data operations
type CartRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, cart *models.Cart) error
	GetByID(ctx context.Context, cartID uint64) (*models.Cart, error)
	GetByUserID(ctx context.Context, tenantID, userID uint64) (*models.Cart, error)
	GetBySessionID(ctx context.Context, tenantID uint64, sessionID string) (*models.Cart, error)
	Update(ctx context.Context, cart *models.Cart) error
	UpdateStatus(ctx context.Context, cartID uint64, status models.CartStatus) error
	Delete(ctx context.Context, cartID uint64) error

	// List with cursor pagination
	List(ctx context.Context, tenantID uint64, req request.ListCartRequest) ([]*models.Cart, string, bool, error)
}

// CartItemRepository defines the interface for cart item data operations
type CartItemRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, item *models.CartItem) error
	GetByID(ctx context.Context, itemID uint64) (*models.CartItem, error)
	Update(ctx context.Context, item *models.CartItem) error
	UpdateQuantity(ctx context.Context, itemID uint64, quantity uint) error
	Delete(ctx context.Context, itemID uint64) error

	// Cart-related operations
	GetByCartID(ctx context.Context, cartID uint64) ([]*models.CartItem, error)
	DeleteByCartID(ctx context.Context, cartID uint64) error
}

// CartItemAddonRepository defines the interface for cart item addon data operations
type CartItemAddonRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, addon *models.CartItemAddon) error
	GetByID(ctx context.Context, addonID uint64) (*models.CartItemAddon, error)
	Update(ctx context.Context, addon *models.CartItemAddon) error
	Delete(ctx context.Context, addonID uint64) error

	// CartItem-related operations
	GetByCartItemID(ctx context.Context, cartItemID uint64) ([]*models.CartItemAddon, error)
	DeleteByCartItemID(ctx context.Context, cartItemID uint64) error
}

// CartSummaryRepository defines the interface for cart summary data operations
type CartSummaryRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, summary *models.CartSummary) error
	GetByCartID(ctx context.Context, cartID uint64) (*models.CartSummary, error)
	Update(ctx context.Context, summary *models.CartSummary) error
	Delete(ctx context.Context, cartID uint64) error
}
