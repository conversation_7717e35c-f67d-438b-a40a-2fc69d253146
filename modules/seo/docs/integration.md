# SEO Module Integration Guide

This document explains how to integrate the SEO module with other content modules in the system.

## Overview

The SEO module provides a flexible way to attach SEO metadata to any content type in the system. This guide shows how to integrate SEO functionality with your content module.

## Integration Steps

### 1. Import Required Packages

Add the SEO module to your module's `go.mod` file:

```go
require (
    wnapi/modules/seo v0.0.0
)
```

And import the necessary packages in your code:

```go
import (
    "wnapi/modules/seo/client"
    "wnapi/modules/seo/dto/request"
)
```

### 2. Initialize the SEO Client

Create an instance of the SEO client in your module:

```go
type YourService struct {
    // ... your service fields
    seoClient *client.SeoClient
}

func NewYourService(/* dependencies */, seoClient *client.SeoClient) *YourService {
    return &YourService{
        // ... initialize your service fields
        seoClient: seoClient,
    }
}
```

### 3. Create, Update, and Retrieve SEO Metadata

Here's a typical example of CRUD operations with SEO metadata in your content service:

#### Creating Content with SEO Metadata

```go
func (s *YourService) CreateContentWithSEO(ctx context.Context, content *models.YourContent, seoData *request.CreateSeoMetaRequest) (*models.YourContent, error) {
    // 1. First create your content in the database
    if err := s.repository.Create(ctx, content); err != nil {
        return nil, err
    }

    // 2. Set the object ID to the newly created content's ID
    seoData.ObjectID = content.ID
    seoData.ObjectType = "your_content_type" // Set appropriate content type
    
    // 3. Create SEO metadata for this content
    _, err := s.seoClient.CreateSeoMeta(ctx, seoData)
    if err != nil {
        // Optionally handle the error (e.g., log it, but don't fail the content creation)
        // You might want to implement a retry mechanism or queue for this
        log.Printf("Error creating SEO metadata: %v", err)
    }

    return content, nil
}
```

#### Updating Content with SEO Metadata

```go
func (s *YourService) UpdateContentWithSEO(ctx context.Context, content *models.YourContent, seoData *request.UpdateSeoMetaRequest) (*models.YourContent, error) {
    // 1. Update your content in the database
    if err := s.repository.Update(ctx, content); err != nil {
        return nil, err
    }

    // 2. Use the upsert endpoint to ensure SEO metadata exists
    seoRequest := request.CreateSeoMetaRequest{
        ObjectID:           content.ID,
        ObjectType:         "your_content_type",
        SeoTitle:           seoData.SeoTitle,
        MetaDescription:    seoData.MetaDescription,
        // ... copy all the fields from seoData
    }
    
    // 3. Create or update SEO metadata
    _, err := s.seoClient.CreateOrUpdateSeoMeta(ctx, &seoRequest)
    if err != nil {
        log.Printf("Error updating SEO metadata: %v", err)
    }

    return content, nil
}
```

#### Retrieving Content with SEO Metadata

```go
func (s *YourService) GetContentWithSEO(ctx context.Context, id uint) (*models.YourContentWithSEO, error) {
    // 1. Get your content from the database
    content, err := s.repository.GetByID(ctx, id)
    if err != nil {
        return nil, err
    }

    // 2. Get SEO metadata for this content
    seoMeta, err := s.seoClient.GetSeoMetaByObject(ctx, content.ID, "your_content_type")
    
    // 3. Create combined response
    result := &models.YourContentWithSEO{
        Content: *content,
        SEO:     seoMeta,
    }
    
    // Note: If SEO data doesn't exist yet, we still return the content
    if err != nil {
        log.Printf("SEO metadata not found: %v", err)
        result.SEO = nil
    }

    return result, nil
}
```

#### Deleting Content with SEO Metadata

```go
func (s *YourService) DeleteContent(ctx context.Context, id uint) error {
    // 1. Delete your content from the database
    if err := s.repository.Delete(ctx, id); err != nil {
        return err
    }

    // 2. Delete associated SEO metadata
    err := s.seoClient.DeleteSeoMetaByObject(ctx, id, "your_content_type")
    if err != nil {
        // Log the error but don't fail the delete operation
        log.Printf("Error deleting SEO metadata: %v", err)
    }

    return nil
}
```

### 4. Define a Combined Response Model

Create a model that combines your content with SEO metadata:

```go
// YourContentWithSEO combines content with SEO metadata
type YourContentWithSEO struct {
    Content models.YourContent `json:"content"`
    SEO     *seo.SeoMetaResponse   `json:"seo,omitempty"`
}
```

### 5. Modify API Handlers

Update your API handlers to work with SEO data:

```go
func (h *YourHandler) CreateContent(c *gin.Context) {
    var req struct {
        Content request.CreateContentRequest `json:"content"`
        SEO     request.CreateSeoMetaRequest `json:"seo,omitempty"`
    }

    if err := c.ShouldBindJSON(&req); err != nil {
        response.Error(c, http.StatusBadRequest, "Invalid request body", "INVALID_REQUEST")
        return
    }

    // Convert request to model and create with SEO
    content := models.YourContent{
        // ... map fields from req.Content
    }

    result, err := h.service.CreateContentWithSEO(c, &content, &req.SEO)
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "Failed to create content", "CREATE_FAILED")
        return
    }

    response.Success(c, result)
}
```

## SEO Client API

The SEO client provides the following methods:

```go
// CreateSeoMeta creates new SEO metadata
func (c *SeoClient) CreateSeoMeta(ctx context.Context, req *request.CreateSeoMetaRequest) (*response.SeoMetaResponse, error)

// GetSeoMetaByID retrieves SEO metadata by ID
func (c *SeoClient) GetSeoMetaByID(ctx context.Context, id uint) (*response.SeoMetaResponse, error)

// GetSeoMetaByObject retrieves SEO metadata by object ID and type
func (c *SeoClient) GetSeoMetaByObject(ctx context.Context, objectID uint, objectType string) (*response.SeoMetaResponse, error)

// UpdateSeoMeta updates existing SEO metadata
func (c *SeoClient) UpdateSeoMeta(ctx context.Context, id uint, req *request.UpdateSeoMetaRequest) (*response.SeoMetaResponse, error)

// DeleteSeoMeta deletes SEO metadata by ID
func (c *SeoClient) DeleteSeoMeta(ctx context.Context, id uint) error

// DeleteSeoMetaByObject deletes SEO metadata by object ID and type
func (c *SeoClient) DeleteSeoMetaByObject(ctx context.Context, objectID uint, objectType string) error

// CreateOrUpdateSeoMeta creates or updates SEO metadata
func (c *SeoClient) CreateOrUpdateSeoMeta(ctx context.Context, req *request.CreateSeoMetaRequest) (*response.SeoMetaResponse, error)
```

## Best Practices

1. **Use Transactions**: When creating content with SEO metadata, consider using transactions to ensure data consistency.

2. **Graceful Degradation**: Handle SEO-related errors gracefully without failing the main content operations.

3. **Common Content Types**: Use consistent object_type strings across the application:
   - "post" - For blog posts
   - "page" - For website pages
   - "product" - For e-commerce products
   - "category" - For categories
   - "tag" - For tags

4. **Bulk Operations**: For bulk operations, consider using goroutines with appropriate rate limiting to update SEO metadata.

5. **Default Values**: Consider providing sensible defaults for SEO fields based on the content itself.

## Example Implementation: Blog Module Integration

Here's a real-world example showing how the Blog module integrates with SEO:

```go
// Inside blog/service/post_service.go

// CreatePost creates a new blog post with SEO metadata
func (s *PostService) CreatePost(ctx context.Context, post *models.Post, seoMeta *request.CreateSeoMetaRequest) (*models.Post, error) {
    // Create the post
    if err := s.repo.Create(ctx, post); err != nil {
        return nil, err
    }

    // If SEO metadata is provided, create it
    if seoMeta != nil {
        seoMeta.ObjectID = post.ID
        seoMeta.ObjectType = "post"

        // If no SEO title provided, use the post title
        if seoMeta.SeoTitle == "" {
            seoMeta.SeoTitle = post.Title
        }
        
        // If no meta description provided, use the post excerpt or truncated content
        if seoMeta.MetaDescription == "" {
            if post.Excerpt != "" {
                seoMeta.MetaDescription = post.Excerpt
            } else {
                // Truncate content to a suitable meta description length
                content := stripHTML(post.Content)
                if len(content) > 160 {
                    content = content[:157] + "..."
                }
                seoMeta.MetaDescription = content
            }
        }

        _, err := s.seoClient.CreateSeoMeta(ctx, seoMeta)
        if err != nil {
            // Log but don't fail
            log.Printf("Failed to create SEO metadata for post %d: %v", post.ID, err)
        }
    }

    return post, nil
}
```