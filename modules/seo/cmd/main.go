package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"wnapi/modules/seo"
	"wnapi/modules/seo/configs"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// Load config using the configs package
	cfg, err := configs.LoadConfig()
	if err != nil {
		log.Fatalf("Error loading config: %v", err)
	}

	// Create and start module
	module := seo.NewModuleWithConfig(cfg)
	if err := module.Start(); err != nil {
		log.Fatalf("Error starting module: %v", err)
	}

	// Wait for termination signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down SEO service...")

	// Stop module
	if err := module.Stop(); err != nil {
		log.Fatalf("Error stopping module: %v", err)
	}

	log.Println("SEO service exited")
}
