package middleware

import (
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"

	"wnapi/modules/seo/tracing"
)

// TracingMiddleware trả về một middleware Gin để trace các requests
func TracingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Khởi tạo một span mới cho request
		path := c.Request.URL.Path
		method := c.Request.Method
		operationName := fmt.Sprintf("%s %s", method, path)

		// Xác định loại operation từ URL path
		var opType tracing.SeoOpType
		switch {
		case strings.Contains(path, "/api/v1/seo/meta"):
			opType = tracing.SeoOpMetatagGeneration
		case strings.Contains(path, "/api/v1/seo/sitemap"):
			opType = tracing.SeoOpSitemapGeneration
		case strings.Contains(path, "/api/v1/seo/redirect"):
			opType = tracing.SeoOpRedirectHandling
		case strings.Contains(path, "/api/v1/seo/url"):
			opType = tracing.SeoOpURLManagement
		case strings.Contains(path, "/api/v1/seo/analysis"):
			opType = tracing.SeoOpSeoAnalysis
		case strings.Contains(path, "/api/v1/seo/search-engine"):
			opType = tracing.SeoOpSearchEngineData
		default:
			opType = tracing.SeoOpMetatagGeneration
		}

		// Khởi tạo span
		ctx, span := tracing.StartSeoSpan(c.Request.Context(), operationName, opType)
		defer span.End()

		// Thêm thông tin từ request vào span
		span.SetAttributes(attribute.String("http.method", method))
		span.SetAttributes(attribute.String("http.url", c.Request.URL.String()))
		span.SetAttributes(attribute.String("http.host", c.Request.Host))
		span.SetAttributes(attribute.String("http.user_agent", c.Request.UserAgent()))

		// Thêm thông tin client IP
		clientIP := c.ClientIP()
		span.SetAttributes(attribute.String("http.client_ip", clientIP))

		// Thêm thông tin từ URL parameters
		for _, param := range c.Params {
			span.SetAttributes(attribute.String(fmt.Sprintf("http.route_param.%s", param.Key), param.Value))
		}

		// Cập nhật context cho các handlers tiếp theo
		c.Request = c.Request.WithContext(ctx)

		// Chạy các handlers tiếp theo
		c.Next()

		// Thêm thông tin sau khi request đã được xử lý
		statusCode := c.Writer.Status()
		span.SetAttributes(attribute.Int("http.status_code", statusCode))

		// Đánh dấu span là error nếu status code >= 400
		if statusCode >= 400 {
			errMsg := fmt.Sprintf("Request failed with status code %d", statusCode)
			span.SetStatus(codes.Error, errMsg)
			span.SetAttributes(attribute.String("error.message", errMsg))
		}

		// Log các headers quan trọng trong response
		span.SetAttributes(attribute.String("http.response_content_type", c.Writer.Header().Get("Content-Type")))
	}
}
