package api

import (
	"wnapi/modules/seo/service"
	"wnapi/pkg/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterSeoRoutes đăng ký tất cả routes cho module SEO
func RegisterSeoRoutes(router *gin.Engine, seoMetaService *service.SeoMetaService, permService middleware.PermissionService) {
	// Tạo handler
	seoMetaHandler := NewSeoMetaHandler(seoMetaService)

	// Đăng ký routes
	seoAPI := router.Group("/api/v1/seo")
	{
		// Routes cho SeoMeta
		seoAPI.GET("/meta/:id", middleware.RequirePermission(permService, "seo.meta.read"), seoMetaHandler.GetSeoMeta)
		seoAPI.GET("/meta", middleware.RequirePermission(permService, "seo.meta.read"), seoMetaHandler.GetSeoMetaByObject)
		seoAPI.POST("/meta", middleware.RequirePermission(permService, "seo.meta.create"), seoMetaHandler.CreateSeoMeta)
		seoAPI.PUT("/meta/:id", middleware.RequirePermission(permService, "seo.meta.update"), seoMetaHandler.UpdateSeoMeta)
		seoAPI.DELETE("/meta/:id", middleware.RequirePermission(permService, "seo.meta.delete"), seoMetaHandler.DeleteSeoMeta)
		seoAPI.DELETE("/meta", middleware.RequirePermission(permService, "seo.meta.delete"), seoMetaHandler.DeleteSeoMetaByObject)
		seoAPI.POST("/meta/upsert", middleware.RequirePermission(permService, "seo.meta.create"), seoMetaHandler.CreateOrUpdateSeoMeta)
	}
}
