package repository

import (
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/otel/attribute"

	"wnapi/modules/seo/models"
	"wnapi/modules/seo/tracing"

	"gorm.io/gorm"
)

// SeoMetaRepository đại diện cho repository xử lý dữ liệu SeoMeta
type SeoMetaRepository interface {
	GetByID(ctx context.Context, id uint) (*models.SeoMeta, error)
	GetByObject(ctx context.Context, objectID uint, objectType string) (*models.SeoMeta, error)
	Create(ctx context.Context, meta *models.SeoMeta) error
	Update(ctx context.Context, meta *models.SeoMeta) error
	Delete(ctx context.Context, id uint) error
	DeleteByObject(ctx context.Context, objectID uint, objectType string) error
	List(ctx context.Context, limit, offset int) ([]models.SeoMeta, int64, error)
}

// GormSeoMetaRepository là implemention của SeoMetaRepository sử dụng GORM
type GormSeoMetaRepository struct {
	db *gorm.DB
}

// NewGormSeoMetaRepository tạo một instance mới của GormSeoMetaRepository
func NewGormSeoMetaRepository(db *gorm.DB) *GormSeoMetaRepository {
	return &GormSeoMetaRepository{
		db: db,
	}
}

// GetByID lấy thông tin SeoMeta theo ID
func (r *GormSeoMetaRepository) GetByID(ctx context.Context, id uint) (*models.SeoMeta, error) {
	ctx, span := tracing.StartSeoSpan(ctx, "Repository.GetSeoMetaByID", tracing.SeoOpMetatagGeneration)
	defer span.End()

	startTime := time.Now()
	var meta models.SeoMeta

	result := r.db.WithContext(ctx).First(&meta, id)

	duration := time.Since(startTime).Milliseconds()
	span.SetAttributes(attribute.Int64("db.query_time_ms", duration))

	if result.Error != nil {
		tracing.RecordError(ctx, span, result.Error, fmt.Sprintf("Không tìm thấy SeoMeta với ID %d", id))
		return nil, result.Error
	}

	span.SetAttributes(
		attribute.Int64("seo.object_id", int64(meta.ObjectID)),
		attribute.String("seo.object_type", meta.ObjectType),
	)

	return &meta, nil
}

// GetByObject lấy thông tin SeoMeta theo object_id và object_type
func (r *GormSeoMetaRepository) GetByObject(ctx context.Context, objectID uint, objectType string) (*models.SeoMeta, error) {
	ctx, span := tracing.StartSeoSpan(ctx, "Repository.GetSeoMetaByObject", tracing.SeoOpMetatagGeneration)
	defer span.End()

	span.SetAttributes(
		attribute.Int64("seo.object_id", int64(objectID)),
		attribute.String("seo.object_type", objectType),
	)

	startTime := time.Now()
	var meta models.SeoMeta

	result := r.db.WithContext(ctx).Where("object_id = ? AND object_type = ?", objectID, objectType).First(&meta)

	duration := time.Since(startTime).Milliseconds()
	span.SetAttributes(attribute.Int64("db.query_time_ms", duration))

	if result.Error != nil {
		tracing.RecordError(ctx, span, result.Error,
			fmt.Sprintf("Không tìm thấy SeoMeta cho objectID=%d, objectType=%s", objectID, objectType))
		return nil, result.Error
	}

	span.SetAttributes(attribute.Int64("seo.meta_id", int64(meta.MetaID)))

	return &meta, nil
}

// Create tạo một bản ghi SeoMeta mới
func (r *GormSeoMetaRepository) Create(ctx context.Context, meta *models.SeoMeta) error {
	ctx, span := tracing.StartSeoSpan(ctx, "Repository.CreateSeoMeta", tracing.SeoOpMetatagGeneration)
	defer span.End()

	span.SetAttributes(
		attribute.Int64("seo.object_id", int64(meta.ObjectID)),
		attribute.String("seo.object_type", meta.ObjectType),
	)

	startTime := time.Now()
	result := r.db.WithContext(ctx).Create(meta)

	duration := time.Since(startTime).Milliseconds()
	span.SetAttributes(attribute.Int64("db.query_time_ms", duration))

	if result.Error != nil {
		tracing.RecordError(ctx, span, result.Error, "Không thể tạo SeoMeta")
		return result.Error
	}

	span.SetAttributes(attribute.Int64("seo.meta_id", int64(meta.MetaID)))

	return nil
}

// Update cập nhật thông tin SeoMeta
func (r *GormSeoMetaRepository) Update(ctx context.Context, meta *models.SeoMeta) error {
	ctx, span := tracing.StartSeoSpan(ctx, "Repository.UpdateSeoMeta", tracing.SeoOpMetatagGeneration)
	defer span.End()

	span.SetAttributes(
		attribute.Int64("seo.meta_id", int64(meta.MetaID)),
		attribute.Int64("seo.object_id", int64(meta.ObjectID)),
		attribute.String("seo.object_type", meta.ObjectType),
	)

	startTime := time.Now()
	result := r.db.WithContext(ctx).Save(meta)

	duration := time.Since(startTime).Milliseconds()
	span.SetAttributes(attribute.Int64("db.query_time_ms", duration))

	if result.Error != nil {
		tracing.RecordError(ctx, span, result.Error, fmt.Sprintf("Không thể cập nhật SeoMeta với ID %d", meta.MetaID))
		return result.Error
	}

	span.SetAttributes(attribute.Int64("db.rows_affected", result.RowsAffected))

	return nil
}

// Delete xóa SeoMeta theo ID
func (r *GormSeoMetaRepository) Delete(ctx context.Context, id uint) error {
	ctx, span := tracing.StartSeoSpan(ctx, "Repository.DeleteSeoMeta", tracing.SeoOpMetatagGeneration)
	defer span.End()

	span.SetAttributes(attribute.Int64("seo.meta_id", int64(id)))

	startTime := time.Now()
	result := r.db.WithContext(ctx).Delete(&models.SeoMeta{}, id)

	duration := time.Since(startTime).Milliseconds()
	span.SetAttributes(attribute.Int64("db.query_time_ms", duration))

	if result.Error != nil {
		tracing.RecordError(ctx, span, result.Error, fmt.Sprintf("Không thể xóa SeoMeta với ID %d", id))
		return result.Error
	}

	span.SetAttributes(attribute.Int64("db.rows_affected", result.RowsAffected))

	return nil
}

// DeleteByObject xóa SeoMeta theo object_id và object_type
func (r *GormSeoMetaRepository) DeleteByObject(ctx context.Context, objectID uint, objectType string) error {
	ctx, span := tracing.StartSeoSpan(ctx, "Repository.DeleteSeoMetaByObject", tracing.SeoOpMetatagGeneration)
	defer span.End()

	span.SetAttributes(
		attribute.Int64("seo.object_id", int64(objectID)),
		attribute.String("seo.object_type", objectType),
	)

	startTime := time.Now()
	result := r.db.WithContext(ctx).Where("object_id = ? AND object_type = ?", objectID, objectType).Delete(&models.SeoMeta{})

	duration := time.Since(startTime).Milliseconds()
	span.SetAttributes(attribute.Int64("db.query_time_ms", duration))

	if result.Error != nil {
		tracing.RecordError(ctx, span, result.Error,
			fmt.Sprintf("Không thể xóa SeoMeta với objectID=%d, objectType=%s", objectID, objectType))
		return result.Error
	}

	span.SetAttributes(attribute.Int64("db.rows_affected", result.RowsAffected))

	return nil
}

// List lấy danh sách SeoMeta có phân trang
func (r *GormSeoMetaRepository) List(ctx context.Context, limit, offset int) ([]models.SeoMeta, int64, error) {
	ctx, span := tracing.StartSeoSpan(ctx, "Repository.ListSeoMeta", tracing.SeoOpMetatagGeneration)
	defer span.End()

	span.SetAttributes(
		attribute.Int("db.limit", limit),
		attribute.Int("db.offset", offset),
	)

	var metaList []models.SeoMeta
	var total int64

	// Đếm tổng số bản ghi
	countStart := time.Now()
	countResult := r.db.WithContext(ctx).Model(&models.SeoMeta{}).Count(&total)
	countDuration := time.Since(countStart).Milliseconds()
	span.SetAttributes(attribute.Int64("db.count_query_time_ms", countDuration))

	if countResult.Error != nil {
		tracing.RecordError(ctx, span, countResult.Error, "Không thể đếm tổng số SeoMeta")
		return nil, 0, countResult.Error
	}

	// Truy vấn dữ liệu với phân trang
	queryStart := time.Now()
	result := r.db.WithContext(ctx).Limit(limit).Offset(offset).Order("meta_id DESC").Find(&metaList)
	queryDuration := time.Since(queryStart).Milliseconds()
	span.SetAttributes(attribute.Int64("db.query_time_ms", queryDuration))

	if result.Error != nil {
		tracing.RecordError(ctx, span, result.Error, "Không thể lấy danh sách SeoMeta")
		return nil, 0, result.Error
	}

	span.SetAttributes(
		attribute.Int64("db.total_records", total),
		attribute.Int("db.records_returned", len(metaList)),
	)

	return metaList, total, nil
}
