package tracing

import (
	"context"
	"fmt"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"

	"wnapi/pkg/tracing"
)

// SeoOpType là kiểu cho các loại thao tác SEO để đảm bảo nhất quán
type SeoOpType string

// Các loại thao tác SEO được định nghĩa trước
const (
	SeoOpMetatagGeneration SeoOpType = "metatag_generation"
	SeoOpSitemapGeneration SeoOpType = "sitemap_generation"
	SeoOpURLManagement     SeoOpType = "url_management"
	SeoOpRedirectHandling  SeoOpType = "redirect_handling"
	SeoOpSeoAnalysis       SeoOpType = "seo_analysis"
	SeoOpSearchEngineData  SeoOpType = "search_engine_data"
)

const (
	// Tên của tracer
	tracerName = "wnapi/modules/seo"
)

// StartSeoSpan bắt đầu một span mới cho một thao tác SEO
func StartSeoSpan(ctx context.Context, operationName string, opType SeoOpType) (context.Context, trace.Span) {
	tracer := otel.Tracer(tracerName)
	ctx, span := tracer.Start(ctx, operationName)

	// Đặt các thuộc tính chung
	span.SetAttributes(attribute.String("service.name", "seo-service"))
	span.SetAttributes(attribute.String("seo.type", string(opType)))

	return ctx, span
}

// StartSeoMetaSpan bắt đầu một span cho thao tác SEO meta
func StartSeoMetaSpan(ctx context.Context, operationName string, metaID uint, objectID uint, objectType string) (context.Context, trace.Span) {
	ctx, span := StartSeoSpan(ctx, operationName, SeoOpMetatagGeneration)

	// Đặt các thuộc tính riêng
	if metaID > 0 {
		span.SetAttributes(attribute.Int64("seo.meta_id", int64(metaID)))
	}
	if objectID > 0 {
		span.SetAttributes(attribute.Int64("seo.object_id", int64(objectID)))
	}
	if objectType != "" {
		span.SetAttributes(attribute.String("seo.object_type", objectType))
	}

	return ctx, span
}

// StartURLSpan bắt đầu một span cho thao tác liên quan URL
func StartURLSpan(ctx context.Context, operationName string, url string) (context.Context, trace.Span) {
	ctx, span := StartSeoSpan(ctx, operationName, SeoOpURLManagement)

	// Đặt thuộc tính URL
	if url != "" {
		span.SetAttributes(attribute.String("seo.url", url))
	}

	return ctx, span
}

// StartRedirectSpan bắt đầu một span cho thao tác chuyển hướng
func StartRedirectSpan(ctx context.Context, operationName string, redirectType string, sourceURL, targetURL string) (context.Context, trace.Span) {
	ctx, span := StartSeoSpan(ctx, operationName, SeoOpRedirectHandling)

	// Đặt thuộc tính cho chuyển hướng
	if redirectType != "" {
		span.SetAttributes(attribute.String("seo.redirect_type", redirectType))
	}
	if sourceURL != "" {
		span.SetAttributes(attribute.String("seo.source_url", sourceURL))
	}
	if targetURL != "" {
		span.SetAttributes(attribute.String("seo.target_url", targetURL))
	}

	return ctx, span
}

// StartSitemapSpan bắt đầu một span cho thao tác liên quan sitemap
func StartSitemapSpan(ctx context.Context, operationName string, sitemapID string) (context.Context, trace.Span) {
	ctx, span := StartSeoSpan(ctx, operationName, SeoOpSitemapGeneration)

	// Đặt thuộc tính sitemap
	if sitemapID != "" {
		span.SetAttributes(attribute.String("seo.sitemap_id", sitemapID))
	}

	return ctx, span
}

// RecordMetaPerformance ghi lại hiệu suất tạo meta tag
func RecordMetaPerformance(span trace.Span, duration float64, tagCount int) {
	span.SetAttributes(attribute.Float64("seo.meta_gen_duration_ms", duration))
	span.SetAttributes(attribute.Int("seo.meta_tag_count", tagCount))
}

// RecordSitemapPerformance ghi lại hiệu suất tạo sitemap
func RecordSitemapPerformance(span trace.Span, duration float64, urlCount int) {
	span.SetAttributes(attribute.Float64("seo.sitemap_gen_duration_ms", duration))
	span.SetAttributes(attribute.Int("seo.sitemap_url_count", urlCount))
}

// RecordError ghi lại lỗi trong span và đặt trạng thái của nó là Errored
func RecordError(ctx context.Context, span trace.Span, err error, message string) {
	if err != nil {
		errMsg := fmt.Sprintf("%s: %v", message, err)
		span.RecordError(err)
		span.SetStatus(codes.Error, errMsg)
		span.SetAttributes(attribute.String("error.message", errMsg))

		// Ghi log lỗi bằng cả tracing và logging
		tracing.LogError(ctx, errMsg)
	}
}
