package tracing

import (
	"context"
	"log"

	"wnapi/modules/seo/configs"
	"wnapi/pkg/tracing"
)

// ShutdownFunc là một hàm đóng tracer để dọn dẹp tài nguyên
type ShutdownFunc func(context.Context) error

// InitTracer khởi tạo OpenTelemetry tracer dựa trên cấu hình
// Trả về một hàm dọn dẹp mà nên được gọi khi shutdown
func InitTracer(cfg *configs.Config) (ShutdownFunc, error) {
	// Nếu tracing không được bật, trả về một hàm shutdown giả
	if !cfg.Tracing.Enabled {
		return func(ctx context.Context) error { return nil }, nil
	}

	// Lấy service name từ cấu hình
	serviceName := cfg.Tracing.ServiceName
	if serviceName == "" {
		serviceName = "seo-service"
	}

	var shutdown func(context.Context) error
	var err error

	// Khởi tạo exporter dựa trên loại đượ<PERSON> c<PERSON><PERSON> hình
	switch cfg.Tracing.ExporterType {
	case "signoz":
		// Khởi tạo SigNoz tracer
		endpoint := cfg.Tracing.SigNoz.Endpoint
		shutdown, err = tracing.InitSignozTracer(serviceName, endpoint)
		if err != nil {
			return nil, err
		}
		log.Printf("SigNoz tracer initialized for SEO module with endpoint: %s", endpoint)

	case "jaeger":
		// Khởi tạo Jaeger tracer
		host := cfg.Tracing.Jaeger.Host
		port := cfg.Tracing.Jaeger.Port
		shutdown, err = tracing.InitJaeger(serviceName, host, port)
		if err != nil {
			return nil, err
		}
		log.Printf("Jaeger tracer initialized for SEO module with endpoint: %s:%s", host, port)

	default:
		log.Printf("Unknown exporter type: %s, defaulting to SigNoz", cfg.Tracing.ExporterType)
		endpoint := cfg.Tracing.SigNoz.Endpoint
		shutdown, err = tracing.InitSignozTracer(serviceName, endpoint)
		if err != nil {
			return nil, err
		}
	}

	log.Printf("Tracing initialized for SEO module with service name: %s", serviceName)
	return shutdown, nil
}
