package tracing

import (
	"fmt"
	"log"

	coreTr "wnapi/internal/pkg/tracing"
	"wnapi/modules/website/internal"
)

// Global cleanup function
var cleanup func()

// InitTracing initializes the tracing system
func InitTracing(cfg *internal.TracingConfig) error {
	// If tracing is disabled, return without initializing
	if !cfg.Enabled {
		log.Println("Tracing is disabled")
		return nil
	}

	serviceName := "website-module"
	var err error

	// Jaeger configuration
	if cfg.Jaeger.Host != "" {
		jaegerTracer, jaegerCloser, err := coreTr.InitJaeger(serviceName, cfg.Jaeger.Host, cfg.Jaeger.Port)
		if err != nil {
			return fmt.Errorf("could not initialize jaeger tracer: %w", err)
		}

		// Store the cleanup function
		cleanup = func() {
			if jaegerCloser != nil {
				jaegerCloser.Close()
			}
		}

		log.Printf("Jaeger tracer initialized for service %s", serviceName)
		return nil
	}

	// Signoz configuration
	if cfg.Signoz.Endpoint != "" {
		signozCleanup, err := coreTr.InitSignozTracer(serviceName, cfg.Signoz.Endpoint)
		if err != nil {
			return fmt.Errorf("could not initialize signoz tracer: %w", err)
		}

		// Store the cleanup function
		cleanup = signozCleanup
		log.Printf("Signoz tracer initialized for service %s", serviceName)
		return nil
	}

	return fmt.Errorf("no tracing provider configured")
}

// Cleanup performs any necessary cleanup for the tracing system
func Cleanup() {
	if cleanup != nil {
		cleanup()
	}
}
