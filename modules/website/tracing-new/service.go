// filepath: /Users/<USER>/Desktop/Workspace/Webnew/wnapi/modules/website/tracing/service.go
package tracing

import (
	"context"
	"fmt"
	"time"

	"wnapi/modules/website/internal"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// TraceService wraps a service method call with tracing
func TraceService(ctx context.Context, cfg *internal.TracingConfig, serviceName, methodName string, f func(context.Context) error) error {
	if cfg == nil || !cfg.Enabled {
		return f(ctx)
	}

	tracer := otel.GetTracerProvider().Tracer("website-service")
	ctx, span := tracer.Start(
		ctx,
		fmt.Sprintf("%s.%s", serviceName, methodName),
		trace.WithAttributes(
			attribute.String("service.name", serviceName),
			attribute.String("service.method", methodName),
		),
	)
	defer span.End()

	start := time.Now()
	err := f(ctx)
	elapsed := time.Since(start)

	// Record duration
	span.SetAttributes(attribute.Int64("duration_ms", elapsed.Milliseconds()))

	// Record error if any
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
	} else {
		span.SetStatus(codes.Ok, "")
	}

	return err
}

// TraceServiceWithResult is similar to TraceService but handles methods that return a value
func TraceServiceWithResult[T any](ctx context.Context, cfg *internal.TracingConfig, serviceName, methodName string, f func(context.Context) (T, error)) (T, error) {
	var result T
	if cfg == nil || !cfg.Enabled {
		return f(ctx)
	}

	tracer := otel.GetTracerProvider().Tracer("website-service")
	ctx, span := tracer.Start(
		ctx,
		fmt.Sprintf("%s.%s", serviceName, methodName),
		trace.WithAttributes(
			attribute.String("service.name", serviceName),
			attribute.String("service.method", methodName),
		),
	)
	defer span.End()

	start := time.Now()
	result, err := f(ctx)
	elapsed := time.Since(start)

	// Record duration
	span.SetAttributes(attribute.Int64("duration_ms", elapsed.Milliseconds()))

	// Record error if any
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
	} else {
		span.SetStatus(codes.Ok, "")
	}

	return result, err
}

// TraceRepository wraps a repository method call with tracing
func TraceRepository(ctx context.Context, cfg *internal.TracingConfig, repoName, methodName string, f func(context.Context) error) error {
	if cfg == nil || !cfg.Enabled {
		return f(ctx)
	}

	tracer := otel.GetTracerProvider().Tracer("website-repository")
	ctx, span := tracer.Start(
		ctx,
		fmt.Sprintf("%s.%s", repoName, methodName),
		trace.WithAttributes(
			attribute.String("repository.name", repoName),
			attribute.String("repository.method", methodName),
		),
	)
	defer span.End()

	start := time.Now()
	err := f(ctx)
	elapsed := time.Since(start)

	// Record duration
	span.SetAttributes(attribute.Int64("duration_ms", elapsed.Milliseconds()))

	// Record error if any
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
	} else {
		span.SetStatus(codes.Ok, "")
	}

	return err
}

// TraceRepositoryWithResult is similar to TraceRepository but handles methods that return a value
func TraceRepositoryWithResult[T any](ctx context.Context, cfg *internal.TracingConfig, repoName, methodName string, f func(context.Context) (T, error)) (T, error) {
	var result T
	if cfg == nil || !cfg.Enabled {
		return f(ctx)
	}

	tracer := otel.GetTracerProvider().Tracer("website-repository")
	ctx, span := tracer.Start(
		ctx,
		fmt.Sprintf("%s.%s", repoName, methodName),
		trace.WithAttributes(
			attribute.String("repository.name", repoName),
			attribute.String("repository.method", methodName),
		),
	)
	defer span.End()

	start := time.Now()
	result, err := f(ctx)
	elapsed := time.Since(start)

	// Record duration
	span.SetAttributes(attribute.Int64("duration_ms", elapsed.Milliseconds()))

	// Record error if any
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
	} else {
		span.SetStatus(codes.Ok, "")
	}

	return result, err
}

// TraceWebsiteOperation wraps an operation on a specific website with tracing
func TraceWebsiteOperation(ctx context.Context, cfg *internal.TracingConfig, operation string, websiteID int, f func(context.Context) error) error {
	if cfg == nil || !cfg.Enabled {
		return f(ctx)
	}

	tracer := otel.GetTracerProvider().Tracer("website-operation")
	ctx, span := tracer.Start(
		ctx,
		fmt.Sprintf("Website.%s", operation),
		trace.WithAttributes(
			attribute.String("operation", operation),
			attribute.Int("website.id", websiteID),
		),
	)
	defer span.End()

	start := time.Now()
	err := f(ctx)
	elapsed := time.Since(start)

	// Record duration
	span.SetAttributes(attribute.Int64("duration_ms", elapsed.Milliseconds()))

	// Record error if any
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
	} else {
		span.SetStatus(codes.Ok, "")
	}

	return err
}

// TraceWebsiteOperationWithResult is similar to TraceWebsiteOperation but handles operations that return a value
func TraceWebsiteOperationWithResult[T any](ctx context.Context, cfg *internal.TracingConfig, operation string, websiteID int, f func(context.Context) (T, error)) (T, error) {
	var result T
	if cfg == nil || !cfg.Enabled {
		return f(ctx)
	}

	tracer := otel.GetTracerProvider().Tracer("website-operation")
	ctx, span := tracer.Start(
		ctx,
		fmt.Sprintf("Website.%s", operation),
		trace.WithAttributes(
			attribute.String("operation", operation),
			attribute.Int("website.id", websiteID),
		),
	)
	defer span.End()

	start := time.Now()
	result, err := f(ctx)
	elapsed := time.Since(start)

	// Record duration
	span.SetAttributes(attribute.Int64("duration_ms", elapsed.Milliseconds()))

	// Record error if any
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
	} else {
		span.SetStatus(codes.Ok, "")
	}

	return result, err
}
