package response

import (
	"time"

	"wnapi/modules/site/models"
)

// PageResponse đại diện cho response page
type PageResponse struct {
	ID              int              `json:"id"`
	WebsiteID       int              `json:"website_id"`
	Title           string           `json:"title"`
	Slug            string           `json:"slug"`
	Content         models.Content   `json:"content,omitempty"`
	Layout          *string          `json:"layout,omitempty"`
	MetaTitle       *string          `json:"meta_title,omitempty"`
	MetaDescription *string          `json:"meta_description,omitempty"`
	IsHomepage      bool             `json:"is_homepage"`
	Status          string           `json:"status"`
	PublishedAt     *time.Time       `json:"published_at,omitempty"`
	CreatedAt       time.Time        `json:"created_at"`
	UpdatedAt       time.Time        `json:"updated_at"`
	Website         *WebsiteResponse `json:"website,omitempty"`
}

// PageListResponse đại diện cho response danh sách page
type PageListResponse struct {
	Pages      []PageResponse `json:"pages"`
	NextCursor string         `json:"next_cursor"`
	Has<PERSON><PERSON>    bool           `json:"has_more"`
}
