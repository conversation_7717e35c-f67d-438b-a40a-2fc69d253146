// filepath: /Users/<USER>/Desktop/Workspace/Webnew/wnapi/modules/website/tracing/middleware.go
package tracing

import (
	"fmt"
	"net/http"
	"time"

	"wnapi/modules/website/internal"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/trace"
)

// TracingMiddleware wraps Gin handlers with OpenTelemetry tracing
type TracingMiddleware struct {
	config *internal.TracingConfig
}

// NewTracingMiddleware creates a new tracing middleware
func NewTracingMiddleware(config *internal.TracingConfig) *TracingMiddleware {
	return &TracingMiddleware{
		config: config,
	}
}

// TraceRequest is a middleware that traces HTTP requests
func (m *TracingMiddleware) TraceRequest() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip tracing if disabled
		if m.config == nil || !m.config.Enabled {
			c.Next()
			return
		}

		// Extract trace context from incoming request
		propagator := propagation.TraceContext{}
		ctx := propagator.Extract(c.Request.Context(), propagation.HeaderCarrier(c.Request.Header))

		// Start a new span
		tracer := otel.GetTracerProvider().Tracer("website-http")
		spanName := fmt.Sprintf("%s %s", c.Request.Method, c.Request.URL.Path)
		ctx, span := tracer.Start(
			ctx,
			spanName,
			trace.WithSpanKind(trace.SpanKindServer),
		)
		defer span.End()

		// Set basic span attributes from the request
		span.SetAttributes(
			attribute.String("http.method", c.Request.Method),
			attribute.String("http.url", c.Request.URL.String()),
			attribute.String("http.host", c.Request.Host),
			attribute.String("http.user_agent", c.Request.UserAgent()),
		)

		// If there is a user ID in the context, add it to the span
		if userID, exists := c.Get("user_id"); exists {
			span.SetAttributes(attribute.String("user.id", fmt.Sprintf("%v", userID)))
		}

		// Store the context with the span in the gin context
		c.Request = c.Request.WithContext(ctx)

		// Process the request
		start := time.Now()
		c.Next()
		latency := time.Since(start)

		// Set response attributes
		span.SetAttributes(
			attribute.Int("http.status_code", c.Writer.Status()),
			attribute.Int64("http.response_time_ms", latency.Milliseconds()),
			attribute.Int("http.response_size", c.Writer.Size()),
		)

		// If there was an error, record it
		for _, err := range c.Errors {
			span.RecordError(err.Err)
			span.SetStatus(codes.Error, err.Error())
		}

		// If status code is 4xx or 5xx, consider it an error
		statusCode := c.Writer.Status()
		if statusCode >= 400 {
			span.SetStatus(codes.Error, fmt.Sprintf("HTTP %d: %s", statusCode, http.StatusText(statusCode)))
			if statusCode >= 500 {
				span.SetAttributes(attribute.Bool("error", true))
			}
		} else {
			span.SetStatus(codes.Ok, "")
		}
	}
}
