package handlers

import (
	"net/http"
	"strconv"

	"wnapi/modules/website/models"
	"wnapi/modules/website/service"

	"github.com/gin-gonic/gin"
)

// ThemeHandler xử lý các request liên quan đến theme
type ThemeHandler struct {
	themeService service.ThemeService
}

// NewThemeHandler tạo mới theme handler
func NewThemeHandler(themeService service.ThemeService) *ThemeHandler {
	return &ThemeHandler{
		themeService: themeService,
	}
}

// ListThemes lấy danh sách themes
func (h *ThemeHandler) ListThemes(c *gin.Context) {
	limit := 10
	cursor := c.Query("cursor")

	themes, nextCursor, hasMore, err := h.themeService.GetThemes(limit, cursor)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusInternalServerError, "<PERSON>hông thể lấy danh sách themes", "THEME_LIST_ERROR", details)
		return
	}

	meta := map[string]interface{}{
		"next_cursor": nextCursor,
		"has_more":    hasMore,
	}
	apiSuccessWithMeta(c, http.StatusOK, "Lấy danh sách themes thành công", themes, meta)
}

// ListPublicThemes lấy danh sách public themes
func (h *ThemeHandler) ListPublicThemes(c *gin.Context) {
	limit := 10
	cursor := c.Query("cursor")

	themes, nextCursor, hasMore, err := h.themeService.GetPublicThemes(limit, cursor)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusInternalServerError, "Không thể lấy danh sách public themes", "THEME_LIST_ERROR", details)
		return
	}

	meta := map[string]interface{}{
		"next_cursor": nextCursor,
		"has_more":    hasMore,
	}
	apiSuccessWithMeta(c, http.StatusOK, "Lấy danh sách public themes thành công", themes, meta)
}

// GetTheme lấy theme theo ID
func (h *ThemeHandler) GetTheme(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "ID phải là số nguyên"}}
		apiErrorWithDetails(c, http.StatusBadRequest, "ID không hợp lệ", "INVALID_ID", details)
		return
	}

	theme, err := h.themeService.GetThemeByID(id)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusNotFound, "Theme không tồn tại", "THEME_NOT_FOUND", details)
		return
	}

	apiSuccess(c, http.StatusOK, "Lấy theme thành công", theme)
}

// CreateTheme tạo mới theme
func (h *ThemeHandler) CreateTheme(c *gin.Context) {
	var theme models.Theme
	if err := c.ShouldBindJSON(&theme); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "INVALID_DATA", details)
		return
	}

	newTheme, err := h.themeService.CreateTheme(&theme)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusInternalServerError, "Không thể tạo theme", "THEME_CREATE_ERROR", details)
		return
	}

	apiSuccess(c, http.StatusCreated, "Tạo theme thành công", newTheme)
}

// UpdateTheme cập nhật theme
func (h *ThemeHandler) UpdateTheme(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "ID phải là số nguyên"}}
		apiErrorWithDetails(c, http.StatusBadRequest, "ID không hợp lệ", "INVALID_ID", details)
		return
	}

	var theme models.Theme
	if err := c.ShouldBindJSON(&theme); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "INVALID_DATA", details)
		return
	}

	updatedTheme, err := h.themeService.UpdateTheme(id, &theme)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusInternalServerError, "Không thể cập nhật theme", "THEME_UPDATE_ERROR", details)
		return
	}

	apiSuccess(c, http.StatusOK, "Cập nhật theme thành công", updatedTheme)
}

// DeleteTheme xóa theme
func (h *ThemeHandler) DeleteTheme(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "ID phải là số nguyên"}}
		apiErrorWithDetails(c, http.StatusBadRequest, "ID không hợp lệ", "INVALID_ID", details)
		return
	}

	err = h.themeService.DeleteTheme(id)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusInternalServerError, "Không thể xóa theme", "THEME_DELETE_ERROR", details)
		return
	}

	apiSuccess(c, http.StatusOK, "Xóa theme thành công", nil)
}
