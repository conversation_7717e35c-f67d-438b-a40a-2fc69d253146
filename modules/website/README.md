# Website Module

## Overview
The Website module provides functionality for managing websites, pages, themes, and templates within the application.

## Configuration
Configuration for the Website module is loaded from environment variables with the prefix `WEBSITE_`. Here are the available configuration options:

```env
# Website Module Configuration
WEBSITE_SITE_NAME=My Website
WEBSITE_SITE_DESCRIPTION=Website description
WEBSITE_CONTACT_EMAIL=<EMAIL>
WEBSITE_MAX_MENU_ITEMS=10
WEBSITE_MAX_BANNERS=5
WEBSITE_ENABLE_CONTACT_FORM=true
WEBSITE_CACHE_TIMEOUT_MINUTES=10
WEBSITE_MAX_PAGE_SIZE=100
WEBSITE_DEFAULT_PAGE_SIZE=10
WEBSITE_MESSAGE=Xin chào từ module Website!

# Tracing Configuration
WEBSITE_TRACING_ENABLED=false
WEBSITE_TRACING_JAEGER_HOST=localhost
WEBSITE_TRACING_JAEGER_PORT=14268
WEBSITE_TRACING_SIGNOZ_ENDPOINT=http://localhost:4317
```

## API Endpoints

### Website Endpoints
- `GET /api/v1/website/websites` - List all websites
- `POST /api/v1/website/websites` - Create a new website
- `GET /api/v1/website/websites/:id` - Get website by ID
- `PUT /api/v1/website/websites/:id` - Update website
- `DELETE /api/v1/website/websites/:id` - Delete website

### Page Endpoints
- `GET /api/v1/website/website-pages/:website_id` - List all pages for a website
- `POST /api/v1/website/website-pages/:website_id` - Create a new page
- `GET /api/v1/website/website-pages/:website_id/page/:id` - Get page by ID
- `PUT /api/v1/website/website-pages/:website_id/page/:id` - Update page
- `DELETE /api/v1/website/website-pages/:website_id/page/:id` - Delete page
- `GET /api/v1/website/website-pages/:website_id/homepage` - Get homepage
- `GET /api/v1/website/website-pages/:website_id/by-slug/:slug` - Get page by slug

### Theme Endpoints
- `GET /api/v1/website/themes` - List all themes
- `POST /api/v1/website/themes` - Create a new theme
- `GET /api/v1/website/themes/:id` - Get theme by ID
- `PUT /api/v1/website/themes/:id` - Update theme
- `DELETE /api/v1/website/themes/:id` - Delete theme
- `GET /api/v1/website/themes/public` - List public themes

### Template Endpoints
- `GET /api/v1/website/theme-templates/:theme_id` - List all templates for a theme
- `POST /api/v1/website/theme-templates/:theme_id` - Create a new template
- `GET /api/v1/website/theme-templates/:theme_id/template/:id` - Get template by ID
- `PUT /api/v1/website/theme-templates/:theme_id/template/:id` - Update template
- `DELETE /api/v1/website/theme-templates/:theme_id/template/:id` - Delete template
- `GET /api/v1/website/theme-templates/:theme_id/by-type/:type` - List templates by type

## Database Structure

The module uses the following database tables:

- `website_websites` - Stores website information
- `website_pages` - Stores page content for websites
- `website_themes` - Stores theme information
- `website_templates` - Stores templates for themes

## Usage Examples

### Creating a new website
```go
websiteService := app.GetService("website").(service.WebsiteService)
result, err := websiteService.CreateWebsite(ctx, tenantID, dto.CreateWebsiteRequest{
    Name: "My New Website",
    Subdomain: "mynewsite",
    Description: "This is my new website",
})
```

### Creating a new page
```go
pageService := app.GetService("website").(service.PageService)
result, err := pageService.CreatePage(ctx, websiteID, dto.CreatePageRequest{
    Title: "Home Page",
    Content: "<h1>Welcome to my website</h1>",
    Published: true,
})
```

## Error Handling

The module uses the standard error handling approach with `ServiceError` types that are mapped to appropriate HTTP status codes and error messages.

## Dependencies

- Core module
- Database module
- Logger package
