package service

import (
	"context"
	"errors"
	"time"
	"wnapi/modules/website/dto/request"
	"wnapi/modules/website/models"
)

// Đ<PERSON>nh nghĩa các lỗi
var (
	ErrTemplateNotFound = errors.New("template không tồn tại")
	ErrThemeRequired    = errors.New("theme ID là bắt buộc")
)

// TemplateService định nghĩa service xử lý template
type TemplateService interface {
	GetTemplates(themeID int, limit int, cursor string) ([]models.Template, string, bool, error)
	GetTemplatesByType(themeID int, templateType string, limit int, cursor string) ([]models.Template, string, bool, error)
	GetTemplateByID(id int) (*models.Template, error)
	CreateTemplate(template *models.Template) (*models.Template, error)
	UpdateTemplate(id int, template *models.Template) (*models.Template, error)
	DeleteTemplate(id int) error
}

type templateService struct {
	templateRepo repository.TemplateRepository
	themeRepo    repository.ThemeRepository
}

// NewTemplateService tạo mới template service
func NewTemplateService(templateRepo repository.TemplateRepository, themeRepo repository.ThemeRepository) TemplateService {
	return &templateService{
		templateRepo: templateRepo,
		themeRepo:    themeRepo,
	}
}

// GetTemplates lấy danh sách templates với phân trang
func (s *templateService) GetTemplates(themeID int, limit int, cursor string) ([]models.Template, string, bool, error) {
	if themeID <= 0 {
		return nil, "", false, ErrThemeRequired
	}

	req := request.ListTemplateRequest{
		Limit:  limit,
		Cursor: cursor,
	}

	templates, nextCursor, hasMore, err := s.templateRepo.List(context.Background(), themeID, req)
	if err != nil {
		return nil, "", false, err
	}

	// Chuyển đổi []*models.Template thành []models.Template
	result := make([]models.Template, len(templates))
	for i, template := range templates {
		result[i] = *template
	}

	return result, nextCursor, hasMore, nil
}

// GetTemplatesByType lấy danh sách templates theo loại với phân trang
func (s *templateService) GetTemplatesByType(themeID int, templateType string, limit int, cursor string) ([]models.Template, string, bool, error) {
	if themeID <= 0 {
		return nil, "", false, ErrThemeRequired
	}

	req := request.ListTemplateRequest{
		Limit:  limit,
		Cursor: cursor,
	}

	templates, nextCursor, hasMore, err := s.templateRepo.ListByType(context.Background(), themeID, templateType, req)
	if err != nil {
		return nil, "", false, err
	}

	// Chuyển đổi []*models.Template thành []models.Template
	result := make([]models.Template, len(templates))
	for i, template := range templates {
		result[i] = *template
	}

	return result, nextCursor, hasMore, nil
}

// GetTemplateByID lấy template theo ID
func (s *templateService) GetTemplateByID(id int) (*models.Template, error) {
	// Vì repository yêu cầu themeID, nhưng trong trường hợp này chúng ta chỉ có templateID
	// Chúng ta sẽ truyền 0 làm themeID và hy vọng repository sẽ xử lý đúng
	// Hoặc có thể điều chỉnh repository để hỗ trợ tìm kiếm chỉ bằng templateID
	template, err := s.templateRepo.GetByID(context.Background(), 0, id)
	if err != nil {
		return nil, err
	}
	if template == nil {
		return nil, ErrTemplateNotFound
	}

	// Lấy thông tin theme
	theme, err := s.themeRepo.GetByID(context.Background(), template.ThemeID)
	if err == nil && theme != nil {
		template.Theme = theme
	}

	return template, nil
}

// CreateTemplate tạo mới template
func (s *templateService) CreateTemplate(template *models.Template) (*models.Template, error) {
	// Kiểm tra theme tồn tại
	if template.ThemeID <= 0 {
		return nil, ErrThemeRequired
	}
	_, err := s.themeRepo.GetByID(context.Background(), template.ThemeID)
	if err != nil {
		return nil, err
	}

	// Set thời gian
	now := time.Now()
	template.CreatedAt = now
	template.UpdatedAt = now

	// Lưu vào repository
	err = s.templateRepo.Create(context.Background(), template)
	if err != nil {
		return nil, err
	}

	// Lấy template mới tạo
	return s.GetTemplateByID(template.TemplateID)
}

// UpdateTemplate cập nhật template
func (s *templateService) UpdateTemplate(id int, template *models.Template) (*models.Template, error) {
	// Kiểm tra tồn tại
	existingTemplate, err := s.GetTemplateByID(id)
	if err != nil {
		return nil, err
	}

	// Kiểm tra theme
	if template.ThemeID <= 0 {
		template.ThemeID = existingTemplate.ThemeID
	} else if template.ThemeID != existingTemplate.ThemeID {
		_, err := s.themeRepo.GetByID(context.Background(), template.ThemeID)
		if err != nil {
			return nil, err
		}
	}

	// Cập nhật thông tin
	template.TemplateID = id
	template.CreatedAt = existingTemplate.CreatedAt
	template.UpdatedAt = time.Now()

	// Lưu vào repository
	err = s.templateRepo.Update(context.Background(), template)
	if err != nil {
		return nil, err
	}

	// Lấy template mới cập nhật
	return s.GetTemplateByID(id)
}

// DeleteTemplate xóa template
func (s *templateService) DeleteTemplate(id int) error {
	// Kiểm tra tồn tại
	existingTemplate, err := s.GetTemplateByID(id)
	if err != nil {
		return err
	}

	// Xóa template
	return s.templateRepo.Delete(context.Background(), existingTemplate.ThemeID, id)
}
