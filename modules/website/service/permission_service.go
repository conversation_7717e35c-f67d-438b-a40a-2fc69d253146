package service

import (
	"wnapi/pkg/middleware"

	"github.com/gin-gonic/gin"
)

// MockPermissionService is a temporary implementation of PermissionService
type MockPermissionService struct{}

// NewMockPermissionService creates a new instance of MockPermissionService
func NewMockPermissionService() middleware.PermissionService {
	return &MockPermissionService{}
}

// CheckPermission implements the PermissionService interface
// This is a temporary implementation that always returns true
func (s *MockPermissionService) CheckPermission(ctx *gin.Context, tenantID, userID uint, permission string) (bool, error) {
	// TODO: Implement actual permission checking or integrate with tenant module
	return true, nil
}
