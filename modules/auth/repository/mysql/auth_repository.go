package mysql

import (
	"context"
	"errors"
	"fmt"
	"time"

	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/internal"
	"wnapi/modules/auth/models"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// mysqlRepository triển khai Repository interface sử dụng GORM
type mysqlRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewMySQLRepository tạo một repository mới
func NewMySQLRepository(dbManager *database.Manager, logger logger.Logger) (internal.Repository, error) {
	if dbManager == nil {
		return nil, errors.New("database manager không được để trống")
	}

	sqlxDB := dbManager.GetDB()
	if sqlxDB == nil {
		return nil, errors.New("không thể kết nối tới cơ sở dữ liệu")
	}

	// Create GORM DB from sqlx DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlxDB.DB,
	}), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to initialize GORM: %w", err)
	}

	return &mysqlRepository{
		db:     gormDB,
		logger: logger,
	}, nil
}

// NewMySQLRepositoryWithDB tạo một repository mới từ gorm.DB
func NewMySQLRepositoryWithDB(db *gorm.DB, logger logger.Logger) (internal.Repository, error) {
	if db == nil {
		return nil, errors.New("gorm DB không được để trống")
	}

	return &mysqlRepository{
		db:     db,
		logger: logger,
	}, nil
}

// CreateUser tạo người dùng mới
func (r *mysqlRepository) CreateUser(ctx context.Context, user *internal.User, password string) error {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "users")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "users"),
		attribute.String("db.operation", "create_user"),
		attribute.String("auth.username", user.Username),
		attribute.String("auth.email", user.Email),
	)

	// Hash mật khẩu
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		tracing.RecordError(ctx, err)
		return err
	}

	user.PasswordHash = string(hashedPassword)
	user.CreatedAt = time.Now()
	user.UpdatedAt = time.Now()

	// Sử dụng GORM để tạo người dùng
	tx := r.db.WithContext(ctx).Create(&models.User{
		TenantID:        user.TenantID,
		Username:        user.Username,
		Email:           user.Email,
		PasswordHash:    user.PasswordHash,
		FullName:        user.FullName,
		UserType:        models.ParseUserType(user.UserType),
		Status:          models.UserStatus(user.Status),
		IsEmailVerified: user.IsEmailVerified,
		CreatedAt:       user.CreatedAt,
		UpdatedAt:       user.UpdatedAt,
	})

	if tx.Error != nil {
		r.logger.Error("Không thể tạo người dùng", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return tx.Error
	}

	// Lấy ID được tạo
	user.UserID = uint(tx.RowsAffected)
	tracing.AddSpanAttributes(ctx, attribute.Int64("auth.user_id", int64(user.UserID)))

	return nil
}

// GetUserByID lấy thông tin người dùng theo ID
func (r *mysqlRepository) GetUserByID(ctx context.Context, id int) (*internal.User, error) {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "users")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "users"),
		attribute.String("db.operation", "get_user_by_id"),
		attribute.Int("auth.user_id", id),
	)

	var userModel models.User
	tx := r.db.WithContext(ctx).Where("user_id = ?", id).First(&userModel)

	if tx.Error != nil {
		if errors.Is(tx.Error, gorm.ErrRecordNotFound) {
			tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", false))
			return nil, internal.ErrUserNotFound
		}
		r.logger.Error("Không thể lấy thông tin người dùng", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return nil, tx.Error
	}

	// Chuyển đổi model thành internal.User
	user := &internal.User{
		UserID:          userModel.UserID,
		TenantID:        userModel.TenantID,
		Username:        userModel.Username,
		Email:           userModel.Email,
		PasswordHash:    userModel.PasswordHash,
		FullName:        userModel.FullName,
		CreatedAt:       userModel.CreatedAt,
		UpdatedAt:       userModel.UpdatedAt,
		LastLogin:       userModel.LastLogin,
		Status:          string(userModel.Status),
		IsEmailVerified: userModel.IsEmailVerified,
		UserType:        userModel.UserType.String(),
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Bool("db.record_found", true),
		attribute.Int("auth.user_id", int(user.UserID)),
		attribute.String("auth.email", user.Email),
	)

	return user, nil
}

// GetUserByUsername lấy thông tin người dùng theo tên đăng nhập
func (r *mysqlRepository) GetUserByUsername(ctx context.Context, username string) (*internal.User, error) {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "users")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "users"),
		attribute.String("db.operation", "get_user_by_username"),
		attribute.String("auth.username", username),
	)

	var userModel models.User
	tx := r.db.WithContext(ctx).Where("username = ?", username).First(&userModel)

	if tx.Error != nil {
		if errors.Is(tx.Error, gorm.ErrRecordNotFound) {
			tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", false))
			return nil, internal.ErrUserNotFound
		}
		r.logger.Error("Không thể lấy thông tin người dùng", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return nil, tx.Error
	}

	// Chuyển đổi model thành internal.User
	user := &internal.User{
		UserID:          userModel.UserID,
		TenantID:        userModel.TenantID,
		Username:        userModel.Username,
		Email:           userModel.Email,
		PasswordHash:    userModel.PasswordHash,
		FullName:        userModel.FullName,
		CreatedAt:       userModel.CreatedAt,
		UpdatedAt:       userModel.UpdatedAt,
		LastLogin:       userModel.LastLogin,
		Status:          string(userModel.Status),
		IsEmailVerified: userModel.IsEmailVerified,
		UserType:        userModel.UserType.String(),
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Bool("db.record_found", true),
		attribute.Int("auth.user_id", int(user.UserID)),
		attribute.String("auth.email", user.Email),
	)

	return user, nil
}

// GetUserByEmail lấy thông tin người dùng theo email
func (r *mysqlRepository) GetUserByEmail(ctx context.Context, email string) (*internal.User, error) {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "users")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "users"),
		attribute.String("db.operation", "get_user_by_email"),
		attribute.String("auth.email", email),
	)

	var userModel models.User
	tx := r.db.WithContext(ctx).Where("email = ?", email).First(&userModel)

	if tx.Error != nil {
		if errors.Is(tx.Error, gorm.ErrRecordNotFound) {
			tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", false))
			return nil, internal.ErrUserNotFound
		}
		r.logger.Error("Không thể lấy thông tin người dùng", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return nil, tx.Error
	}

	// Chuyển đổi model thành internal.User
	user := &internal.User{
		UserID:          userModel.UserID,
		TenantID:        userModel.TenantID,
		Username:        userModel.Username,
		Email:           userModel.Email,
		PasswordHash:    userModel.PasswordHash,
		FullName:        userModel.FullName,
		CreatedAt:       userModel.CreatedAt,
		UpdatedAt:       userModel.UpdatedAt,
		LastLogin:       userModel.LastLogin,
		Status:          string(userModel.Status),
		IsEmailVerified: userModel.IsEmailVerified,
		UserType:        userModel.UserType.String(),
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Bool("db.record_found", true),
		attribute.Int("auth.user_id", int(user.UserID)),
		attribute.String("auth.username", user.Username),
	)

	return user, nil
}

// UpdateUser cập nhật thông tin người dùng
func (r *mysqlRepository) UpdateUser(ctx context.Context, user *internal.User) error {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "users")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "users"),
		attribute.String("db.operation", "update_user"),
		attribute.Int("auth.user_id", int(user.UserID)),
		attribute.String("auth.username", user.Username),
		attribute.String("auth.email", user.Email),
	)

	user.UpdatedAt = time.Now()

	// Sử dụng GORM để cập nhật
	userModel := models.User{
		UserID:          user.UserID,
		TenantID:        user.TenantID,
		Username:        user.Username,
		Email:           user.Email,
		PasswordHash:    user.PasswordHash,
		FullName:        user.FullName,
		UpdatedAt:       user.UpdatedAt,
		Status:          models.UserStatus(user.Status),
		IsEmailVerified: user.IsEmailVerified,
		UserType:        models.ParseUserType(user.UserType),
	}

	tx := r.db.WithContext(ctx).Model(&models.User{}).Where("user_id = ?", user.UserID).Updates(userModel)

	if tx.Error != nil {
		r.logger.Error("Không thể cập nhật người dùng", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return tx.Error
	}

	return nil
}

// DeleteUser xóa người dùng
func (r *mysqlRepository) DeleteUser(ctx context.Context, id int) error {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "users")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "users"),
		attribute.String("db.operation", "delete_user"),
		attribute.Int("auth.user_id", id),
	)

	tx := r.db.WithContext(ctx).Delete(&models.User{}, id)

	if tx.Error != nil {
		r.logger.Error("Không thể xóa người dùng", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return tx.Error
	}

	return nil
}

// CreateToken tạo token mới (sử dụng auth_sessions table)
func (r *mysqlRepository) CreateToken(ctx context.Context, token *internal.Token) error {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "auth_sessions")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_sessions"),
		attribute.String("db.operation", "create_token"),
		attribute.Int("auth.user_id", token.UserID),
		attribute.String("auth.token_type", string(token.TokenType)),
	)

	// Nếu ID token chưa được thiết lập, tạo ID mới
	if token.ID == "" {
		token.ID = uuid.New().String()
	}

	token.CreatedAt = time.Now()
	token.UpdatedAt = time.Now()

	// Sử dụng GORM để tạo token
	sessionModel := models.AuthSession{
		ID:           token.ID,
		UserID:       uint(token.UserID),
		AccessToken:  token.AccessToken,
		RefreshToken: token.RefreshToken,
		ExpiresAt:    token.ExpiresAt,
		CreatedAt:    token.CreatedAt,
		UpdatedAt:    token.UpdatedAt,
	}

	tx := r.db.WithContext(ctx).Create(&sessionModel)

	if tx.Error != nil {
		r.logger.Error("Không thể tạo token", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return tx.Error
	}

	// Che giấu thông tin nhạy cảm trong span
	if len(token.RefreshToken) > 8 {
		maskedToken := token.RefreshToken[0:8] + "..."
		tracing.AddSpanAttributes(ctx, attribute.String("auth.token_id", token.ID))
		tracing.AddSpanAttributes(ctx, attribute.String("auth.token_value_masked", maskedToken))
	}

	return nil
}

// GetTokenByValue lấy token theo giá trị và loại (sử dụng auth_sessions table)
func (r *mysqlRepository) GetTokenByValue(ctx context.Context, tokenValue string, tokenType internal.TokenType) (*internal.Token, error) {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "auth_sessions")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_sessions"),
		attribute.String("db.operation", "get_token_by_value"),
		attribute.String("auth.token_type", string(tokenType)),
	)

	// Che giấu thông tin nhạy cảm trong span
	if len(tokenValue) > 8 {
		maskedToken := tokenValue[0:8] + "..."
		tracing.AddSpanAttributes(ctx, attribute.String("auth.token_value_masked", maskedToken))
	}

	var sessionModel models.AuthSession
	tx := r.db.WithContext(ctx).Where("access_token = ? OR refresh_token = ?", tokenValue, tokenValue).First(&sessionModel)

	if tx.Error != nil {
		if errors.Is(tx.Error, gorm.ErrRecordNotFound) {
			tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", false))
			return nil, internal.ErrTokenNotFound
		}
		r.logger.Error("Không thể lấy token", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return nil, tx.Error
	}

	// Chuyển đổi model thành internal.Token
	token := &internal.Token{
		ID:           sessionModel.ID,
		UserID:       int(sessionModel.UserID),
		TokenType:    tokenType,
		AccessToken:  sessionModel.AccessToken,
		RefreshToken: sessionModel.RefreshToken,
		ExpiresAt:    sessionModel.ExpiresAt,
		CreatedAt:    sessionModel.CreatedAt,
		UpdatedAt:    sessionModel.UpdatedAt,
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Bool("db.record_found", true),
		attribute.Int("auth.user_id", token.UserID),
		attribute.String("auth.token_id", token.ID),
		attribute.Bool("auth.token_expired", token.IsExpired()),
	)

	return token, nil
}

// DeleteToken xóa token (sử dụng auth_sessions table)
func (r *mysqlRepository) DeleteToken(ctx context.Context, id string) error {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "auth_sessions")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_sessions"),
		attribute.String("db.operation", "delete_token"),
		attribute.String("auth.token_id", id),
	)

	tx := r.db.WithContext(ctx).Delete(&models.AuthSession{}, "id = ?", id)

	if tx.Error != nil {
		r.logger.Error("Không thể xóa token", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return tx.Error
	}

	return nil
}

// DeleteExpiredTokens xóa tất cả token đã hết hạn (sử dụng auth_sessions table)
func (r *mysqlRepository) DeleteExpiredTokens(ctx context.Context) error {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "auth_sessions")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_sessions"),
		attribute.String("db.operation", "delete_expired_tokens"),
	)

	// Sử dụng raw query vì đây là thao tác list/batch
	query := "DELETE FROM auth_sessions WHERE expires_at < ?"
	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	tx := r.db.WithContext(ctx).Exec(query, time.Now())
	if tx.Error != nil {
		r.logger.Error("Không thể xóa token hết hạn", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return tx.Error
	}

	// Thêm thông tin về số lượng token đã xóa
	tracing.AddSpanAttributes(ctx, attribute.Int64("db.rows_affected", tx.RowsAffected))

	return nil
}

// GetUserProfile lấy thông tin profile của người dùng
func (r *mysqlRepository) GetUserProfile(ctx context.Context, userID int) (*models.Profile, error) {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "user_profiles")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "user_profiles"),
		attribute.String("db.operation", "get_user_profile"),
		attribute.Int("auth.user_id", userID),
	)

	var profile models.Profile
	tx := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&profile)

	if tx.Error != nil {
		if errors.Is(tx.Error, gorm.ErrRecordNotFound) {
			// Không tìm thấy profile không phải lỗi, trả về nil
			tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", false))
			return nil, nil
		}
		r.logger.Error("Không thể lấy thông tin profile người dùng", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return nil, tx.Error
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Bool("db.record_found", true),
		attribute.Int("auth.user_id", int(profile.UserID)),
	)

	return &profile, nil
}
