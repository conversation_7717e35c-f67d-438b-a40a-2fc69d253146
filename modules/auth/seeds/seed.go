package seeds

import (
	"context"
	"fmt"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/seed"
	"wnapi/modules/auth/internal"
	"wnapi/modules/auth/seeds/seeders"
)

// AuthSeed implements seed.ModuleSeed interface for auth module
type AuthSeed struct {
	repo    internal.Repository
	logger  logger.Logger
	config  *seed.Config
	seeders []seed.Seeder
}

// Dependencies chứa các dependencies cần thiết cho auth seeders
type Dependencies struct {
	Repository internal.Repository
	Logger     logger.Logger
	Config     *seed.Config
}

// NewAuthSeed tạo một AuthSeed mới
func NewAuthSeed(deps Dependencies) *AuthSeed {
	if deps.Config == nil {
		deps.Config = seed.DefaultConfig()
	}

	authSeed := &AuthSeed{
		repo:   deps.Repository,
		logger: deps.Logger,
		config: deps.Config,
	}

	// Khởi tạo seeders theo thứ tự dependencies
	authSeed.seeders = []seed.Seeder{
		seeders.NewTenantSeeder(deps.Repository, deps.Logger, deps.Config),
		seeders.NewRoleSeeder(deps.Repository, deps.Logger, deps.Config),
		seeders.NewPermissionSeeder(deps.Repository, deps.Logger, deps.Config),
		seeders.NewUserSeeder(deps.Repository, deps.Logger, deps.Config),
		seeders.NewUserRoleSeeder(deps.Repository, deps.Logger, deps.Config),
		seeders.NewRolePermissionSeeder(deps.Repository, deps.Logger, deps.Config),
	}

	return authSeed
}

// ModuleName trả về tên module
func (s *AuthSeed) ModuleName() string {
	return "auth"
}

// GetSeeders trả về danh sách seeders của module
func (s *AuthSeed) GetSeeders() []seed.Seeder {
	return s.seeders
}

// SeedAll chạy tất cả seeders trong module
func (s *AuthSeed) SeedAll(ctx context.Context) error {
	s.logger.Info("Bắt đầu seed auth module", "environment", s.config.Environment)

	for _, seeder := range s.seeders {
		s.logger.Info("Chạy seeder", "seeder", seeder.Name())
		
		if err := seeder.Run(ctx); err != nil {
			s.logger.Error("Lỗi chạy seeder", 
				"seeder", seeder.Name(), 
				"error", err.Error())
			return fmt.Errorf("lỗi chạy seeder %s: %w", seeder.Name(), err)
		}
		
		s.logger.Info("Hoàn thành seeder", "seeder", seeder.Name())
	}

	s.logger.Info("Hoàn thành seed auth module")
	return nil
}

// SeedSpecific chạy seeder cụ thể
func (s *AuthSeed) SeedSpecific(ctx context.Context, seederName string) error {
	s.logger.Info("Chạy seeder cụ thể", "seeder", seederName)

	// Tìm seeder theo tên
	var targetSeeder seed.Seeder
	for _, seeder := range s.seeders {
		if seeder.Name() == seederName {
			targetSeeder = seeder
			break
		}
	}

	if targetSeeder == nil {
		return fmt.Errorf("không tìm thấy seeder '%s' trong auth module", seederName)
	}

	// Kiểm tra dependencies
	if err := s.checkDependencies(ctx, targetSeeder); err != nil {
		return fmt.Errorf("lỗi kiểm tra dependencies cho seeder %s: %w", seederName, err)
	}

	// Chạy seeder
	if err := targetSeeder.Run(ctx); err != nil {
		s.logger.Error("Lỗi chạy seeder", "seeder", seederName, "error", err.Error())
		return fmt.Errorf("lỗi chạy seeder %s: %w", seederName, err)
	}

	s.logger.Info("Hoàn thành seeder", "seeder", seederName)
	return nil
}

// checkDependencies kiểm tra và chạy dependencies nếu cần
func (s *AuthSeed) checkDependencies(ctx context.Context, targetSeeder seed.Seeder) error {
	dependencies := targetSeeder.Dependencies()
	if len(dependencies) == 0 {
		return nil
	}

	s.logger.Info("Kiểm tra dependencies", 
		"seeder", targetSeeder.Name(), 
		"dependencies", fmt.Sprintf("%v", dependencies))

	// Tạo map để track seeders đã chạy
	seederMap := make(map[string]seed.Seeder)
	for _, seeder := range s.seeders {
		seederMap[seeder.Name()] = seeder
	}

	// Chạy dependencies theo thứ tự
	for _, depName := range dependencies {
		depSeeder, exists := seederMap[depName]
		if !exists {
			return fmt.Errorf("dependency '%s' không tồn tại", depName)
		}

		s.logger.Info("Chạy dependency", "dependency", depName)
		if err := depSeeder.Run(ctx); err != nil {
			return fmt.Errorf("lỗi chạy dependency %s: %w", depName, err)
		}
	}

	return nil
}

// RollbackAll rollback tất cả seeders trong module
func (s *AuthSeed) RollbackAll(ctx context.Context) error {
	s.logger.Info("Bắt đầu rollback auth module")

	// Rollback theo thứ tự ngược lại
	for i := len(s.seeders) - 1; i >= 0; i-- {
		seeder := s.seeders[i]
		s.logger.Info("Rollback seeder", "seeder", seeder.Name())
		
		if err := seeder.Rollback(ctx); err != nil {
			s.logger.Error("Lỗi rollback seeder", 
				"seeder", seeder.Name(), 
				"error", err.Error())
			return fmt.Errorf("lỗi rollback seeder %s: %w", seeder.Name(), err)
		}
		
		s.logger.Info("Hoàn thành rollback seeder", "seeder", seeder.Name())
	}

	s.logger.Info("Hoàn thành rollback auth module")
	return nil
}

// RollbackSpecific rollback seeder cụ thể
func (s *AuthSeed) RollbackSpecific(ctx context.Context, seederName string) error {
	s.logger.Info("Rollback seeder cụ thể", "seeder", seederName)

	// Tìm seeder theo tên
	var targetSeeder seed.Seeder
	for _, seeder := range s.seeders {
		if seeder.Name() == seederName {
			targetSeeder = seeder
			break
		}
	}

	if targetSeeder == nil {
		return fmt.Errorf("không tìm thấy seeder '%s' trong auth module", seederName)
	}

	// Rollback seeder
	if err := targetSeeder.Rollback(ctx); err != nil {
		s.logger.Error("Lỗi rollback seeder", "seeder", seederName, "error", err.Error())
		return fmt.Errorf("lỗi rollback seeder %s: %w", seederName, err)
	}

	s.logger.Info("Hoàn thành rollback seeder", "seeder", seederName)
	return nil
}

// GetSeederByName trả về seeder theo tên
func (s *AuthSeed) GetSeederByName(name string) seed.Seeder {
	for _, seeder := range s.seeders {
		if seeder.Name() == name {
			return seeder
		}
	}
	return nil
}

// GetSeederNames trả về danh sách tên tất cả seeders
func (s *AuthSeed) GetSeederNames() []string {
	var names []string
	for _, seeder := range s.seeders {
		names = append(names, seeder.Name())
	}
	return names
}

// ValidateConfiguration kiểm tra cấu hình của module
func (s *AuthSeed) ValidateConfiguration() error {
	if s.repo == nil {
		return fmt.Errorf("repository không được để trống")
	}

	if s.logger == nil {
		return fmt.Errorf("logger không được để trống")
	}

	if s.config == nil {
		return fmt.Errorf("config không được để trống")
	}

	// Validate config
	if err := s.config.Validate(); err != nil {
		return fmt.Errorf("lỗi validate config: %w", err)
	}

	return nil
}
