package seeders

import (
	"context"
	"fmt"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/seed"
	"wnapi/modules/auth/internal"
)

// TenantSeeder implements seed.Seeder interface for tenant data
type TenantSeeder struct {
	*BaseSeeder
}

// TenantData represents tenant data structure from JSON
type TenantData struct {
	ID           uint   `json:"id"`
	Name         string `json:"name"`
	Slug         string `json:"slug"`
	Description  string `json:"description"`
	Active       bool   `json:"active"`
	Logo         string `json:"logo"`
	ContactEmail string `json:"contact_email"`
	ContactPhone string `json:"contact_phone"`
}

// NewTenantSeeder tạo một TenantSeeder mới
func NewTenantSeeder(repo internal.Repository, logger logger.Logger, config *seed.Config) *TenantSeeder {
	return &TenantSeeder{
		BaseSeeder: NewBaseSeeder(repo, logger, config),
	}
}

// Name trả về tên của seeder
func (s *TenantSeeder) Name() string {
	return "tenants"
}

// Dependencies trả về danh sách seeder phụ thuộc
func (s *TenantSeeder) Dependencies() []string {
	return []string{} // Tenants không phụ thuộc vào seeder nào khác
}

// Description mô tả chức năng của seeder
func (s *TenantSeeder) Description() string {
	return "Seed tenant data for multi-tenant application"
}

// Run thực thi seed tenant data
func (s *TenantSeeder) Run(ctx context.Context) error {
	s.logger.Info("Bắt đầu seed tenant data", "environment", s.config.Environment)

	// Load data từ JSON file
	seedData, err := s.LoadSeedData("tenants.json")
	if err != nil {
		s.LogError(s.Name(), err)
		return fmt.Errorf("lỗi load tenant data: %w", err)
	}

	// Parse data
	var tenants []TenantData
	if err := s.ParseDataToSlice(seedData, &tenants); err != nil {
		s.LogError(s.Name(), err)
		return fmt.Errorf("lỗi parse tenant data: %w", err)
	}

	if len(tenants) == 0 {
		s.LogSkipped(s.Name(), "no tenant data found")
		return nil
	}

	s.logger.Info("Loaded tenant data", "count", len(tenants))

	// Process tenants
	processedCount := 0
	for i, tenantData := range tenants {
		if err := s.processTenant(ctx, tenantData); err != nil {
			s.LogError(s.Name(), err)
			return fmt.Errorf("lỗi process tenant %s: %w", tenantData.Name, err)
		}
		
		processedCount++
		s.LogProgress(s.Name(), i+1, len(tenants))
	}

	s.LogSuccess(s.Name(), processedCount)
	return nil
}

// Rollback xóa tenant data
func (s *TenantSeeder) Rollback(ctx context.Context) error {
	s.logger.Info("Bắt đầu rollback tenant data")

	// Load data để biết cần xóa gì
	seedData, err := s.LoadSeedData("tenants.json")
	if err != nil {
		return fmt.Errorf("lỗi load tenant data cho rollback: %w", err)
	}

	var tenants []TenantData
	if err := s.ParseDataToSlice(seedData, &tenants); err != nil {
		return fmt.Errorf("lỗi parse tenant data cho rollback: %w", err)
	}

	// Xóa từng tenant
	for _, tenantData := range tenants {
		if err := s.rollbackTenant(ctx, tenantData); err != nil {
			s.LogError(s.Name(), err)
			return fmt.Errorf("lỗi rollback tenant %s: %w", tenantData.Name, err)
		}
	}

	s.logger.Info("Hoàn thành rollback tenant data")
	return nil
}

// processTenant xử lý một tenant
func (s *TenantSeeder) processTenant(ctx context.Context, tenantData TenantData) error {
	// Validate required fields
	if err := s.validateTenantData(tenantData); err != nil {
		return err
	}

	// Check if tenant already exists
	if s.ShouldSkipExisting() {
		exists, err := s.tenantExists(ctx, tenantData.Slug)
		if err != nil {
			return fmt.Errorf("lỗi kiểm tra tenant existence: %w", err)
		}
		
		if exists {
			s.logger.Info("Tenant đã tồn tại, skip", "slug", tenantData.Slug)
			return nil
		}
	}

	// Create tenant
	if s.IsDryRun() {
		s.LogDryRun(s.Name(), fmt.Sprintf("Would create tenant: %s", tenantData.Name))
		return nil
	}

	// TODO: Implement actual tenant creation using repository
	// This is a stub implementation
	s.logger.Info("Creating tenant", 
		"name", tenantData.Name, 
		"slug", tenantData.Slug,
		"active", tenantData.Active)

	return nil
}

// rollbackTenant rollback một tenant
func (s *TenantSeeder) rollbackTenant(ctx context.Context, tenantData TenantData) error {
	if s.IsDryRun() {
		s.LogDryRun(s.Name(), fmt.Sprintf("Would delete tenant: %s", tenantData.Name))
		return nil
	}

	// TODO: Implement actual tenant deletion using repository
	s.logger.Info("Deleting tenant", "slug", tenantData.Slug)
	return nil
}

// validateTenantData validate tenant data
func (s *TenantSeeder) validateTenantData(data TenantData) error {
	if data.Name == "" {
		return fmt.Errorf("tenant name không được để trống")
	}
	
	if data.Slug == "" {
		return fmt.Errorf("tenant slug không được để trống")
	}
	
	if data.ContactEmail == "" {
		return fmt.Errorf("tenant contact email không được để trống")
	}

	return nil
}

// tenantExists kiểm tra xem tenant đã tồn tại chưa
func (s *TenantSeeder) tenantExists(ctx context.Context, slug string) (bool, error) {
	// TODO: Implement using repository
	// For now, return false
	return false, nil
}

// GetTenantBySlug lấy tenant theo slug (helper method)
func (s *TenantSeeder) GetTenantBySlug(ctx context.Context, slug string) (*TenantData, error) {
	// TODO: Implement using repository
	return nil, fmt.Errorf("not implemented")
}

// CreateTenant tạo tenant mới (helper method)
func (s *TenantSeeder) CreateTenant(ctx context.Context, data TenantData) error {
	// TODO: Implement using repository
	return fmt.Errorf("not implemented")
}

// DeleteTenant xóa tenant (helper method)
func (s *TenantSeeder) DeleteTenant(ctx context.Context, slug string) error {
	// TODO: Implement using repository
	return fmt.Errorf("not implemented")
}
