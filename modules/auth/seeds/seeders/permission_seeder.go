package seeders

import (
	"context"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/seed"
	"wnapi/modules/auth/internal"
)

// PermissionSeeder implements seed.Seeder interface for permission data
type PermissionSeeder struct {
	*BaseSeeder
}

// NewPermissionSeeder tạo một PermissionSeeder mới
func NewPermissionSeeder(repo internal.Repository, logger logger.Logger, config *seed.Config) *PermissionSeeder {
	return &PermissionSeeder{
		BaseSeeder: NewBaseSeeder(repo, logger, config),
	}
}

// Name trả về tên của seeder
func (s *PermissionSeeder) Name() string {
	return "permissions"
}

// Dependencies trả về danh sách seeder phụ thuộc
func (s *PermissionSeeder) Dependencies() []string {
	return []string{"tenants"} // Permissions có thể phụ thuộc vào tenants
}

// Description mô tả chức năng của seeder
func (s *PermissionSeeder) Description() string {
	return "Seed permission data for RBAC system"
}

// Run thực thi seed permission data
func (s *PermissionSeeder) Run(ctx context.Context) error {
	s.logger.Info("Bắt đầu seed permission data", "environment", s.config.Environment)
	// TODO: Implement full permission seeding logic
	s.logger.Info("Permission seeder - stub implementation")
	return nil
}

// Rollback xóa permission data
func (s *PermissionSeeder) Rollback(ctx context.Context) error {
	s.logger.Info("Bắt đầu rollback permission data")
	// TODO: Implement permission rollback logic
	s.logger.Info("Permission rollback - stub implementation")
	return nil
}
