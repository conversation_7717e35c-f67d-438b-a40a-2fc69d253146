package seeders

import (
	"context"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/seed"
	"wnapi/modules/auth/internal"
)

// UserRoleSeeder implements seed.Seeder interface for user-role mappings
type UserRoleSeeder struct {
	*BaseSeeder
}

// NewUserRoleSeeder tạo một UserRoleSeeder mới
func NewUserRoleSeeder(repo internal.Repository, logger logger.Logger, config *seed.Config) *UserRoleSeeder {
	return &UserRoleSeeder{
		BaseSeeder: NewBaseSeeder(repo, logger, config),
	}
}

// Name trả về tên của seeder
func (s *UserRoleSeeder) Name() string {
	return "user_roles"
}

// Dependencies trả về danh sách seeder phụ thuộc
func (s *UserRoleSeeder) Dependencies() []string {
	return []string{"users", "roles"} // UserRoles phụ thuộc vào users và roles
}

// Description mô tả chức năng của seeder
func (s *UserRoleSeeder) Description() string {
	return "Seed user-role mappings for RBAC system"
}

// Run thực thi seed user-role data
func (s *UserRoleSeeder) Run(ctx context.Context) error {
	s.logger.Info("Bắt đầu seed user-role data", "environment", s.config.Environment)
	// TODO: Implement full user-role seeding logic
	s.logger.Info("UserRole seeder - stub implementation")
	return nil
}

// Rollback xóa user-role data
func (s *UserRoleSeeder) Rollback(ctx context.Context) error {
	s.logger.Info("Bắt đầu rollback user-role data")
	// TODO: Implement user-role rollback logic
	s.logger.Info("UserRole rollback - stub implementation")
	return nil
}
