package seeders

import (
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/seed"
	"wnapi/modules/auth/internal"
)

// BaseSeeder cung cấp functionality chung cho tất cả auth seeders
type BaseSeeder struct {
	repo       internal.Repository
	logger     logger.Logger
	config     *seed.Config
	dataLoader seed.DataLoader
}

// NewBaseSeeder tạo một BaseSeeder mới
func NewBaseSeeder(repo internal.Repository, logger logger.Logger, config *seed.Config) *BaseSeeder {
	return &BaseSeeder{
		repo:       repo,
		logger:     logger,
		config:     config,
		dataLoader: seed.NewFileDataLoader(config),
	}
}

// LoadSeedData load data từ JSON file
func (b *BaseSeeder) LoadSeedData(fileName string) (*seed.SeedData, error) {
	dataPath := b.getDataPath()
	return b.dataLoader.LoadDataWithFallback(dataPath, b.config.Environment, fileName)
}

// ParseDataToSlice parse seed data thành slice of structs
func (b *BaseSeeder) ParseDataToSlice(seedData *seed.SeedData, target interface{}) error {
	dataBytes, err := json.Marshal(seedData.Data)
	if err != nil {
		return fmt.Errorf("lỗi marshal seed data: %w", err)
	}

	if err := json.Unmarshal(dataBytes, target); err != nil {
		return fmt.Errorf("lỗi unmarshal seed data: %w", err)
	}

	return nil
}

// ValidateData validate cấu trúc data
func (b *BaseSeeder) ValidateData(data interface{}) error {
	if data == nil {
		return fmt.Errorf("data không được để trống")
	}
	return nil
}

// GetTenantID trả về tenant ID từ config hoặc context
func (b *BaseSeeder) GetTenantID(ctx context.Context) uint {
	// Có thể lấy từ context nếu cần
	return b.config.TenantID
}

// GetUserID trả về user ID cho audit purposes
func (b *BaseSeeder) GetUserID() *uint {
	return b.config.UserID
}

// ShouldSkipExisting kiểm tra xem có skip existing data không
func (b *BaseSeeder) ShouldSkipExisting() bool {
	return b.config.SkipExists
}

// IsDryRun kiểm tra xem có phải dry run mode không
func (b *BaseSeeder) IsDryRun() bool {
	return b.config.DryRun
}

// GetBatchSize trả về batch size cho bulk operations
func (b *BaseSeeder) GetBatchSize() int {
	return b.config.BatchSize
}

// LogProgress log progress của seeding operation
func (b *BaseSeeder) LogProgress(seederName string, current, total int) {
	b.logger.Info("Seeding progress",
		"seeder", seederName,
		"current", current,
		"total", total,
		"percentage", fmt.Sprintf("%.1f%%", float64(current)/float64(total)*100))
}

// LogSuccess log thành công của seeding operation
func (b *BaseSeeder) LogSuccess(seederName string, count int) {
	b.logger.Info("Seeding completed successfully",
		"seeder", seederName,
		"records_processed", count)
}

// LogError log lỗi của seeding operation
func (b *BaseSeeder) LogError(seederName string, err error) {
	b.logger.Error("Seeding failed",
		"seeder", seederName,
		"error", err.Error())
}

// LogSkipped log khi skip existing data
func (b *BaseSeeder) LogSkipped(seederName string, reason string) {
	b.logger.Info("Seeding skipped",
		"seeder", seederName,
		"reason", reason)
}

// LogDryRun log khi chạy dry run mode
func (b *BaseSeeder) LogDryRun(seederName string, action string) {
	b.logger.Info("Dry run mode",
		"seeder", seederName,
		"action", action,
		"note", "No actual changes made to database")
}

// getDataPath trả về đường dẫn đến thư mục data
func (b *BaseSeeder) getDataPath() string {
	return filepath.Join(b.config.DataPath, "modules", "auth", "seeds", "data")
}

// CheckDataExists kiểm tra xem data đã tồn tại chưa
func (b *BaseSeeder) CheckDataExists(ctx context.Context, tableName string, conditions map[string]interface{}) (bool, error) {
	dbOps := seed.NewGormDatabaseOperations(nil, b.logger) // TODO: Inject proper DB
	return dbOps.CheckDataExists(ctx, tableName, conditions)
}

// BatchInsert thực hiện batch insert
func (b *BaseSeeder) BatchInsert(ctx context.Context, tableName string, data []interface{}) error {
	if b.IsDryRun() {
		b.LogDryRun("batch_insert", fmt.Sprintf("Would insert %d records into %s", len(data), tableName))
		return nil
	}

	dbOps := seed.NewGormDatabaseOperations(nil, b.logger) // TODO: Inject proper DB
	return dbOps.BatchInsert(ctx, tableName, data, b.GetBatchSize())
}

// BulkUpsert thực hiện bulk upsert
func (b *BaseSeeder) BulkUpsert(ctx context.Context, tableName string, data []interface{}, conflictColumns []string) error {
	if b.IsDryRun() {
		b.LogDryRun("bulk_upsert", fmt.Sprintf("Would upsert %d records into %s", len(data), tableName))
		return nil
	}

	dbOps := seed.NewGormDatabaseOperations(nil, b.logger) // TODO: Inject proper DB
	return dbOps.BulkUpsert(ctx, tableName, data, conflictColumns, b.GetBatchSize())
}

// DeleteByConditions xóa records theo conditions
func (b *BaseSeeder) DeleteByConditions(ctx context.Context, tableName string, conditions map[string]interface{}) error {
	if b.IsDryRun() {
		b.LogDryRun("delete", fmt.Sprintf("Would delete records from %s with conditions %v", tableName, conditions))
		return nil
	}

	dbOps := seed.NewGormDatabaseOperations(nil, b.logger) // TODO: Inject proper DB
	return dbOps.DeleteByConditions(ctx, tableName, conditions)
}

// ValidateRequiredFields kiểm tra required fields
func (b *BaseSeeder) ValidateRequiredFields(data map[string]interface{}, requiredFields []string) error {
	for _, field := range requiredFields {
		if value, exists := data[field]; !exists || value == nil || value == "" {
			return fmt.Errorf("required field '%s' is missing or empty", field)
		}
	}
	return nil
}

// ConvertToInterfaces chuyển đổi slice of structs thành slice of interfaces
func (b *BaseSeeder) ConvertToInterfaces(data interface{}) ([]interface{}, error) {
	// Sử dụng reflection để convert
	// Implementation sẽ được hoàn thiện trong task tiếp theo
	return nil, fmt.Errorf("not implemented yet")
}

// GetEnvironmentSpecificValue lấy value theo environment
func (b *BaseSeeder) GetEnvironmentSpecificValue(values map[string]interface{}) interface{} {
	if value, exists := values[b.config.Environment]; exists {
		return value
	}
	
	// Fallback to default
	if value, exists := values["default"]; exists {
		return value
	}
	
	return nil
}

// SanitizeData sanitize data trước khi insert
func (b *BaseSeeder) SanitizeData(data map[string]interface{}) map[string]interface{} {
	sanitized := make(map[string]interface{})
	
	for key, value := range data {
		// Remove nil values
		if value != nil {
			sanitized[key] = value
		}
	}
	
	return sanitized
}

// GetCurrentTimestamp trả về timestamp hiện tại
func (b *BaseSeeder) GetCurrentTimestamp() string {
	return seed.GetCurrentTime()
}
