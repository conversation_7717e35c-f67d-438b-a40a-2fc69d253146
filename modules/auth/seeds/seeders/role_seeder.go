package seeders

import (
	"context"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/seed"
	"wnapi/modules/auth/internal"
)

// RoleSeeder implements seed.Seeder interface for role data
type RoleSeeder struct {
	*BaseSeeder
}

// NewRoleSeeder tạo một RoleSeeder mới
func NewRoleSeeder(repo internal.Repository, logger logger.Logger, config *seed.Config) *RoleSeeder {
	return &RoleSeeder{
		BaseSeeder: NewBaseSeeder(repo, logger, config),
	}
}

// Name trả về tên của seeder
func (s *RoleSeeder) Name() string {
	return "roles"
}

// Dependencies trả về danh sách seeder phụ thuộc
func (s *RoleSeeder) Dependencies() []string {
	return []string{"tenants"} // Roles phụ thuộc vào tenants
}

// Description mô tả chức năng của seeder
func (s *RoleSeeder) Description() string {
	return "Seed role data for RBAC system"
}

// Run thực thi seed role data
func (s *RoleSeeder) Run(ctx context.Context) error {
	s.logger.Info("Bắt đầu seed role data", "environment", s.config.Environment)
	// TODO: Implement full role seeding logic
	s.logger.Info("Role seeder - stub implementation")
	return nil
}

// Rollback xóa role data
func (s *RoleSeeder) Rollback(ctx context.Context) error {
	s.logger.Info("Bắt đầu rollback role data")
	// TODO: Implement role rollback logic
	s.logger.Info("Role rollback - stub implementation")
	return nil
}
