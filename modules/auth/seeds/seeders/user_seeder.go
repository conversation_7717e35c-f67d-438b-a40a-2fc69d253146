package seeders

import (
	"context"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/seed"
	"wnapi/modules/auth/internal"
)

// UserSeeder implements seed.Seeder interface for user data
type UserSeeder struct {
	*BaseSeeder
}

// NewUserSeeder tạo một UserSeeder mới
func NewUserSeeder(repo internal.Repository, logger logger.Logger, config *seed.Config) *UserSeeder {
	return &UserSeeder{
		BaseSeeder: NewBaseSeeder(repo, logger, config),
	}
}

// Name trả về tên của seeder
func (s *UserSeeder) Name() string {
	return "users"
}

// Dependencies trả về danh sách seeder phụ thuộc
func (s *UserSeeder) Dependencies() []string {
	return []string{"tenants"} // Users phụ thuộc vào tenants
}

// Description mô tả chức năng của seeder
func (s *UserSeeder) Description() string {
	return "Seed user accounts for authentication"
}

// Run thực thi seed user data
func (s *UserSeeder) Run(ctx context.Context) error {
	s.logger.Info("Bắt đầu seed user data", "environment", s.config.Environment)
	// TODO: Implement full user seeding logic
	s.logger.Info("User seeder - stub implementation")
	return nil
}

// Rollback xóa user data
func (s *UserSeeder) Rollback(ctx context.Context) error {
	s.logger.Info("Bắt đầu rollback user data")
	// TODO: Implement user rollback logic
	s.logger.Info("User rollback - stub implementation")
	return nil
}
