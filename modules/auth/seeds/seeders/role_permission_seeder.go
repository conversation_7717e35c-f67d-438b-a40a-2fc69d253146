package seeders

import (
	"context"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/seed"
	"wnapi/modules/auth/internal"
)

// RolePermissionSeeder implements seed.Seeder interface for role-permission mappings
type RolePermissionSeeder struct {
	*BaseSeeder
}

// NewRolePermissionSeeder tạo một RolePermissionSeeder mới
func NewRolePermissionSeeder(repo internal.Repository, logger logger.Logger, config *seed.Config) *RolePermissionSeeder {
	return &RolePermissionSeeder{
		BaseSeeder: NewBaseSeeder(repo, logger, config),
	}
}

// Name trả về tên của seeder
func (s *RolePermissionSeeder) Name() string {
	return "role_permissions"
}

// Dependencies trả về danh sách seeder phụ thuộc
func (s *RolePermissionSeeder) Dependencies() []string {
	return []string{"roles", "permissions"} // RolePermissions phụ thuộc vào roles và permissions
}

// Description mô tả chức năng của seeder
func (s *RolePermissionSeeder) Description() string {
	return "Seed role-permission mappings for RBAC system"
}

// Run thực thi seed role-permission data
func (s *RolePermissionSeeder) Run(ctx context.Context) error {
	s.logger.Info("Bắt đầu seed role-permission data", "environment", s.config.Environment)
	// TODO: Implement full role-permission seeding logic
	s.logger.Info("RolePermission seeder - stub implementation")
	return nil
}

// Rollback xóa role-permission data
func (s *RolePermissionSeeder) Rollback(ctx context.Context) error {
	s.logger.Info("Bắt đầu rollback role-permission data")
	// TODO: Implement role-permission rollback logic
	s.logger.Info("RolePermission rollback - stub implementation")
	return nil
}
