{"environment": "dev", "metadata": {"version": "1.0.0", "created_at": "2024-03-15T10:00:00Z", "description": "Default tenant data for development environment", "dependencies": [], "tags": ["tenants", "auth", "dev"], "author": "System", "environment": ["dev", "staging"], "tenant_scope": "global"}, "data": [{"id": 1, "name": "<PERSON><PERSON><PERSON>", "slug": "default", "description": "Default tenant for development and testing", "active": true, "logo": "", "contact_email": "<EMAIL>", "contact_phone": "+1234567890"}, {"id": 2, "name": "Demo Company", "slug": "demo", "description": "Demo company for showcasing features", "active": true, "logo": "", "contact_email": "<EMAIL>", "contact_phone": "+1234567891"}]}