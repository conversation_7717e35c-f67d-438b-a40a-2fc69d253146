package seeds

import (
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/seed"
	"wnapi/modules/auth/internal"
)

// RegisterAuthSeed đăng ký auth seed với global registry
func RegisterAuthSeed(repo internal.Repository, logger logger.Logger, config *seed.Config) error {
	deps := Dependencies{
		Repository: repo,
		Logger:     logger,
		Config:     config,
	}

	authSeed := NewAuthSeed(deps)
	return seed.RegisterModuleSeed("auth", authSeed)
}

// init function để tự động đăng ký (sẽ được gọi khi import package)
// T<PERSON><PERSON> thờ<PERSON> comment out vì cần dependencies
/*
func init() {
	// Tự động đăng ký auth seed khi import module
	// Cần có cách để inject dependencies
	seed.RegisterModuleSeed("auth", NewAuthSeed)
}
*/
