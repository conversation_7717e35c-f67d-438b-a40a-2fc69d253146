// modules/product/permission_defs.go
package product

import "wnapi/internal/pkg/permission"

// Module name
const ModuleName = "products"

// Product permissions
const (
	// Standard CRUD permissions
	CreatePermission = ModuleName + permission.PermissionSeparator + permission.ActionCreate
	ReadPermission   = ModuleName + permission.PermissionSeparator + permission.ActionRead
	UpdatePermission = ModuleName + permission.PermissionSeparator + permission.ActionUpdate
	DeletePermission = ModuleName + permission.PermissionSeparator + permission.ActionDelete
	ListPermission   = ModuleName + permission.PermissionSeparator + permission.ActionList

	// Special permissions
	PublishPermission = ModuleName + permission.PermissionSeparator + "publish"
	ExportPermission  = ModuleName + permission.PermissionSeparator + "export"
	ImportPermission  = ModuleName + permission.PermissionSeparator + "import"

	// Full management permission
	ManagePermission = ModuleName + permission.PermissionSeparator + permission.ActionManage
)

// GetCRUDPermissions trả về tất cả CRUD permissions của module
func GetCRUDPermissions() []string {
	return []string{
		CreatePermission,
		ReadPermission,
		UpdatePermission,
		DeletePermission,
		ListPermission,
	}
}

// GetAllPermissions trả về tất cả permissions của module
func GetAllPermissions() []string {
	return append(
		GetCRUDPermissions(),
		PublishPermission,
		ExportPermission,
		ImportPermission,
		ManagePermission,
	)
}
