package product

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"wnapi/internal/core"
	"wnapi/modules/product/internal"
)

// Module represents the product module
type Module struct {
	DB     *sqlx.DB
	GormDB *gorm.DB
	Config *internal.ProductConfig
	server *http.Server
	router *gin.Engine
}

// NewModule creates a new instance of the product module
func NewModule(app *core.AppBootstrap, config map[string]interface{}) (core.Module, error) {
	// Load config từ env
	cfg, err := internal.LoadProductConfig()
	if err != nil {
		return nil, err
	}

	// Lấy DBManager từ app
	dbManager := app.GetDBManager()
	sqlxdb := dbManager.GetDB()

	// Khởi tạo GormDB từ sqlxdb
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlxdb.DB,
	}), &gorm.Config{})
	if err != nil {
		return nil, err
	}

	m := &Module{
		DB:     sqlxdb,
		GormDB: gormDB,
		Config: cfg,
	}
	return m, nil
}

// NewModuleWithConfig creates a new instance of the product module with configuration
func NewModuleWithConfig(cfg *internal.ProductConfig) *Module {
	// Set up database connection
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true",
		cfg.DBUser,
		cfg.DBPassword,
		cfg.DBHost,
		cfg.DBPort,
		cfg.DBName,
	)

	db, err := sqlx.Connect("mysql", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Set connection pool settings
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	// Khởi tạo GormDB từ chuỗi kết nối
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		DSN: dsn,
	}), &gorm.Config{})

	if err != nil {
		log.Fatalf("Failed to initialize GORM: %v", err)
	}

	return &Module{
		DB:     db,
		GormDB: gormDB,
		Config: cfg,
		router: gin.Default(),
	}
}

// RegisterRoutes implements core.Module interface
func (m *Module) RegisterRoutes(server *core.Server) error {
	// TODO: Đăng ký route thực tế ở đây, ví dụ:
	// server.GET("/api/v1/products", handler.ListProducts)
	// server.POST("/api/v1/products", handler.CreateProduct)
	// ...
	return nil
}

// Start starts the ecom module server
func (m *Module) Start() error {
	// If router is not set, use the one from the module
	if m.router == nil {
		m.router = gin.Default()
	}

	// Register routes
	m.RegisterRoutes(nil)

	// Create HTTP server
	m.server = &http.Server{
		Addr:    fmt.Sprintf("%s:%d", m.Config.ServerHost, m.Config.ServerPort),
		Handler: m.router,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting product service on %s:%d", m.Config.ServerHost, m.Config.ServerPort)
		if err := m.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	return nil
}

// Stop gracefully stops the product module server
func (m *Module) Stop() error {
	if m.server == nil {
		return nil
	}

	// Create a context with timeout for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Shutdown server
	if err := m.server.Shutdown(ctx); err != nil {
		return fmt.Errorf("server shutdown failed: %w", err)
	}

	// Close database connection
	if m.DB != nil {
		if err := m.DB.Close(); err != nil {
			return fmt.Errorf("database connection close failed: %w", err)
		}
	}

	log.Println("Product service stopped gracefully")
	return nil
}

// Thêm các method để implement core.Module
func (m *Module) Name() string {
	return "product"
}

func (m *Module) Init(ctx context.Context) error {
	// Khởi tạo các service, repository nếu cần
	return nil
}

func (m *Module) Cleanup(ctx context.Context) error {
	if m.DB != nil {
		_ = m.DB.Close()
	}
	return nil
}

func (m *Module) GetMigrationPath() string {
	return "modules/product/migrations"
}

func (m *Module) GetMigrationOrder() int {
	return 2 // sau auth
}

func init() {
	core.RegisterModuleFactory("product", NewModule)
}
