// modules/product/api/handler.go
package api

import (
	"net/http"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/modules/product"
	"wnapi/modules/product/service"

	"github.com/gin-gonic/gin"
)

// ProductHandler xử lý các API của module product
type ProductHandler struct {
	service           service.ProductService
	middlewareFactory *permission.MiddlewareFactory // Thêm MiddlewareFactory
	logger            logger.Logger
}

// NewProductHandler tạo một handler mới
func NewProductHandler(
	svc service.ProductService,
	mwFactory *permission.MiddlewareFactory, // Thêm tham số MiddlewareFactory
	log logger.Logger,
) *ProductHandler {
	return &ProductHandler{
		service:           svc,
		middlewareFactory: mwFactory, // Lưu MiddlewareFactory
		logger:            log,
	}
}

// RegisterRoutes đăng ký routes cho module product
func (h *ProductHandler) RegisterRoutes(router *gin.RouterGroup) {
	// Group cho module product
	productGroup := router.Group("/products")

	// Các public endpoints (không cần xác thực và phân quyền)
	productGroup.GET("/public", h.ListPublicProducts)

	// Protected routes - yêu cầu xác thực và phân quyền
	// Lưu ý: Thứ tự middleware rất quan trọng - Tenant → Auth → Permission
	// Giả sử tenantMiddleware và authMiddleware đã được áp dụng ở một cấp cao hơn

	// API liệt kê sản phẩm - yêu cầu quyền "products.list"
	productGroup.GET("",
		h.middlewareFactory.RequirePermission(product.ListPermission),
		h.ListProducts,
	)

	// API lấy chi tiết sản phẩm - yêu cầu quyền "products.read"
	productGroup.GET("/:id",
		h.middlewareFactory.RequirePermission(product.ReadPermission),
		h.GetProduct,
	)

	// API tạo sản phẩm - yêu cầu quyền "products.create"
	productGroup.POST("",
		h.middlewareFactory.RequirePermission(product.CreatePermission),
		h.CreateProduct,
	)

	// API cập nhật sản phẩm - yêu cầu quyền "products.update"
	productGroup.PUT("/:id",
		h.middlewareFactory.RequirePermission(product.UpdatePermission),
		h.UpdateProduct,
	)

	// API xóa sản phẩm - yêu cầu quyền "products.delete"
	productGroup.DELETE("/:id",
		h.middlewareFactory.RequirePermission(product.DeletePermission),
		h.DeleteProduct,
	)

	// API đặc biệt - yêu cầu nhiều quyền cùng lúc
	productGroup.PUT("/:id/publish",
		h.middlewareFactory.RequireAllPermissions(product.UpdatePermission, product.PublishPermission),
		h.PublishProduct,
	)

	// API chỉ yêu cầu một trong các quyền
	productGroup.GET("/stats",
		h.middlewareFactory.RequireAnyPermission(product.ReadPermission, "reports.view"),
		h.GetProductStats,
	)
}

// Các handler methods - triển khai stub
func (h *ProductHandler) ListPublicProducts(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "List public products"})
}

func (h *ProductHandler) ListProducts(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "List products"})
}

func (h *ProductHandler) GetProduct(c *gin.Context) {
	id := c.Param("id")
	c.JSON(http.StatusOK, gin.H{"message": "Get product", "id": id})
}

func (h *ProductHandler) CreateProduct(c *gin.Context) {
	c.JSON(http.StatusCreated, gin.H{"message": "Create product"})
}

func (h *ProductHandler) UpdateProduct(c *gin.Context) {
	id := c.Param("id")
	c.JSON(http.StatusOK, gin.H{"message": "Update product", "id": id})
}

func (h *ProductHandler) DeleteProduct(c *gin.Context) {
	id := c.Param("id")
	c.JSON(http.StatusOK, gin.H{"message": "Delete product", "id": id})
}

func (h *ProductHandler) PublishProduct(c *gin.Context) {
	id := c.Param("id")
	c.JSON(http.StatusOK, gin.H{"message": "Publish product", "id": id})
}

func (h *ProductHandler) GetProductStats(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Get product stats"})
}
