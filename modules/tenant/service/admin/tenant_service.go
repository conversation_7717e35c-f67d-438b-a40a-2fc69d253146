package admin

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"time"

	"wnapi/modules/tenant/dto/admin/tenant"
	"wnapi/modules/tenant/repository"
	"wnapi/pkg/database/seeder"

	"wnapi/modules/tenant/tracing"
)

// TenantService handles admin tenant operations
type TenantService struct {
	repo repository.TenantRepository
	db   *sql.DB
}

// NewTenantService creates a new admin tenant service
func NewTenantService(repo repository.TenantRepository, db *sql.DB) *TenantService {
	return &TenantService{
		repo: repo,
		db:   db,
	}
}

// Create creates a new tenant
func (s *TenantService) Create(ctx context.Context, req tenant.CreateTenantRequest) (*tenant.TenantResponse, error) {
	// Bắt đầu span mới
	ctx, otSpan, jaegerSpan := tracing.StartTenantSpan(ctx, "tenant.create")
	defer tracing.FinishTenantSpan(otSpan, jaegerSpan)

	// Thêm thuộc tính tenant
	tenantAttrs := map[string]interface{}{
		"tenant.name":      req.TenantName,
		"tenant.resource":  "tenant",
		"tenant.operation": "create",
		"tenant.status":    req.Status,
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, tenantAttrs)

	// Tạo tenant repository
	newTenant := repository.Tenant{
		Name:      req.TenantName,
		Code:      req.TenantCode,
		Status:    req.Status,
		PlanType:  req.PlanType,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Lưu tenant vào database
	createdTenant, err := s.repo.Create(ctx, newTenant)
	if err != nil {
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, fmt.Errorf("failed to create tenant: %w", err)
	}

	// Chuyển đổi sang đối tượng response
	response := tenant.FromRepository(createdTenant)

	// Thêm ID vào span
	tenantIDAttrs := map[string]interface{}{
		"tenant.id": response.ID,
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, tenantIDAttrs)

	return &response, nil
}

// CreateWithTemplate tạo tenant mới với dữ liệu mẫu
func (s *TenantService) CreateWithTemplate(ctx context.Context, req tenant.CreateTenantRequest) (*tenant.TenantResponse, error) {
	// Tạo tenant cơ bản
	tenantResp, err := s.Create(ctx, req)
	if err != nil {
		return nil, err
	}

	// Khởi tạo master seeder
	master := seeder.NewMasterSeeder(s.db)
	if err := master.DiscoverModules(); err != nil {
		log.Printf("Cảnh báo: Không thể phát hiện modules cho seed: %v", err)
		return tenantResp, nil
	}

	// Seed dữ liệu mẫu cho tenant mới
	if err := master.SeedTenantTemplate(ctx, int64(tenantResp.ID), req.TenantName); err != nil {
		log.Printf("Cảnh báo: Lỗi khi seed dữ liệu mẫu cho tenant %d: %v", tenantResp.ID, err)
	}

	return tenantResp, nil
}

// GetByID retrieves a tenant by ID
func (s *TenantService) GetByID(ctx context.Context, tenantID uint) (*tenant.TenantResponse, error) {
	// Bắt đầu span mới
	ctx, otSpan, jaegerSpan := tracing.StartTenantSpan(ctx, "tenant.get_by_id")
	defer tracing.FinishTenantSpan(otSpan, jaegerSpan)

	// Thêm thuộc tính tenant
	tenantAttrs := map[string]interface{}{
		"tenant.id":        tenantID,
		"tenant.resource":  "tenant",
		"tenant.operation": "get_by_id",
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, tenantAttrs)

	tenantModel, err := s.repo.GetByID(ctx, tenantID)
	if err != nil {
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, err
	}

	response := tenant.FromRepository(tenantModel)

	// Thêm tên tenant vào span
	nameAttrs := map[string]interface{}{
		"tenant.name": response.TenantName,
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, nameAttrs)

	return &response, nil
}

// Update updates a tenant
func (s *TenantService) Update(ctx context.Context, tenantID uint, req tenant.UpdateTenantRequest) (*tenant.TenantResponse, error) {
	// Bắt đầu span mới
	ctx, otSpan, jaegerSpan := tracing.StartTenantSpan(ctx, "tenant.update")
	defer tracing.FinishTenantSpan(otSpan, jaegerSpan)

	// Thêm thuộc tính tenant
	tenantAttrs := map[string]interface{}{
		"tenant.id":        tenantID,
		"tenant.name":      req.TenantName,
		"tenant.resource":  "tenant",
		"tenant.operation": "update",
		"tenant.status":    req.Status,
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, tenantAttrs)

	// Lấy tenant hiện tại
	currentTenant, err := s.repo.GetByID(ctx, tenantID)
	if err != nil {
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, err
	}

	// Cập nhật các trường
	updateTenant := repository.Tenant{
		ID:        tenantID,
		Name:      req.TenantName,
		Status:    req.Status,
		PlanType:  req.PlanType,
		Code:      currentTenant.Code,
		Email:     currentTenant.Email,
		CreatedAt: currentTenant.CreatedAt,
		UpdatedAt: time.Now(),
	}

	// Cập nhật tenant
	updatedTenant, err := s.repo.Update(ctx, updateTenant)
	if err != nil {
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, fmt.Errorf("failed to update tenant: %w", err)
	}

	response := tenant.FromRepository(updatedTenant)
	return &response, nil
}

// Delete removes a tenant
func (s *TenantService) Delete(ctx context.Context, tenantID uint) error {
	// Bắt đầu span mới
	ctx, otSpan, jaegerSpan := tracing.StartTenantSpan(ctx, "tenant.delete")
	defer tracing.FinishTenantSpan(otSpan, jaegerSpan)

	// Thêm thuộc tính tenant
	tenantAttrs := map[string]interface{}{
		"tenant.id":        tenantID,
		"tenant.resource":  "tenant",
		"tenant.operation": "delete",
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, tenantAttrs)

	err := s.repo.Delete(ctx, tenantID)
	if err != nil {
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
	}
	return err
}

// List retrieves a list of tenants with pagination
func (s *TenantService) List(ctx context.Context, req tenant.ListTenantsRequest) (*tenant.ListTenantsResponse, error) {
	// Bắt đầu span mới
	ctx, otSpan, jaegerSpan := tracing.StartTenantSpan(ctx, "tenant.list")
	defer tracing.FinishTenantSpan(otSpan, jaegerSpan)

	// Thêm thuộc tính tenant
	tenantAttrs := map[string]interface{}{
		"tenant.resource":  "tenant",
		"tenant.operation": "list",
		"tenant.status":    req.Status,
		"tenant.search":    req.Search,
		"tenant.limit":     req.Limit,
		"tenant.cursor":    req.Cursor,
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, tenantAttrs)

	limit := req.Limit
	if limit <= 0 {
		limit = 10
	}

	// Tạo params cho repository
	params := repository.ListParams{
		Cursor: req.Cursor,
		Limit:  limit,
		Status: req.Status,
		Search: req.Search,
	}

	// Gọi repository
	tenants, nextCursor, hasMore, err := s.repo.List(ctx, params)
	if err != nil {
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, fmt.Errorf("failed to list tenants: %w", err)
	}

	// Thêm thông tin kết quả vào span
	resultAttrs := map[string]interface{}{
		"tenant.count":       len(tenants),
		"tenant.next_cursor": nextCursor,
		"tenant.has_more":    hasMore,
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, resultAttrs)

	response := &tenant.ListTenantsResponse{
		Data: tenant.FromRepositoryList(tenants),
		Meta: struct {
			NextCursor string `json:"next_cursor"`
			HasMore    bool   `json:"has_more"`
		}{
			NextCursor: nextCursor,
			HasMore:    hasMore,
		},
	}
	return response, nil
}

// UpdateStatus updates a tenant's status
func (s *TenantService) UpdateStatus(ctx context.Context, tenantID uint, status string) (*tenant.TenantResponse, error) {
	// Bắt đầu span mới
	ctx, otSpan, jaegerSpan := tracing.StartTenantSpan(ctx, "tenant.update_status")
	defer tracing.FinishTenantSpan(otSpan, jaegerSpan)

	// Thêm thuộc tính tenant
	tenantAttrs := map[string]interface{}{
		"tenant.id":        tenantID,
		"tenant.resource":  "tenant",
		"tenant.operation": "update_status",
		"tenant.status":    status,
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, tenantAttrs)

	// Update tenant status
	updatedTenant, err := s.repo.UpdateStatus(ctx, tenantID, status)
	if err != nil {
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, err
	}

	response := tenant.FromRepository(updatedTenant)
	return &response, nil
}

// UpdatePlan updates a tenant's subscription plan
func (s *TenantService) UpdatePlan(ctx context.Context, tenantID uint, planType string, expiryDate *time.Time) (*tenant.TenantResponse, error) {
	// Bắt đầu span mới
	ctx, otSpan, jaegerSpan := tracing.StartTenantSpan(ctx, "tenant.update_plan")
	defer tracing.FinishTenantSpan(otSpan, jaegerSpan)

	// Thêm thuộc tính tenant
	tenantAttrs := map[string]interface{}{
		"tenant.id":        tenantID,
		"tenant.resource":  "tenant",
		"tenant.operation": "update_plan",
		"tenant.plan_type": planType,
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, tenantAttrs)

	// Lấy tenant hiện tại
	currentTenant, err := s.repo.GetByID(ctx, tenantID)
	if err != nil {
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, err
	}

	// Cập nhật trường plan_type
	updateTenant := repository.Tenant{
		ID:       tenantID,
		Name:     currentTenant.Name,
		Code:     currentTenant.Code,
		Status:   currentTenant.Status,
		PlanType: planType,
		// Cần thêm các trường khác nếu cần thiết
	}

	// Cập nhật tenant
	updatedTenant, err := s.repo.Update(ctx, updateTenant)
	if err != nil {
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, fmt.Errorf("failed to update tenant plan: %w", err)
	}

	response := tenant.FromRepository(updatedTenant)
	return &response, nil
}

// GetStats retrieves tenant statistics
func (s *TenantService) GetStats(ctx context.Context) (map[string]int64, error) {
	// Bắt đầu span mới
	ctx, otSpan, jaegerSpan := tracing.StartTenantSpan(ctx, "tenant.get_stats")
	defer tracing.FinishTenantSpan(otSpan, jaegerSpan)

	// Sample implementation
	stats := map[string]int64{
		"total":     0,
		"active":    0,
		"inactive":  0,
		"suspended": 0,
	}

	// TODO: Implement real statistics by counting tenants with each status
	// This would require new methods in the repository

	// Thêm thông tin kết quả vào span
	for k, v := range stats {
		statAttrs := map[string]interface{}{
			"tenant.stats." + k: v,
		}
		tracing.AddTenantAttributes(otSpan, jaegerSpan, statAttrs)
	}

	return stats, nil
}
