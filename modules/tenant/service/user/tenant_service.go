package user

import (
	"context"
	"fmt"
	"time"

	"wnapi/modules/tenant/dto/user/tenant"
	"wnapi/modules/tenant/repository"
)

// TenantService handles user tenant operations
type TenantService struct {
	repo repository.TenantRepository
}

// NewTenantService creates a new user tenant service
func NewTenantService(repo repository.TenantRepository) *TenantService {
	return &TenantService{
		repo: repo,
	}
}

// C<PERSON> creates a new tenant
func (s *TenantService) Create(ctx context.Context, req tenant.CreateTenantRequest) (*tenant.TenantResponse, error) {
	// Kiểm tra nếu mã tenant đã tồn tại
	exists, err := s.repo.ExistsByCode(ctx, req.TenantCode)
	if err != nil {
		return nil, fmt.Errorf("failed to check tenant code: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("tenant code '%s' already exists", req.TenantCode)
	}

	// Thiết lập giá trị mặc định cho các trường nếu cần
	now := time.Now()

	// Tạo repository.Tenant từ request
	tenantData := repository.Tenant{
		Name:      req.TenantName,
		Code:      req.TenantCode,
		Status:    req.Status,
		PlanType:  req.PlanType,
		CreatedAt: now,
		UpdatedAt: now,
	}

	// Xử lý thời gian hết hạn nếu có
	var expiresAt *time.Time
	if req.SubscriptionExpiresAt != nil {
		t := req.SubscriptionExpiresAt.Time()
		expiresAt = &t
		// Gán trường SubscriptionExpiresAt trong tenantData nếu cần
	}

	// Lưu tenant vào database
	createdTenant, err := s.repo.Create(ctx, tenantData)
	if err != nil {
		return nil, fmt.Errorf("failed to create tenant: %w", err)
	}

	// Nếu có ngày hết hạn, cập nhật riêng
	if expiresAt != nil {
		// Định dạng thời gian theo chuẩn RFC3339 cho API
		expiresAtStr := expiresAt.Format(time.RFC3339)
		err = s.repo.UpdatePlan(ctx, createdTenant.ID, createdTenant.PlanType, &expiresAtStr)
		if err != nil {
			return nil, fmt.Errorf("failed to update subscription expiration: %w", err)
		}

		// Lấy lại tenant sau khi cập nhật
		createdTenant, err = s.repo.GetByID(ctx, createdTenant.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to retrieve updated tenant: %w", err)
		}
	}

	// Chuyển đổi model thành response
	response := tenant.FromRepository(createdTenant)
	return &response, nil
}

// GetByCode retrieves a tenant by code
func (s *TenantService) GetByCode(ctx context.Context, code string) (*tenant.TenantResponse, error) {
	tenantModel, err := s.repo.GetByCode(ctx, code)
	if err != nil {
		return nil, err
	}

	response := tenant.FromRepository(tenantModel)
	return &response, nil
}

// List retrieves a list of active tenants with pagination
func (s *TenantService) List(ctx context.Context, req tenant.ListTenantsRequest) (*tenant.ListTenantsResponse, error) {
	// Set default limit if not provided
	limit := req.Limit
	if limit <= 0 {
		limit = 10
	}

	// For user, always filter by active status if not specified
	status := req.Status
	if status == "" {
		status = "active"
	}

	// Create repository params
	params := repository.ListParams{
		Cursor: req.Cursor,
		Limit:  limit,
		Status: status,
	}

	// List tenants from repository
	tenants, nextCursor, hasMore, err := s.repo.List(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to list tenants: %w", err)
	}

	// Convert to response
	response := &tenant.ListTenantsResponse{
		Data: tenant.FromRepositoryList(tenants),
		Meta: struct {
			NextCursor string `json:"next_cursor"`
			HasMore    bool   `json:"has_more"`
		}{
			NextCursor: nextCursor,
			HasMore:    hasMore,
		},
	}

	return response, nil
}
