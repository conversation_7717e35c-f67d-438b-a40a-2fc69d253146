package internal

import (
	"context"
	"net/http"
	"time"
	"wnapi/modules/tenant/dto"
)

// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	// ErrTenantNotFound là lỗi khi không tìm thấy tenant
	ErrTenantNotFound ServiceError = "tenant_not_found"
	// ErrPlanNotFound là lỗi khi không tìm thấy plan
	ErrPlanNotFound ServiceError = "plan_not_found"
	// ErrUserNotFound là lỗi khi không tìm thấy user
	ErrUserNotFound ServiceError = "user_not_found"
	// ErrInvalidTenantData là lỗi khi dữ liệu tenant không hợp lệ
	ErrInvalidTenantData ServiceError = "invalid_tenant_data"
	// ErrTenantExistsWithName là lỗi khi tên tenant đã tồn tại
	ErrTenantExistsWithName ServiceError = "tenant_exists_with_name"
	// ErrTenantExistsWithDomain là lỗi khi domain tenant đã tồn tại
	ErrTenantExistsWithDomain ServiceError = "tenant_exists_with_domain"
	// ErrDatabaseError là lỗi khi tương tác với database
	ErrDatabaseError ServiceError = "database_error"
	// ErrInvalidSortField là lỗi khi trường sắp xếp không hợp lệ
	ErrInvalidSortField ServiceError = "invalid_sort_field"
	// ErrMaxTenantsPerAccountReached là lỗi khi đạt đến số lượng tenant tối đa cho tài khoản
	ErrMaxTenantsPerAccountReached ServiceError = "max_tenants_per_account_reached"
	// ErrMaxUsersPerTenantReached là lỗi khi đạt đến số lượng user tối đa cho tenant
	ErrMaxUsersPerTenantReached ServiceError = "max_users_per_tenant_reached"
)

// ErrorResponse định nghĩa cấu trúc phản hồi lỗi
type ErrorResponse struct {
	StatusCode int    // HTTP status code
	Message    string // Thông báo lỗi hiển thị cho người dùng
	ErrorCode  string // Mã lỗi nội bộ
}

// ErrorMap ánh xạ ServiceError với thông tin phản hồi lỗi tương ứng
var ErrorMap = map[ServiceError]ErrorResponse{
	ErrTenantNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy tenant",
		ErrorCode:  "TENANT_NOT_FOUND",
	},
	ErrPlanNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy gói dịch vụ",
		ErrorCode:  "PLAN_NOT_FOUND",
	},
	ErrUserNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy người dùng",
		ErrorCode:  "USER_NOT_FOUND",
	},
	ErrInvalidTenantData: {
		StatusCode: http.StatusBadRequest,
		Message:    "Dữ liệu tenant không hợp lệ",
		ErrorCode:  "INVALID_TENANT_DATA",
	},
	ErrTenantExistsWithName: {
		StatusCode: http.StatusConflict,
		Message:    "Tên tenant đã tồn tại",
		ErrorCode:  "TENANT_EXISTS_WITH_NAME",
	},
	ErrTenantExistsWithDomain: {
		StatusCode: http.StatusConflict,
		Message:    "Domain tenant đã tồn tại",
		ErrorCode:  "TENANT_EXISTS_WITH_DOMAIN",
	},
	ErrDatabaseError: {
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi cơ sở dữ liệu",
		ErrorCode:  "DATABASE_ERROR",
	},
	ErrInvalidSortField: {
		StatusCode: http.StatusBadRequest,
		Message:    "Trường sắp xếp không hợp lệ",
		ErrorCode:  "INVALID_SORT_FIELD",
	},
	ErrMaxTenantsPerAccountReached: {
		StatusCode: http.StatusForbidden,
		Message:    "Đã đạt đến số lượng tenant tối đa cho tài khoản",
		ErrorCode:  "MAX_TENANTS_PER_ACCOUNT_REACHED",
	},
	ErrMaxUsersPerTenantReached: {
		StatusCode: http.StatusForbidden,
		Message:    "Đã đạt đến số lượng người dùng tối đa cho tenant",
		ErrorCode:  "MAX_USERS_PER_TENANT_REACHED",
	},
}

func (e ServiceError) Error() string {
	return string(e)
}

// GetErrorResponse trả về thông tin phản hồi lỗi dựa trên ServiceError
func GetErrorResponse(err error) ErrorResponse {
	if serviceErr, ok := err.(ServiceError); ok {
		if resp, exists := ErrorMap[serviceErr]; exists {
			return resp
		}
	}

	// Mặc định trả về lỗi hệ thống nếu không tìm thấy lỗi trong map
	return ErrorResponse{
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi hệ thống",
		ErrorCode:  "INTERNAL_ERROR",
	}
}

// Tenant định nghĩa cấu trúc dữ liệu cho tenant
type Tenant struct {
	TenantID              uint       `json:"tenant_id" gorm:"primaryKey"`
	TenantName            string     `json:"tenant_name" gorm:"column:tenant_name"`
	TenantCode            string     `json:"tenant_code" gorm:"column:tenant_code"`
	CreatedAt             time.Time  `json:"created_at" gorm:"column:created_at;autoCreateTime"`
	UpdatedAt             time.Time  `json:"updated_at" gorm:"column:updated_at;autoUpdateTime"`
	Status                string     `json:"status" gorm:"column:status"`
	PlanType              string     `json:"plan_type" gorm:"column:plan_type"`
	SubscriptionExpiresAt *time.Time `json:"subscription_expires_at,omitempty" gorm:"column:subscription_expires_at"`
}

// TableName sets the insert table name for this struct type
func (Tenant) TableName() string {
	return "tenant_tenants"
}

// Plan định nghĩa cấu trúc dữ liệu cho gói dịch vụ
type Plan struct {
	ID          int       `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name        string    `gorm:"column:name;size:100;not null;uniqueIndex" json:"name"`
	DisplayName string    `gorm:"column:display_name;size:200" json:"display_name"`
	Description string    `gorm:"column:description;size:500" json:"description"`
	Price       float64   `gorm:"column:price;type:decimal(10,2);default:0" json:"price"`
	Interval    string    `gorm:"column:interval;size:20;default:monthly" json:"interval"`
	Features    string    `gorm:"column:features;type:json" json:"features"`
	MaxUsers    int       `gorm:"column:max_users;default:5" json:"max_users"`
	IsActive    bool      `gorm:"column:is_active;default:true" json:"is_active"`
	CreatedAt   time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName sets the insert table name for this struct type
func (Plan) TableName() string {
	return "tenant_plans"
}

// TenantUser định nghĩa cấu trúc dữ liệu cho mối quan hệ giữa tenant và user
type TenantUser struct {
	ID        int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	TenantID  int64     `gorm:"column:tenant_id;not null;index:idx_tenant_user,unique" json:"tenant_id"`
	UserID    int64     `gorm:"column:user_id;not null;index:idx_tenant_user,unique" json:"user_id"`
	Role      string    `gorm:"column:role;size:50;default:member" json:"role"`
	IsActive  bool      `gorm:"column:is_active;default:true" json:"is_active"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName sets the insert table name for this struct type
func (TenantUser) TableName() string {
	return "tenant_users"
}

// Repository định nghĩa interface cho tenant repository
type Repository interface {
	// Tenant methods
	CreateTenant(ctx context.Context, tenant *Tenant) error
	GetTenantByID(ctx context.Context, id int64) (*Tenant, error)
	GetTenantByName(ctx context.Context, name string) (*Tenant, error)
	GetTenantByDomain(ctx context.Context, domain string) (*Tenant, error)
	UpdateTenant(ctx context.Context, tenant *Tenant) error
	DeleteTenant(ctx context.Context, id int64) error
	ListTenants(ctx context.Context, params dto.ListTenantsParams) ([]Tenant, int64, error)
	GetUserTenants(ctx context.Context, userID int64) ([]Tenant, error)

	// Plan methods
	CreatePlan(ctx context.Context, plan *Plan) error
	GetPlanByID(ctx context.Context, id int) (*Plan, error)
	UpdatePlan(ctx context.Context, plan *Plan) error
	DeletePlan(ctx context.Context, id int) error
	ListPlans(ctx context.Context, params dto.ListPlansParams) ([]Plan, int64, error)

	// Tenant User methods
	AddUserToTenant(ctx context.Context, tenantUser *TenantUser) error
	RemoveUserFromTenant(ctx context.Context, tenantID, userID int64) error
	GetTenantUsers(ctx context.Context, tenantID int64, params dto.ListTenantUsersParams) ([]TenantUser, int64, error)
	UpdateTenantUserRole(ctx context.Context, tenantID, userID int64, role string) error
	GetTenantUser(ctx context.Context, tenantID, userID int64) (*TenantUser, error)
	CountTenantUsers(ctx context.Context, tenantID int64) (int64, error)
	CountUserTenants(ctx context.Context, userID int64) (int64, error)
}

// TenantService định nghĩa interface cho tenant service
type TenantService interface {
	// Tenant methods
	CreateTenant(ctx context.Context, req dto.CreateTenantRequest) (*dto.TenantResponse, error)
	GetTenant(ctx context.Context, id int64) (*dto.TenantResponse, error)
	GetTenantByName(ctx context.Context, name string) (*dto.TenantResponse, error)
	GetTenantByDomain(ctx context.Context, domain string) (*dto.TenantResponse, error)
	UpdateTenant(ctx context.Context, id int64, req dto.UpdateTenantRequest) (*dto.TenantResponse, error)
	DeleteTenant(ctx context.Context, id int64) error
	ListTenants(ctx context.Context, params dto.ListTenantsParams) (*dto.ListTenantsResponse, error)
	GetUserTenants(ctx context.Context, userID int64) (*dto.UserTenantsResponse, error)

	// Plan methods
	CreatePlan(ctx context.Context, req dto.CreatePlanRequest) (*dto.PlanResponse, error)
	GetPlan(ctx context.Context, id int) (*dto.PlanResponse, error)
	UpdatePlan(ctx context.Context, id int, req dto.UpdatePlanRequest) (*dto.PlanResponse, error)
	DeletePlan(ctx context.Context, id int) error
	ListPlans(ctx context.Context, params dto.ListPlansParams) (*dto.ListPlansResponse, error)

	// Tenant User methods
	AddUserToTenant(ctx context.Context, req dto.AddUserToTenantRequest) (*dto.TenantUserResponse, error)
	RemoveUserFromTenant(ctx context.Context, tenantID, userID int64) error
	GetTenantUsers(ctx context.Context, tenantID int64, params dto.ListTenantUsersParams) (*dto.ListTenantUsersResponse, error)
	UpdateTenantUserRole(ctx context.Context, tenantID, userID int64, req dto.UpdateTenantUserRoleRequest) (*dto.TenantUserResponse, error)
}
