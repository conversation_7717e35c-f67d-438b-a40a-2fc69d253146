package user

import (
	"net/http"

	"wnapi/modules/tenant/api/handlers"
	"wnapi/modules/tenant/dto/user/tenant"
	"wnapi/modules/tenant/service/user"

	"github.com/gin-gonic/gin"
)

// TenantHandler handles HTTP requests for tenant user operations
type TenantHandler struct {
	service *user.TenantService
}

// NewTenantHandler creates a new user tenant handler
func NewTenantHandler(service *user.TenantService) *TenantHandler {
	return &TenantHandler{
		service: service,
	}
}

// Create handles POST /api/v1/tenants
func (h *TenantHandler) Create(c *gin.Context) {
	var req tenant.CreateTenantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{
			map[string]string{
				"message": err.Error(),
			},
		}
		handlers.ApiErrorWithDetails(c, http.StatusBadRequest, handlers.ErrCodeInvalidTenantData, "Invalid request data", details)
		return
	}

	// Lấy tenant ID từ JWT token nếu cần
	// tenantID, err := handlers.GetTenantIDFromContext(c)
	// if err != nil {
	//    handlers.ApiErrorWithDetails(...)
	//    return
	// }

	result, err := h.service.Create(c, req)
	if err != nil {
		details := []interface{}{
			map[string]string{
				"message": err.Error(),
			},
		}
		handlers.ApiErrorWithDetails(c, http.StatusInternalServerError, handlers.ErrCodeInternalServer, "Failed to create tenant", details)
		return
	}

	handlers.ApiSuccess(c, http.StatusCreated, "Tenant created successfully", result)
}

// GetByCode handles GET /api/v1/tenants/:tenant_code
func (h *TenantHandler) GetByCode(c *gin.Context) {
	var req tenant.GetTenantRequest
	if err := c.ShouldBindUri(&req); err != nil {
		details := []interface{}{
			map[string]string{
				"message": err.Error(),
			},
		}
		handlers.ApiErrorWithDetails(c, http.StatusBadRequest, handlers.ErrCodeInvalidTenantData, "Invalid tenant code", details)
		return
	}

	result, err := h.service.GetByCode(c, req.TenantCode)
	if err != nil {
		details := []interface{}{
			map[string]string{
				"message": err.Error(),
			},
		}
		handlers.ApiErrorWithDetails(c, http.StatusNotFound, handlers.ErrCodeTenantNotFound, "Tenant not found", details)
		return
	}

	handlers.ApiSuccess(c, http.StatusOK, "Tenant retrieved successfully", result)
}

// List handles GET /api/v1/tenants
func (h *TenantHandler) List(c *gin.Context) {
	var req tenant.ListTenantsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{
			map[string]string{
				"message": err.Error(),
			},
		}
		handlers.ApiErrorWithDetails(c, http.StatusBadRequest, handlers.ErrCodeInvalidTenantData, "Invalid request parameters", details)
		return
	}

	result, err := h.service.List(c, req)
	if err != nil {
		details := []interface{}{
			map[string]string{
				"message": err.Error(),
			},
		}
		handlers.ApiErrorWithDetails(c, http.StatusInternalServerError, handlers.ErrCodeInternalServer, "Failed to list tenants", details)
		return
	}

	meta := map[string]interface{}{
		"next_cursor": result.Meta.NextCursor,
		"has_more":    result.Meta.HasMore,
	}

	handlers.ApiSuccessWithMeta(c, http.StatusOK, "Tenants retrieved successfully", result.Data, meta)
}
