package handlers

import (
	"errors"
	"net/http"
	"strconv"

	"wnapi/modules/tenant/dto/common"
	"wnapi/pkg/auth"
	"wnapi/pkg/response"

	"github.com/gin-gonic/gin"
)

// Constants for common error codes
const (
	ErrCodeInvalidTenantData  = common.ErrorCodeInvalidTenantData
	ErrCodeTenantNotFound     = common.ErrorCodeTenantNotFound
	ErrCodeInternalServer     = common.ErrorCodeInternalServer
	ErrCodeTenantAlreadyExist = common.ErrorCodeTenantAlreadyExist
	ErrCodeNotFound           = "NOT_FOUND"
	ErrCodeInvalidInput       = "INVALID_INPUT"
	ErrCodeDuplicateData      = "DUPLICATE_DATA"
	ErrCodeServerError        = "SERVER_ERROR"
	ErrCodeUnauthorized       = "UNAUTHORIZED"
)

// getTenantIDFromContext lấy tenant ID từ context request
func getTenantIDFromContext(c *gin.Context) (string, error) {
	// <PERSON><PERSON><PERSON> claims từ context
	claims, exists := auth.GetClaimsFromContext(c)
	if !exists {
		return "", errors.New("không tìm thấy thông tin xác thực")
	}

	return strconv.Itoa(int(claims.TenantID)), nil
}

// getUserIDFromContext lấy user ID từ context request
func getUserIDFromContext(c *gin.Context) (string, error) {
	// Lấy user ID từ context
	userID, exists := auth.GetUserIDFromContext(c)
	if !exists {
		return "", errors.New("không tìm thấy thông tin người dùng")
	}

	return strconv.Itoa(int(userID)), nil
}

// handleError xử lý lỗi chung cho các handler
func handleError(c *gin.Context, err error) {
	// Mặc định là lỗi server
	statusCode := http.StatusInternalServerError
	message := "Có lỗi xảy ra, vui lòng thử lại sau"
	errorCode := ErrCodeServerError

	// Ở đây bạn có thể thêm các trường hợp lỗi cụ thể khác
	// if errors.Is(err, customError) { ... }

	details := []interface{}{map[string]string{"message": err.Error()}}
	response.ErrorWithDetails(c, statusCode, message, errorCode, details)
}

// apiSuccess sends a successful API response
func ApiSuccess(c *gin.Context, statusCode int, message string, data interface{}) {
	response.Success(c, statusCode, message, data)
}

// apiSuccessWithMeta sends a successful API response with metadata
func ApiSuccessWithMeta(c *gin.Context, statusCode int, message string, data interface{}, meta map[string]interface{}) {
	response.SuccessWithMeta(c, statusCode, message, data, meta)
}

// apiError sends an error API response
func ApiError(c *gin.Context, statusCode int, errorCode string, message string) {
	response.ErrorWithDetails(c, statusCode, message, errorCode, nil)
}

// apiErrorWithDetails sends an error API response with detailed information
func ApiErrorWithDetails(c *gin.Context, statusCode int, errorCode string, message string, details interface{}) {
	response.ErrorWithDetails(c, statusCode, message, errorCode, details)
}

// handleInvalidTenantIDError handles invalid tenant ID errors
func HandleInvalidTenantIDError(c *gin.Context, err error) {
	details := []interface{}{
		map[string]string{
			"message": err.Error(),
		},
	}
	ApiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidTenantData, "Invalid tenant ID", details)
}

// handleInvalidRequestDataError handles invalid request data errors
func HandleInvalidRequestDataError(c *gin.Context, err error) {
	details := []interface{}{
		map[string]string{
			"message": err.Error(),
		},
	}
	ApiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidTenantData, "Invalid request data", details)
}

// handleTenantNotFoundError handles tenant not found errors
func HandleTenantNotFoundError(c *gin.Context, err error) {
	details := []interface{}{
		map[string]string{
			"message": err.Error(),
		},
	}
	ApiErrorWithDetails(c, http.StatusNotFound, ErrCodeTenantNotFound, "Tenant not found", details)
}

// handleServerError handles server errors
func HandleServerError(c *gin.Context, message string, err error) {
	details := []interface{}{
		map[string]string{
			"message": err.Error(),
		},
	}
	ApiErrorWithDetails(c, http.StatusInternalServerError, ErrCodeInternalServer, message, details)
}
