package routes

import (
	"wnapi/modules/tenant/api/handlers/user"
	"wnapi/pkg/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterUserRoutes đăng ký routes cho user API
func RegisterUserRoutes(router *gin.RouterGroup, handler *user.TenantHandler, permService middleware.PermissionService) {
	// Tenant routes
	tenants := router.Group("/tenants")
	{
		tenants.GET("", middleware.RequirePermission(permService, "tenants.read"), handler.List)
		tenants.GET("/:tenant_code", middleware.RequirePermission(permService, "tenants.read"), handler.GetByCode)
		tenants.POST("", middleware.RequirePermission(permService, "tenants.create"), handler.Create)
	}
}
