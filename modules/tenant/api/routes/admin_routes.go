package routes

import (
	"wnapi/modules/tenant/api/handlers/admin"
	"wnapi/pkg/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterAdminRoutes đăng ký routes cho admin API
func RegisterAdminRoutes(router *gin.RouterGroup, handler *admin.TenantHandler, permService middleware.PermissionService) {
	// Tenant routes
	tenants := router.Group("/admin/tenants")
	{
		tenants.GET("", middleware.RequirePermission(permService, "tenants.read"), handler.List)
		tenants.POST("", middleware.RequirePermission(permService, "tenants.create"), handler.Create)
		tenants.GET("/:tenant_id", middleware.RequirePermission(permService, "tenants.read"), handler.GetByID)
		tenants.PUT("/:tenant_id", middleware.RequirePermission(permService, "tenants.update"), handler.Update)
		tenants.DELETE("/:tenant_id", middleware.RequirePermission(permService, "tenants.delete"), handler.Delete)
		tenants.PATCH("/:tenant_id/status", middleware.RequirePermission(permService, "tenants.manage_status"), handler.UpdateStatus)
		tenants.PATCH("/:tenant_id/plan", middleware.RequirePermission(permService, "tenants.manage_plan"), handler.UpdatePlan)
	}
}
