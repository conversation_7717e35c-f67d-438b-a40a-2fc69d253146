package dto

import "time"

// Pagination chứa thông tin phân trang
type Pagination struct {
	CurrentPage int   `json:"current_page"`
	PageSize    int   `json:"page_size"`
	TotalItems  int64 `json:"total_items"`
	TotalPages  int   `json:"total_pages"`
	HasNext     bool  `json:"has_next"`
	HasPrev     bool  `json:"has_prev"`
}

// Tenant DTOs
// ------------------------------

// CreateTenantRequest chứa thông tin để tạo tenant mới
type CreateTenantRequest struct {
	Name        string `json:"name" binding:"required,max=100,alphanum"`
	DisplayName string `json:"display_name" binding:"required,max=200"`
	Domain      string `json:"domain" binding:"required,max=255,hostname"`
	Description string `json:"description" binding:"max=500"`
	PlanID      int    `json:"plan_id" binding:"required"`
	OwnerID     int64  `json:"owner_id" binding:"required"`
	Logo        string `json:"logo" binding:"omitempty,url"`
	TimeZone    string `json:"time_zone" binding:"omitempty,max=50"`
	IsActive    bool   `json:"is_active" binding:"omitempty"`
	Settings    string `json:"settings" binding:"omitempty,json"`
}

// UpdateTenantRequest chứa thông tin để cập nhật tenant
type UpdateTenantRequest struct {
	DisplayName *string `json:"display_name" binding:"omitempty,max=200"`
	Domain      *string `json:"domain" binding:"omitempty,max=255,hostname"`
	Description *string `json:"description" binding:"omitempty,max=500"`
	PlanID      *int    `json:"plan_id" binding:"omitempty"`
	Logo        *string `json:"logo" binding:"omitempty,url"`
	TimeZone    *string `json:"time_zone" binding:"omitempty,max=50"`
	IsActive    *bool   `json:"is_active" binding:"omitempty"`
	Settings    *string `json:"settings" binding:"omitempty,json"`
}

// TenantResponse chứa thông tin trả về về tenant
type TenantResponse struct {
	ID          int64      `json:"id"`
	Name        string     `json:"name"`
	DisplayName string     `json:"display_name"`
	Domain      string     `json:"domain"`
	Description string     `json:"description"`
	PlanID      int        `json:"plan_id"`
	PlanName    string     `json:"plan_name,omitempty"`
	IsActive    bool       `json:"is_active"`
	Logo        string     `json:"logo"`
	Settings    string     `json:"settings"`
	TimeZone    string     `json:"time_zone"`
	OwnerID     int64      `json:"owner_id"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// ListTenantsParams chứa các tham số để lọc và phân trang danh sách tenant
type ListTenantsParams struct {
	Page      int    `form:"page" binding:"omitempty,min=1"`
	PageSize  int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	SortBy    string `form:"sort_by" binding:"omitempty"`
	SortOrder string `form:"sort_order" binding:"omitempty,oneof=asc desc"`
	Search    string `form:"search" binding:"omitempty"`
	PlanID    *int   `form:"plan_id" binding:"omitempty"`
	IsActive  *bool  `form:"is_active" binding:"omitempty"`
	OwnerID   *int64 `form:"owner_id" binding:"omitempty"`
}

// ListTenantsResponse chứa kết quả danh sách tenant có phân trang
type ListTenantsResponse struct {
	Tenants    []TenantResponse `json:"tenants"`
	Pagination Pagination       `json:"pagination"`
}

// UserTenantsResponse chứa danh sách tenant của một user
type UserTenantsResponse struct {
	Tenants []TenantResponse `json:"tenants"`
}

// Plan DTOs
// ------------------------------

// CreatePlanRequest chứa thông tin để tạo plan mới
type CreatePlanRequest struct {
	Name        string  `json:"name" binding:"required,max=100,alphanum"`
	DisplayName string  `json:"display_name" binding:"required,max=200"`
	Description string  `json:"description" binding:"max=500"`
	Price       float64 `json:"price" binding:"min=0"`
	Interval    string  `json:"interval" binding:"required,oneof=monthly yearly quarterly"`
	Features    string  `json:"features" binding:"omitempty,json"`
	MaxUsers    int     `json:"max_users" binding:"required,min=1"`
	IsActive    bool    `json:"is_active" binding:"omitempty"`
}

// UpdatePlanRequest chứa thông tin để cập nhật plan
type UpdatePlanRequest struct {
	DisplayName *string  `json:"display_name" binding:"omitempty,max=200"`
	Description *string  `json:"description" binding:"omitempty,max=500"`
	Price       *float64 `json:"price" binding:"omitempty,min=0"`
	Interval    *string  `json:"interval" binding:"omitempty,oneof=monthly yearly quarterly"`
	Features    *string  `json:"features" binding:"omitempty,json"`
	MaxUsers    *int     `json:"max_users" binding:"omitempty,min=1"`
	IsActive    *bool    `json:"is_active" binding:"omitempty"`
}

// PlanResponse chứa thông tin trả về về plan
type PlanResponse struct {
	ID          int       `json:"id"`
	Name        string    `json:"name"`
	DisplayName string    `json:"display_name"`
	Description string    `json:"description"`
	Price       float64   `json:"price"`
	Interval    string    `json:"interval"`
	Features    string    `json:"features"`
	MaxUsers    int       `json:"max_users"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ListPlansParams chứa các tham số để lọc và phân trang danh sách plan
type ListPlansParams struct {
	Page      int    `form:"page" binding:"omitempty,min=1"`
	PageSize  int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	SortBy    string `form:"sort_by" binding:"omitempty"`
	SortOrder string `form:"sort_order" binding:"omitempty,oneof=asc desc"`
	Search    string `form:"search" binding:"omitempty"`
	IsActive  *bool  `form:"is_active" binding:"omitempty"`
}

// ListPlansResponse chứa kết quả danh sách plan có phân trang
type ListPlansResponse struct {
	Plans      []PlanResponse `json:"plans"`
	Pagination Pagination     `json:"pagination"`
}

// Tenant User DTOs
// ------------------------------

// AddUserToTenantRequest chứa thông tin để thêm user vào tenant
type AddUserToTenantRequest struct {
	TenantID int64  `json:"tenant_id" binding:"required"`
	UserID   int64  `json:"user_id" binding:"required"`
	Role     string `json:"role" binding:"required,oneof=owner admin member viewer"`
}

// UpdateTenantUserRoleRequest chứa thông tin để cập nhật vai trò của user trong tenant
type UpdateTenantUserRoleRequest struct {
	Role string `json:"role" binding:"required,oneof=admin member viewer"`
}

// TenantUserResponse chứa thông tin trả về về mối quan hệ user-tenant
type TenantUserResponse struct {
	ID        int64     `json:"id"`
	TenantID  int64     `json:"tenant_id"`
	UserID    int64     `json:"user_id"`
	Role      string    `json:"role"`
	IsActive  bool      `json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	// Thông tin bổ sung về user nếu cần
	UserEmail    string `json:"user_email,omitempty"`
	UserFullName string `json:"user_full_name,omitempty"`
}

// ListTenantUsersParams chứa các tham số để lọc và phân trang danh sách user trong tenant
type ListTenantUsersParams struct {
	Page      int    `form:"page" binding:"omitempty,min=1"`
	PageSize  int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	SortBy    string `form:"sort_by" binding:"omitempty"`
	SortOrder string `form:"sort_order" binding:"omitempty,oneof=asc desc"`
	Search    string `form:"search" binding:"omitempty"`
	Role      string `form:"role" binding:"omitempty,oneof=owner admin member viewer"`
	IsActive  *bool  `form:"is_active" binding:"omitempty"`
}

// ListTenantUsersResponse chứa kết quả danh sách user trong tenant có phân trang
type ListTenantUsersResponse struct {
	Users      []TenantUserResponse `json:"users"`
	Pagination Pagination           `json:"pagination"`
}
