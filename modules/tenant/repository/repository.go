package repository

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/tenant/dto"
	"wnapi/modules/tenant/internal"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// mysqlRepository triển khai Repository interface sử dụng GORM
type mysqlRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewMySQLRepository tạo một repository mới
func NewMySQLRepository(dbManager *database.Manager, logger logger.Logger) (internal.Repository, error) {
	if dbManager == nil {
		return nil, errors.New("database manager không được để trống")
	}

	sqlxDB := dbManager.GetDB()
	if sqlxDB == nil {
		return nil, errors.New("không thể kết nối tới cơ sở dữ liệu")
	}

	// Create GORM DB from sqlx DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlxDB.DB,
	}), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to initialize GORM: %w", err)
	}

	return &mysqlRepository{
		db:     gormDB,
		logger: logger,
	}, nil
}

// Tenant methods
// ---------------------------------

// CreateTenant tạo tenant mới
func (r *mysqlRepository) CreateTenant(ctx context.Context, tenant *internal.Tenant) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "tenant_tenants")
	defer span.End()

	result := r.db.WithContext(ctx).Create(tenant)
	if result.Error != nil {
		r.logger.Error("Không thể tạo tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	return nil
}

// GetTenantByID lấy tenant theo ID
func (r *mysqlRepository) GetTenantByID(ctx context.Context, id int64) (*internal.Tenant, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	var tenant internal.Tenant
	result := r.db.WithContext(ctx).First(&tenant, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrTenantNotFound
		}
		r.logger.Error("Không thể lấy tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &tenant, nil
}

// GetTenantByName lấy tenant theo tên
func (r *mysqlRepository) GetTenantByName(ctx context.Context, name string) (*internal.Tenant, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	var tenant internal.Tenant
	result := r.db.WithContext(ctx).Where("name = ?", name).First(&tenant)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrTenantNotFound
		}
		r.logger.Error("Không thể lấy tenant theo tên", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &tenant, nil
}

// GetTenantByDomain lấy tenant theo domain
func (r *mysqlRepository) GetTenantByDomain(ctx context.Context, domain string) (*internal.Tenant, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	var tenant internal.Tenant
	result := r.db.WithContext(ctx).Where("domain = ?", domain).First(&tenant)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrTenantNotFound
		}
		r.logger.Error("Không thể lấy tenant theo domain", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &tenant, nil
}

// UpdateTenant cập nhật tenant
func (r *mysqlRepository) UpdateTenant(ctx context.Context, tenant *internal.Tenant) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "tenant_tenants")
	defer span.End()

	result := r.db.WithContext(ctx).Save(tenant)
	if result.Error != nil {
		r.logger.Error("Không thể cập nhật tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrTenantNotFound
	}

	return nil
}

// DeleteTenant xóa tenant
func (r *mysqlRepository) DeleteTenant(ctx context.Context, id int64) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "tenant_tenants")
	defer span.End()

	// Sử dụng transaction để đảm bảo xóa cả tenant users
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		r.logger.Error("Không thể bắt đầu transaction", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return internal.ErrDatabaseError
	}

	// Xóa tenant users trước
	if err := tx.Where("tenant_id = ?", id).Delete(&internal.TenantUser{}).Error; err != nil {
		tx.Rollback()
		r.logger.Error("Không thể xóa tenant users", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return internal.ErrDatabaseError
	}

	// Xóa tenant
	result := tx.Delete(&internal.Tenant{}, id)
	if result.Error != nil {
		tx.Rollback()
		r.logger.Error("Không thể xóa tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		tx.Rollback()
		return internal.ErrTenantNotFound
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		r.logger.Error("Không thể commit transaction", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return internal.ErrDatabaseError
	}

	return nil
}

// ListTenants lấy danh sách tenant với phân trang và lọc
func (r *mysqlRepository) ListTenants(ctx context.Context, params dto.ListTenantsParams) ([]internal.Tenant, int64, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	query := r.db.WithContext(ctx).Model(&internal.Tenant{})

	// Áp dụng các điều kiện lọc
	if params.Search != "" {
		searchTerm := "%" + params.Search + "%"
		query = query.Where("name LIKE ? OR display_name LIKE ? OR domain LIKE ? OR description LIKE ?",
			searchTerm, searchTerm, searchTerm, searchTerm)
	}

	if params.PlanID != nil {
		query = query.Where("plan_id = ?", *params.PlanID)
	}

	if params.IsActive != nil {
		query = query.Where("is_active = ?", *params.IsActive)
	}

	if params.OwnerID != nil {
		query = query.Where("owner_id = ?", *params.OwnerID)
	}

	// Đếm tổng số bản ghi
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error("Không thể đếm tổng số tenant", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	// Xác định thứ tự sắp xếp
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := params.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	// Áp dụng phân trang và sắp xếp
	page := params.Page
	if page <= 0 {
		page = 1
	}

	pageSize := params.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	query = query.Order(clause.OrderByColumn{Column: clause.Column{Name: sortBy}, Desc: strings.ToLower(sortOrder) == "desc"})
	query = query.Offset(offset).Limit(pageSize)

	// Lấy dữ liệu
	var tenants []internal.Tenant
	if err := query.Find(&tenants).Error; err != nil {
		r.logger.Error("Không thể lấy danh sách tenant", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	return tenants, total, nil
}

// GetUserTenants lấy danh sách tenant của một user
func (r *mysqlRepository) GetUserTenants(ctx context.Context, userID int64) ([]internal.Tenant, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	var tenants []internal.Tenant
	result := r.db.WithContext(ctx).
		Joins("JOIN tenant_users ON tenant_users.tenant_id = tenant_tenants.id").
		Where("tenant_users.user_id = ? AND tenant_users.is_active = true", userID).
		Find(&tenants)

	if result.Error != nil {
		r.logger.Error("Không thể lấy danh sách tenant của user", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return tenants, nil
}

// Plan methods
// ---------------------------------

// CreatePlan tạo plan mới
func (r *mysqlRepository) CreatePlan(ctx context.Context, plan *internal.Plan) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "tenant_plans")
	defer span.End()

	result := r.db.WithContext(ctx).Create(plan)
	if result.Error != nil {
		r.logger.Error("Không thể tạo plan", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	return nil
}

// GetPlanByID lấy plan theo ID
func (r *mysqlRepository) GetPlanByID(ctx context.Context, id int) (*internal.Plan, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_plans")
	defer span.End()

	var plan internal.Plan
	result := r.db.WithContext(ctx).First(&plan, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrPlanNotFound
		}
		r.logger.Error("Không thể lấy plan", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &plan, nil
}

// UpdatePlan cập nhật plan
func (r *mysqlRepository) UpdatePlan(ctx context.Context, plan *internal.Plan) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "tenant_plans")
	defer span.End()

	result := r.db.WithContext(ctx).Save(plan)
	if result.Error != nil {
		r.logger.Error("Không thể cập nhật plan", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrPlanNotFound
	}

	return nil
}

// DeletePlan xóa plan
func (r *mysqlRepository) DeletePlan(ctx context.Context, id int) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "tenant_plans")
	defer span.End()

	// Kiểm tra xem có tenant nào đang sử dụng plan này không
	var count int64
	if err := r.db.WithContext(ctx).Model(&internal.Tenant{}).Where("plan_id = ?", id).Count(&count).Error; err != nil {
		r.logger.Error("Không thể kiểm tra tenant sử dụng plan", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return internal.ErrDatabaseError
	}

	if count > 0 {
		return fmt.Errorf("không thể xóa plan vì đang được sử dụng bởi %d tenant", count)
	}

	result := r.db.WithContext(ctx).Delete(&internal.Plan{}, id)
	if result.Error != nil {
		r.logger.Error("Không thể xóa plan", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrPlanNotFound
	}

	return nil
}

// ListPlans lấy danh sách plan với phân trang và lọc
func (r *mysqlRepository) ListPlans(ctx context.Context, params dto.ListPlansParams) ([]internal.Plan, int64, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_plans")
	defer span.End()

	query := r.db.WithContext(ctx).Model(&internal.Plan{})

	// Áp dụng các điều kiện lọc
	if params.Search != "" {
		searchTerm := "%" + params.Search + "%"
		query = query.Where("name LIKE ? OR display_name LIKE ? OR description LIKE ?",
			searchTerm, searchTerm, searchTerm)
	}

	if params.IsActive != nil {
		query = query.Where("is_active = ?", *params.IsActive)
	}

	// Đếm tổng số bản ghi
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error("Không thể đếm tổng số plan", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	// Xác định thứ tự sắp xếp
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := params.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	// Áp dụng phân trang và sắp xếp
	page := params.Page
	if page <= 0 {
		page = 1
	}

	pageSize := params.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	query = query.Order(clause.OrderByColumn{Column: clause.Column{Name: sortBy}, Desc: strings.ToLower(sortOrder) == "desc"})
	query = query.Offset(offset).Limit(pageSize)

	// Lấy dữ liệu
	var plans []internal.Plan
	if err := query.Find(&plans).Error; err != nil {
		r.logger.Error("Không thể lấy danh sách plan", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	return plans, total, nil
}

// Tenant User methods
// ---------------------------------

// AddUserToTenant thêm user vào tenant
func (r *mysqlRepository) AddUserToTenant(ctx context.Context, tenantUser *internal.TenantUser) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "tenant_users")
	defer span.End()

	result := r.db.WithContext(ctx).Create(tenantUser)
	if result.Error != nil {
		// Kiểm tra lỗi duplicate key
		if strings.Contains(result.Error.Error(), "Duplicate entry") {
			// Cập nhật bản ghi hiện tại nếu đã tồn tại
			updateResult := r.db.WithContext(ctx).
				Model(&internal.TenantUser{}).
				Where("tenant_id = ? AND user_id = ?", tenantUser.TenantID, tenantUser.UserID).
				Updates(map[string]interface{}{
					"role":      tenantUser.Role,
					"is_active": tenantUser.IsActive,
				})
			if updateResult.Error != nil {
				r.logger.Error("Không thể cập nhật tenant user", logger.String("error", updateResult.Error.Error()))
				tracing.RecordError(ctx, updateResult.Error)
				return internal.ErrDatabaseError
			}
			return nil
		}

		r.logger.Error("Không thể thêm user vào tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	return nil
}

// RemoveUserFromTenant xóa user khỏi tenant
func (r *mysqlRepository) RemoveUserFromTenant(ctx context.Context, tenantID, userID int64) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "tenant_users")
	defer span.End()

	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND user_id = ?", tenantID, userID).
		Delete(&internal.TenantUser{})

	if result.Error != nil {
		r.logger.Error("Không thể xóa user khỏi tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrUserNotFound
	}

	return nil
}

// GetTenantUsers lấy danh sách user trong tenant với phân trang và lọc
func (r *mysqlRepository) GetTenantUsers(ctx context.Context, tenantID int64, params dto.ListTenantUsersParams) ([]internal.TenantUser, int64, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_users")
	defer span.End()

	query := r.db.WithContext(ctx).
		Model(&internal.TenantUser{}).
		Where("tenant_id = ?", tenantID)

	// Áp dụng các điều kiện lọc
	if params.Role != "" {
		query = query.Where("role = ?", params.Role)
	}

	if params.IsActive != nil {
		query = query.Where("is_active = ?", *params.IsActive)
	}

	// Đếm tổng số bản ghi
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error("Không thể đếm tổng số tenant user", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	// Xác định thứ tự sắp xếp
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := params.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	// Áp dụng phân trang và sắp xếp
	page := params.Page
	if page <= 0 {
		page = 1
	}

	pageSize := params.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	query = query.Order(clause.OrderByColumn{Column: clause.Column{Name: sortBy}, Desc: strings.ToLower(sortOrder) == "desc"})
	query = query.Offset(offset).Limit(pageSize)

	// Lấy dữ liệu
	var users []internal.TenantUser
	if err := query.Find(&users).Error; err != nil {
		r.logger.Error("Không thể lấy danh sách tenant user", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	return users, total, nil
}

// UpdateTenantUserRole cập nhật vai trò của user trong tenant
func (r *mysqlRepository) UpdateTenantUserRole(ctx context.Context, tenantID, userID int64, role string) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "tenant_users")
	defer span.End()

	result := r.db.WithContext(ctx).
		Model(&internal.TenantUser{}).
		Where("tenant_id = ? AND user_id = ?", tenantID, userID).
		Update("role", role)

	if result.Error != nil {
		r.logger.Error("Không thể cập nhật vai trò user trong tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrUserNotFound
	}

	return nil
}

// GetTenantUser lấy thông tin mối quan hệ giữa tenant và user
func (r *mysqlRepository) GetTenantUser(ctx context.Context, tenantID, userID int64) (*internal.TenantUser, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_users")
	defer span.End()

	var tenantUser internal.TenantUser
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND user_id = ?", tenantID, userID).
		First(&tenantUser)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrUserNotFound
		}
		r.logger.Error("Không thể lấy thông tin tenant user", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &tenantUser, nil
}

// CountTenantUsers đếm số lượng user trong tenant
func (r *mysqlRepository) CountTenantUsers(ctx context.Context, tenantID int64) (int64, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "COUNT", "tenant_users")
	defer span.End()

	var count int64
	result := r.db.WithContext(ctx).
		Model(&internal.TenantUser{}).
		Where("tenant_id = ?", tenantID).
		Count(&count)

	if result.Error != nil {
		r.logger.Error("Không thể đếm số lượng user trong tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return 0, internal.ErrDatabaseError
	}

	return count, nil
}

// CountUserTenants đếm số lượng tenant của user
func (r *mysqlRepository) CountUserTenants(ctx context.Context, userID int64) (int64, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "COUNT", "tenant_users")
	defer span.End()

	var count int64
	result := r.db.WithContext(ctx).
		Model(&internal.TenantUser{}).
		Where("user_id = ?", userID).
		Count(&count)

	if result.Error != nil {
		r.logger.Error("Không thể đếm số lượng tenant của user", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return 0, internal.ErrDatabaseError
	}

	return count, nil
}
