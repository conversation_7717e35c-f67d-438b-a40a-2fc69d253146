package mysql

import (
	"context"
	"database/sql"
	stderrors "errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/models"
)

// Define error variants
var (
	ErrNotFound = stderrors.New("not found")
	ErrConflict = stderrors.New("conflict")
)

// CategoryRepository implements the repository.CategoryRepository interface with MySQL
type CategoryRepository struct {
	db *sqlx.DB
}

// NewCategoryRepository creates a new CategoryRepository instance
func NewCategoryRepository(db *sqlx.DB) *CategoryRepository {
	return &CategoryRepository{
		db: db,
	}
}

// Create adds a new category to the database
func (r *CategoryRepository) Create(ctx context.Context, category *models.Category) error {
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Generate slug if not provided
	if category.Slug == "" {
		category.Slug = generateSlug(category.Name)
	}

	// Check if slug exists for this tenant
	var count uint
	err = tx.GetContext(ctx, &count, "SELECT COUNT(*) FROM blog_categories WHERE tenant_id = ? AND slug = ?",
		category.TenantID, category.Slug)
	if err != nil {
		return fmt.Errorf("failed to check slug uniqueness: %w", err)
	}

	if count > 0 {
		return ErrConflict
	}

	// Get parent's left, right, and depth values if parent exists
	var left, right, depth uint
	if category.ParentID != nil {
		var parent models.Category
		err = tx.GetContext(ctx, &parent,
			"SELECT lft, rgt, depth FROM blog_categories WHERE tenant_id = ? AND category_id = ?",
			category.TenantID, *category.ParentID)

		if err != nil {
			if err == sql.ErrNoRows {
				return ErrNotFound
			}
			return fmt.Errorf("failed to get parent category: %w", err)
		}

		// New node will be inserted as the last child
		left = uint(parent.Right)
		right = uint(parent.Right + 1)
		depth = uint(parent.Depth + 1)

		// Make space for the new node by updating existing nodes
		_, err = tx.ExecContext(ctx,
			"UPDATE blog_categories SET rgt = rgt + 2 WHERE tenant_id = ? AND rgt >= ? ORDER BY rgt DESC",
			category.TenantID, parent.Right)
		if err != nil {
			return fmt.Errorf("failed to update right values: %w", err)
		}

		_, err = tx.ExecContext(ctx,
			"UPDATE blog_categories SET lft = lft + 2 WHERE tenant_id = ? AND lft > ? ORDER BY lft DESC",
			category.TenantID, parent.Right)
		if err != nil {
			return fmt.Errorf("failed to update left values: %w", err)
		}
	} else {
		// Find the right-most value to place the new root node after
		var maxRight uint
		err = tx.GetContext(ctx, &maxRight,
			"SELECT COALESCE(MAX(rgt), 0) FROM blog_categories WHERE tenant_id = ?",
			category.TenantID)
		if err != nil {
			return fmt.Errorf("failed to get max right value: %w", err)
		}

		left = maxRight + 1
		right = maxRight + 2
		depth = 0
	}

	// Insert the new category
	query := `
		INSERT INTO blog_categories (
			tenant_id, parent_id, name, slug, description, featured_image,
			lft, rgt, depth, position, is_active, is_featured,
			meta_title, meta_description, created_at, updated_at, created_by, updated_by
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := tx.ExecContext(ctx, query,
		category.TenantID, category.ParentID, category.Name, category.Slug,
		category.Description, category.FeaturedImage, left, right, depth,
		category.Position, category.IsActive, category.IsFeatured,
		category.MetaTitle, category.MetaDescription, time.Now(), time.Now(),
		category.CreatedBy, category.UpdatedBy)

	if err != nil {
		return fmt.Errorf("failed to insert category: %w", err)
	}

	// Get the inserted ID
	categoryID, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get inserted id: %w", err)
	}

	category.CategoryID = uint(categoryID)
	category.Left = int(left)
	category.Right = int(right)
	category.Depth = int(depth)

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// GetByID retrieves a category by its ID
func (r *CategoryRepository) GetByID(ctx context.Context, tenantID uint, categoryID uint) (*models.Category, error) {
	var category models.Category

	query := `
		SELECT * FROM blog_categories
		WHERE tenant_id = ? AND category_id = ?
	`

	err := r.db.GetContext(ctx, &category, query, tenantID, categoryID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	return &category, nil
}

// GetBySlug retrieves a category by its slug
func (r *CategoryRepository) GetBySlug(ctx context.Context, tenantID uint, slug string) (*models.Category, error) {
	var category models.Category

	query := `
		SELECT * FROM blog_categories
		WHERE tenant_id = ? AND slug = ?
	`

	err := r.db.GetContext(ctx, &category, query, tenantID, slug)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get category by slug: %w", err)
	}

	return &category, nil
}

// Update updates an existing category
func (r *CategoryRepository) Update(ctx context.Context, category *models.Category) error {
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Check if the category exists
	var existingCategory models.Category
	err = tx.GetContext(ctx, &existingCategory,
		"SELECT * FROM blog_categories WHERE tenant_id = ? AND category_id = ?",
		category.TenantID, category.CategoryID)

	if err != nil {
		if err == sql.ErrNoRows {
			return ErrNotFound
		}
		return fmt.Errorf("failed to check if category exists: %w", err)
	}

	// Check if slug changed and is unique
	if category.Slug != existingCategory.Slug {
		var count uint
		err = tx.GetContext(ctx, &count,
			"SELECT COUNT(*) FROM blog_categories WHERE tenant_id = ? AND slug = ? AND category_id != ?",
			category.TenantID, category.Slug, category.CategoryID)

		if err != nil {
			return fmt.Errorf("failed to check slug uniqueness: %w", err)
		}

		if count > 0 {
			return ErrConflict
		}
	}

	// Update category fields
	query := `
		UPDATE blog_categories SET
			name = ?,
			slug = ?,
			description = ?,
			featured_image = ?,
			is_active = ?,
			is_featured = ?,
			meta_title = ?,
			meta_description = ?,
			updated_by = ?,
			updated_at = ?
		WHERE tenant_id = ? AND category_id = ?
	`

	_, err = tx.ExecContext(ctx, query,
		category.Name,
		category.Slug,
		category.Description,
		category.FeaturedImage,
		category.IsActive,
		category.IsFeatured,
		category.MetaTitle,
		category.MetaDescription,
		category.UpdatedBy,
		time.Now(),
		category.TenantID,
		category.CategoryID,
	)

	if err != nil {
		return fmt.Errorf("failed to update category: %w", err)
	}

	// If parent ID changed, we would update the tree structure here
	// But for simplicity, we'll just update the parent_id field
	if (category.ParentID == nil && existingCategory.ParentID != nil) ||
		(category.ParentID != nil && existingCategory.ParentID == nil) ||
		(category.ParentID != nil && existingCategory.ParentID != nil && *category.ParentID != *existingCategory.ParentID) {

		_, err = tx.ExecContext(ctx,
			"UPDATE blog_categories SET parent_id = ? WHERE tenant_id = ? AND category_id = ?",
			category.ParentID, category.TenantID, category.CategoryID)

		if err != nil {
			return fmt.Errorf("failed to update parent id: %w", err)
		}
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// Delete removes a category and optionally its children
func (r *CategoryRepository) Delete(ctx context.Context, tenantID uint, categoryID uint) error {
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Check if the category exists and get its left and right values
	var category models.Category
	err = tx.GetContext(ctx, &category,
		"SELECT * FROM blog_categories WHERE tenant_id = ? AND category_id = ?",
		tenantID, categoryID)

	if err != nil {
		if err == sql.ErrNoRows {
			return ErrNotFound
		}
		return fmt.Errorf("failed to check if category exists: %w", err)
	}

	// Delete the category and all its descendants
	_, err = tx.ExecContext(ctx,
		"DELETE FROM blog_categories WHERE tenant_id = ? AND lft BETWEEN ? AND ?",
		tenantID, category.Left, category.Right)

	if err != nil {
		return fmt.Errorf("failed to delete categories: %w", err)
	}

	// Update left and right values for remaining nodes
	width := category.Right - category.Left + 1

	_, err = tx.ExecContext(ctx,
		"UPDATE blog_categories SET lft = lft - ? WHERE tenant_id = ? AND lft > ?",
		width, tenantID, category.Right)

	if err != nil {
		return fmt.Errorf("failed to update left values: %w", err)
	}

	_, err = tx.ExecContext(ctx,
		"UPDATE blog_categories SET rgt = rgt - ? WHERE tenant_id = ? AND rgt > ?",
		width, tenantID, category.Right)

	if err != nil {
		return fmt.Errorf("failed to update right values: %w", err)
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// GetTree retrieves the entire category tree for a tenant
func (r *CategoryRepository) GetTree(ctx context.Context, tenantID uint) ([]*models.Category, error) {
	query := `
		SELECT * FROM blog_categories
		WHERE tenant_id = ?
		ORDER BY lft
	`

	var categories []*models.Category
	err := r.db.SelectContext(ctx, &categories, query, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get category tree: %w", err)
	}

	// Xây dựng lại cấu trúc cây với node con được sắp xếp theo position
	tree := buildCategoryTree(categories)

	return tree, nil
}

// buildCategoryTree xây dựng cấu trúc cây từ danh sách phẳng các categories
// và sắp xếp các node con theo position
func buildCategoryTree(flatCategories []*models.Category) []*models.Category {
	if len(flatCategories) == 0 {
		return []*models.Category{}
	}

	// Tạo map để lưu trữ tham chiếu đến các đối tượng category theo ID
	categoryMap := make(map[uint]*models.Category)
	for _, category := range flatCategories {
		categoryMap[category.CategoryID] = category
		category.Children = []*models.Category{} // Khởi tạo slice Children rỗng
	}

	// Danh sách các node gốc
	var rootCategories []*models.Category

	// Xây dựng cấu trúc cây
	for _, category := range flatCategories {
		if category.ParentID == nil || *category.ParentID == 0 {
			// Đây là node gốc
			rootCategories = append(rootCategories, category)
		} else {
			// Đây là node con, thêm vào danh sách Children của node cha
			parentID := *category.ParentID
			if parent, exists := categoryMap[parentID]; exists {
				parent.Children = append(parent.Children, category)
			} else {
				// Nếu không tìm thấy node cha, coi như node gốc
				rootCategories = append(rootCategories, category)
			}
		}
	}

	// Sắp xếp các node con theo position
	sortCategoriesByPosition(rootCategories)
	for _, category := range flatCategories {
		sortCategoriesByPosition(category.Children)
	}

	return rootCategories
}

// sortCategoriesByPosition sắp xếp danh sách các category theo position tăng dần
func sortCategoriesByPosition(categories []*models.Category) {
	sort.Slice(categories, func(i, j int) bool {
		// Sắp xếp theo position tăng dần
		if categories[i].Position != categories[j].Position {
			return categories[i].Position < categories[j].Position
		}
		// Nếu position bằng nhau, sắp xếp theo ID để đảm bảo tính ổn định
		return categories[i].CategoryID < categories[j].CategoryID
	})
}

// GetSubtree retrieves a category and all its descendants
func (r *CategoryRepository) GetSubtree(ctx context.Context, tenantID uint, categoryID uint) ([]*models.Category, error) {
	// First get the category to find its left and right values
	var category models.Category
	err := r.db.GetContext(ctx, &category,
		"SELECT * FROM blog_categories WHERE tenant_id = ? AND category_id = ?",
		tenantID, categoryID)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	// Then get all categories between those values
	query := `
		SELECT * FROM blog_categories
		WHERE tenant_id = ? AND lft >= ? AND rgt <= ?
		ORDER BY lft
	`

	var categories []*models.Category
	err = r.db.SelectContext(ctx, &categories, query, tenantID, category.Left, category.Right)
	if err != nil {
		return nil, fmt.Errorf("failed to get category subtree: %w", err)
	}

	return categories, nil
}

// GetAncestors retrieves all ancestors of a category
func (r *CategoryRepository) GetAncestors(ctx context.Context, tenantID uint, categoryID uint) ([]*models.Category, error) {
	// First get the category to find its left and right values
	var category models.Category
	err := r.db.GetContext(ctx, &category,
		"SELECT * FROM blog_categories WHERE tenant_id = ? AND category_id = ?",
		tenantID, categoryID)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	// Then get all ancestors
	query := `
		SELECT * FROM blog_categories
		WHERE tenant_id = ? AND lft < ? AND rgt > ?
		ORDER BY lft
	`

	var categories []*models.Category
	err = r.db.SelectContext(ctx, &categories, query, tenantID, category.Left, category.Right)
	if err != nil {
		return nil, fmt.Errorf("failed to get category ancestors: %w", err)
	}

	return categories, nil
}

// GetChildren retrieves direct children of a category
func (r *CategoryRepository) GetChildren(ctx context.Context, tenantID uint, categoryID uint) ([]*models.Category, error) {
	query := `
		SELECT * FROM blog_categories
		WHERE tenant_id = ? AND parent_id = ?
		ORDER BY position
	`

	var categories []*models.Category
	err := r.db.SelectContext(ctx, &categories, query, tenantID, categoryID)
	if err != nil {
		return nil, fmt.Errorf("failed to get category children: %w", err)
	}

	return categories, nil
}

// MoveNode moves a category relative to another category
func (r *CategoryRepository) MoveNode(ctx context.Context, tenantID uint, categoryID uint, targetID uint, position string) error {
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("không thể bắt đầu giao dịch: %w", err)
	}

	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Không thể di chuyển node đến chính nó
	if categoryID == targetID {
		return fmt.Errorf("không thể di chuyển category đến chính nó")
	}

	// Kiểm tra xem category nguồn tồn tại không
	var sourceNode models.Category
	err = tx.GetContext(ctx, &sourceNode,
		"SELECT * FROM blog_categories WHERE tenant_id = ? AND category_id = ?",
		tenantID, categoryID)
	if err != nil {
		if err == sql.ErrNoRows {
			return ErrNotFound
		}
		return fmt.Errorf("không tìm thấy category nguồn: %w", err)
	}

	// Kiểm tra xem category đích tồn tại không
	var targetNode models.Category
	err = tx.GetContext(ctx, &targetNode,
		"SELECT * FROM blog_categories WHERE tenant_id = ? AND category_id = ?",
		tenantID, targetID)
	if err != nil {
		if err == sql.ErrNoRows {
			return ErrNotFound
		}
		return fmt.Errorf("không tìm thấy category đích: %w", err)
	}

	// Không thể di chuyển node đến một trong các node con của nó
	if targetNode.Left > sourceNode.Left && targetNode.Right < sourceNode.Right {
		return fmt.Errorf("không thể di chuyển category đến một trong các category con của nó")
	}

	// Tính toán kích thước của cây con nguồn
	nodeSize := sourceNode.Right - sourceNode.Left + 1

	// Lưu trữ thông tin nút nguồn
	oldRight := sourceNode.Right
	// Các biến dưới đây được bỏ do không sử dụng
	// oldLeft := sourceNode.Left
	// oldParentID := sourceNode.ParentID

	// Tạm thởi gỡ bỏ nút nguồn và cây con của nó khỏi cây
	_, err = tx.ExecContext(ctx, `
		UPDATE blog_categories
		SET lft = lft * -1, rgt = rgt * -1
		WHERE tenant_id = ? AND lft >= ? AND rgt <= ?`,
		tenantID, sourceNode.Left, sourceNode.Right)
	if err != nil {
		return fmt.Errorf("không thể gỡ bỏ nút nguồn: %w", err)
	}

	// Cập nhật lại các giá trị left và right cho các nút còn lại sau khi gỡ bỏ
	_, err = tx.ExecContext(ctx, `
		UPDATE blog_categories
		SET lft = CASE
				WHEN lft > ? THEN lft - ?
				ELSE lft
			END,
			rgt = CASE
				WHEN rgt > ? THEN rgt - ?
				ELSE rgt
			END
		WHERE tenant_id = ? AND lft > 0`,
		oldRight, nodeSize, oldRight, nodeSize, tenantID)
	if err != nil {
		return fmt.Errorf("không thể cập nhật giá trị sau khi gỡ bỏ: %w", err)
	}

	// Xác định vị trí mới dựa trên position
	var newLeft, newDepth uint
	var newParentID *uint

	switch position {
	case "child":
		// Di chuyển thành con của nút đích
		newLeft = uint(targetNode.Right)
		newDepth = uint(targetNode.Depth + 1)
		newParentID = &targetID

	case "before":
		// Di chuyển trước nút đích
		newLeft = uint(targetNode.Left)
		newDepth = uint(targetNode.Depth)
		newParentID = targetNode.ParentID

	case "after":
		// Di chuyển sau nút đích
		newLeft = uint(targetNode.Right + 1)
		newDepth = uint(targetNode.Depth)
		newParentID = targetNode.ParentID

	default:
		return fmt.Errorf("vị trí không hợp lệ: %s", position)
	}

	// Cập nhật lại các giá trị left và right cho các nút để tạo khoảng trống cho nút mới
	_, err = tx.ExecContext(ctx, `
		UPDATE blog_categories
		SET lft = CASE
				WHEN lft >= ? THEN lft + ?
				ELSE lft
			END,
			rgt = CASE
				WHEN rgt >= ? THEN rgt + ?
				ELSE rgt
			END
		WHERE tenant_id = ? AND lft > 0`,
		newLeft, nodeSize, newLeft, nodeSize, tenantID)
	if err != nil {
		return fmt.Errorf("không thể tạo khoảng trống cho nút mới: %w", err)
	}

	// Tính toán chênh lệch để điều chỉnh các giá trị của cây con nguồn
	leftOffset := int(newLeft) - sourceNode.Left
	depthOffset := int(newDepth) - sourceNode.Depth

	// Đặt lại cây con nguồn vào vị trí mới
	_, err = tx.ExecContext(ctx, `
		UPDATE blog_categories
		SET lft = (lft * -1) + ?,
			rgt = (rgt * -1) + ?,
			depth = depth + ?,
			parent_id = CASE
				WHEN category_id = ? THEN ?
				ELSE parent_id
			END
		WHERE tenant_id = ? AND lft < 0`,
		leftOffset, leftOffset, depthOffset, categoryID, newParentID, tenantID)
	if err != nil {
		return fmt.Errorf("không thể đặt lại nút nguồn: %w", err)
	}

	// Commit giao dịch
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("không thể commit giao dịch: %w", err)
	}

	return nil
}

// ChangeParent changes a category's parent
func (r *CategoryRepository) ChangeParent(ctx context.Context, tenantID uint, categoryID uint, newParentID uint) error {
	var category models.Category
	category.CategoryID = categoryID
	category.TenantID = tenantID
	category.ParentID = &newParentID

	return r.Update(ctx, &category)
}

// List retrieves a paginated list of categories using cursor pagination
func (r *CategoryRepository) List(ctx context.Context, tenantID uint, req request.ListCategoryRequest) ([]*models.Category, string, bool, error) {
	// Base query
	baseQuery := "FROM blog_categories WHERE tenant_id = ?"
	args := []interface{}{tenantID}

	// Add filters
	if req.ParentID != nil {
		baseQuery += " AND parent_id = ?"
		args = append(args, *req.ParentID)
	}

	if req.OnlyActive {
		baseQuery += " AND is_active = 1"
	}

	// Cursor-based pagination
	limitQuery := ""
	if req.Limit <= 0 {
		req.Limit = 50 // Default limit
	}

	sortOrder := "ASC"
	if req.Cursor != "" {
		// For simplicity, we'll just use the category_id for cursor pagination
		categoryID, err := strconv.ParseInt(req.Cursor, 10, 64)
		if err != nil {
			return nil, "", false, fmt.Errorf("invalid cursor: %w", err)
		}

		if categoryID > 0 {
			baseQuery += " AND category_id > ?"
			args = append(args, categoryID)
		}
	}

	// Add ordering and limit
	orderBy := fmt.Sprintf(" ORDER BY category_id %s", sortOrder)
	limitQuery = fmt.Sprintf(" LIMIT %d", req.Limit+1) // Get one extra to check if there are more

	// Create final query
	dataQuery := fmt.Sprintf("SELECT * %s%s%s", baseQuery, orderBy, limitQuery)

	// Get the data
	var categories []*models.Category
	err := r.db.SelectContext(ctx, &categories, dataQuery, args...)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to list categories: %w", err)
	}

	// Check if there are more results
	hasMore := false
	if len(categories) > req.Limit {
		hasMore = true
		categories = categories[:req.Limit]
	}

	// Generate the next cursor
	var nextCursor string
	if hasMore && len(categories) > 0 {
		lastCategory := categories[len(categories)-1]
		nextCursor = fmt.Sprintf("%d", lastCategory.CategoryID)
	}

	return categories, nextCursor, hasMore, nil
}

// GetCategoriesWithPostCount retrieves categories with post count
func (r *CategoryRepository) GetCategoriesWithPostCount(ctx context.Context, tenantID uint) ([]*models.Category, error) {
	query := `
		SELECT c.*, COUNT(pc.post_id) as post_count
		FROM blog_categories c
		LEFT JOIN blog_post_categories pc ON c.category_id = pc.category_id
		WHERE c.tenant_id = ?
		GROUP BY c.category_id
		ORDER BY c.lft
	`

	var categories []*models.Category
	err := r.db.SelectContext(ctx, &categories, query, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get categories with post count: %w", err)
	}

	return categories, nil
}

// RebuildTree tính toán lại lft, rgt, depth và position cho tất cả các category của một tenant.
// Nó đọc toàn bộ cây vào bộ nhớ, xây dựng lại quan hệ cha-con,
// rồi duyệt cây để gán lại các giá trị nested set và position.
func (r *CategoryRepository) RebuildTree(ctx context.Context, tenantID uint) (err error) { // Sử dụng named return để defer dễ xử lý lỗi
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	// Đảm bảo rollback nếu có lỗi xảy ra trong quá trình thực thi
	defer func() {
		if p := recover(); p != nil { // Bắt panic nếu có
			_ = tx.Rollback()
			panic(p) // Re-panic sau khi rollback
		} else if err != nil { // Rollback nếu có lỗi được return
			_ = tx.Rollback()
		} else { // Commit nếu không có lỗi
			err = tx.Commit()
			if err != nil {
				err = fmt.Errorf("failed to commit transaction: %w", err)
			}
		}
	}()

	// 1. Lấy tất cả categories cho tenant.
	// QUAN TRỌNG: Thứ tự sắp xếp ở đây ảnh hưởng đến thứ tự Children ban đầu.
	// Sắp xếp theo parent_id, sau đó theo position hiện có (để cố gắng giữ thứ tự cũ nếu hợp lệ),
	// cuối cùng theo ID để đảm bảo tính nhất quán nếu position trùng lặp.
	// Nếu muốn reset position hoàn toàn theo tên chẳng hạn, đổi thành: ORDER BY COALESCE(parent_id, 0), name
	query := `
		SELECT * FROM blog_categories
		WHERE tenant_id = ?
		ORDER BY COALESCE(parent_id, 0), position, category_id
	`
	var categories []*models.Category
	if err = tx.SelectContext(ctx, &categories, query, tenantID); err != nil {
		return fmt.Errorf("failed to get categories: %w", err)
	}

	if len(categories) == 0 {
		// Không có gì để rebuild nếu không có category nào
		return nil
	}

	// 2. Xây dựng map và liên kết cha-con trong bộ nhớ
	categoryMap := make(map[uint]*models.Category, len(categories))
	var rootCategories []*models.Category // Slice chứa các category gốc (parent_id IS NULL)

	for i := range categories {
		cat := categories[i] // Lấy con trỏ đến category trong slice gốc
		categoryMap[cat.CategoryID] = cat
		cat.Children = []*models.Category{} // Khởi tạo slice Children rỗng
	}

	// Liên kết con với cha và xác định gốc
	for _, cat := range categories {
		if cat.ParentID != nil && *cat.ParentID != 0 { // Kiểm tra ParentID hợp lệ
			if parent, exists := categoryMap[*cat.ParentID]; exists {
				parent.Children = append(parent.Children, cat)
			} else {
				// Xử lý trường hợp Orphan (cha không tồn tại trong danh sách):
				// Coi nó như một node gốc. Dữ liệu này có thể cần được làm sạch sau.
				fmt.Printf("Warning: Category %d (Name: %s) has non-existent parent %d. Treating as root.\n",
					cat.CategoryID, cat.Name, *cat.ParentID)
				cat.ParentID = nil // Đặt lại ParentID thành nil
				rootCategories = append(rootCategories, cat)
			}
		} else {
			// Là category gốc
			rootCategories = append(rootCategories, cat)
		}
	}

	// 3. Sắp xếp các node gốc theo position cũ (hoặc tiêu chí khác nếu cần)
	// Điều này quan trọng nếu bạn muốn các cây gốc có thứ tự nhất quán.
	sort.SliceStable(rootCategories, func(i, j int) bool {
		if rootCategories[i].Position != rootCategories[j].Position {
			return rootCategories[i].Position < rootCategories[j].Position
		}
		return rootCategories[i].CategoryID < rootCategories[j].CategoryID // Fallback để ổn định
	})

	// 4. Rebuild cây (tính lft, rgt, depth, position) bắt đầu từ các gốc
	counter := uint(1) // Bộ đếm cho lft/rgt
	for i, root := range rootCategories {
		root.Position = i // Gán lại position cho các node gốc (0, 1, 2...)
		counter = r.rebuildNodeRecursive(root, 0, counter)
	}

	// 5. Cập nhật tất cả categories trong database với giá trị mới
	// Sử dụng một câu lệnh UPDATE duy nhất trong vòng lặp không phải là tối ưu nhất
	// với số lượng lớn, nhưng đơn giản để minh họa.
	// Cân nhắc dùng bulk update hoặc prepared statement nếu hiệu năng là vấn đề.
	updateQuery := `
		UPDATE blog_categories
		SET lft = ?, rgt = ?, depth = ?, position = ?
		WHERE tenant_id = ? AND category_id = ?
	`
	for _, category := range categories {
		// Đảm bảo rằng các giá trị lft, rgt được tính toán hợp lệ
		// (Ví dụ: không có node nào có lft=0 hoặc rgt=0 trừ khi cây rỗng)
		if category.Left == 0 || category.Right == 0 {
			return fmt.Errorf("internal error: category %d (Name: %s) has invalid LFT/RGT value after rebuild (%d, %d)",
				category.CategoryID, category.Name, category.Left, category.Right)
		}

		_, execErr := tx.ExecContext(ctx, updateQuery,
			category.Left,
			category.Right,
			category.Depth,
			category.Position, // Thêm position vào update
			tenantID,
			category.CategoryID,
		)
		if execErr != nil {
			// Lỗi được defer func xử lý rollback
			return fmt.Errorf("failed to update category %d (Name: %s): %w", category.CategoryID, category.Name, execErr)
		}
	}

	// Transaction sẽ được commit hoặc rollback bởi defer func
	return nil
}

// rebuildNodeRecursive duyệt cây con theo chiều sâu (depth-first),
// tính toán và gán giá trị lft, rgt, depth, và position cho các node con.
func (r *CategoryRepository) rebuildNodeRecursive(node *models.Category, depth uint, counter uint) uint {
	node.Depth = int(depth)
	node.Left = int(counter)
	counter++ // Tăng bộ đếm cho lft của node hiện tại

	// Sắp xếp các con trước khi duyệt đệ quy để đảm bảo thứ tự position đúng
	// Slice node.Children đã được sắp xếp theo ORDER BY của query ban đầu.

	// Duyệt qua các con và gán position, sau đó gọi đệ quy
	for i, child := range node.Children {
		child.Position = i                                        // Use i directly as int
		counter = r.rebuildNodeRecursive(child, depth+1, counter) // Gọi đệ quy và cập nhật bộ đếm
	}

	// Sau khi duyệt xong tất cả con cháu, gán rgt cho node hiện tại
	node.Right = int(counter)
	counter++ // Tăng bộ đếm cho rgt của node hiện tại

	return counter // Trả về bộ đếm đã cập nhật
}

// MoveNodeProcedure calls the blog_categories_move_node stored procedure
func (r *CategoryRepository) MoveNodeProcedure(ctx context.Context, tenantID uint, categoryID uint, newParentID uint, position int) error {
	// Bắt đầu transaction
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Gọi stored procedure
	var execErr error
	if newParentID == 0 {
		// Truyền NULL thay vì 0 cho p_new_parent_id khi muốn chuyển node thành root
		_, execErr = tx.ExecContext(ctx,
			"CALL blog_categories_move_node(?, ?, NULL, ?)",
			tenantID, categoryID, position)
	} else {
		_, execErr = tx.ExecContext(ctx,
			"CALL blog_categories_move_node(?, ?, ?, ?)",
			tenantID, categoryID, newParentID, position)
	}

	if execErr != nil {
		return fmt.Errorf("failed to call move_node procedure: %w", execErr)
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// UpdatePositionProcedure calls the blog_categories_update_position stored procedure
func (r *CategoryRepository) UpdatePositionProcedure(ctx context.Context, tenantID uint, categoryID uint, position int) error {
	// Bắt đầu transaction
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Gọi stored procedure
	_, err = tx.ExecContext(ctx,
		"CALL blog_categories_update_position(?, ?, ?)",
		tenantID, categoryID, position)

	if err != nil {
		return fmt.Errorf("failed to call update_position procedure: %w", err)
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// MoveNodeSiblingProcedure calls the blog_categories_move_node_sibling stored procedure
func (r *CategoryRepository) MoveNodeSiblingProcedure(ctx context.Context, tenantID uint, categoryID uint, targetID uint) error {
	// Bắt đầu transaction
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Gọi stored procedure
	_, err = tx.ExecContext(ctx,
		"CALL blog_categories_move_node_sibling(?, ?, ?)",
		tenantID, categoryID, targetID)

	if err != nil {
		return fmt.Errorf("failed to call move_node_sibling procedure: %w", err)
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// MoveNodeRelativeProcedure calls the blog_categories_move_node_relative stored procedure
func (r *CategoryRepository) MoveNodeRelativeProcedure(ctx context.Context, tenantID uint, categoryID uint, targetID uint, mode string) error {
	// Bắt đầu transaction
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Kiểm tra targetID, nếu = 0 thì truyền NULL vào procedure
	var execErr error
	if targetID == 0 {
		_, execErr = tx.ExecContext(ctx,
			"CALL blog_categories_move_node_relative(?, ?, NULL, ?)",
			tenantID, categoryID, mode)
	} else {
		_, execErr = tx.ExecContext(ctx,
			"CALL blog_categories_move_node_relative(?, ?, ?, ?)",
			tenantID, categoryID, targetID, mode)
	}

	if execErr != nil {
		return fmt.Errorf("failed to call move_node_relative procedure: %w", execErr)
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// generateSlug creates a URL-friendly slug from a string
func generateSlug(name string) string {
	// Convert to lowercase
	slug := strings.ToLower(name)

	// Replace spaces with hyphens
	slug = strings.ReplaceAll(slug, " ", "-")

	// Remove special characters
	slug = strings.Map(func(r rune) rune {
		if (r >= 'a' && r <= 'z') || (r >= '0' && r <= '9') || r == '-' {
			return r
		}
		return -1
	}, slug)

	// Remove consecutive hyphens
	for strings.Contains(slug, "--") {
		slug = strings.ReplaceAll(slug, "--", "-")
	}

	// Trim hyphens from start and end
	slug = strings.Trim(slug, "-")

	return slug
}
