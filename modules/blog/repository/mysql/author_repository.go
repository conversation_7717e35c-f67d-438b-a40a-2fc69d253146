package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/jmoiron/sqlx"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/models"
	"wnapi/modules/blog/repository"
)

// AuthorRepository implements the repository.AuthorRepository interface
type AuthorRepository struct {
	db *sqlx.DB
}

// NewAuthorRepository creates a new instance of AuthorRepository
func NewAuthorRepository(db *sqlx.DB) repository.AuthorRepository {
	return &AuthorRepository{db: db}
}

// Create inserts a new author into the database
func (r *AuthorRepository) Create(ctx context.Context, author *models.BlogAuthor) error {
	// Set default values
	if !author.IsActive {
		author.IsActive = true
	}

	now := time.Now()
	author.CreatedAt = now
	author.UpdatedAt = now

	// Insert author
	query := `
		INSERT INTO blog_authors (tenant_id, user_id, display_name, bio, avatar_url, email, is_active, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := r.db.ExecContext(
		ctx,
		query,
		author.TenantID,
		author.UserID,
		author.DisplayName,
		author.Bio,
		author.AvatarURL,
		author.Email,
		author.IsActive,
		author.CreatedAt,
		author.UpdatedAt,
	)
	if err != nil {
		return fmt.Errorf("failed to create author: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get last insert ID: %w", err)
	}

	author.ID = uint(id)
	return nil
}

// GetByID fetches an author by ID and tenant
func (r *AuthorRepository) GetByID(ctx context.Context, tenantID, authorID uint) (*models.BlogAuthor, error) {
	var author models.BlogAuthor

	query := `
		SELECT id, tenant_id, user_id, display_name, bio, avatar_url, email, is_active, created_at, updated_at
		FROM blog_authors
		WHERE id = ? AND tenant_id = ?
	`

	err := r.db.GetContext(ctx, &author, query, authorID, tenantID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("author not found")
		}
		return nil, fmt.Errorf("failed to get author: %w", err)
	}

	return &author, nil
}

// GetByUserID fetches an author by user ID and tenant
func (r *AuthorRepository) GetByUserID(ctx context.Context, tenantID, userID uint) (*models.BlogAuthor, error) {
	var author models.BlogAuthor

	query := `
		SELECT id, tenant_id, user_id, display_name, bio, avatar_url, email, is_active, created_at, updated_at
		FROM blog_authors
		WHERE user_id = ? AND tenant_id = ?
	`

	err := r.db.GetContext(ctx, &author, query, userID, tenantID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("author not found")
		}
		return nil, fmt.Errorf("failed to get author: %w", err)
	}

	return &author, nil
}

// Update updates an existing author
func (r *AuthorRepository) Update(ctx context.Context, author *models.BlogAuthor) error {
	author.UpdatedAt = time.Now()

	query := `
		UPDATE blog_authors
		SET display_name = ?, bio = ?, avatar_url = ?, email = ?, is_active = ?, updated_at = ?
		WHERE id = ? AND tenant_id = ?
	`

	result, err := r.db.ExecContext(
		ctx,
		query,
		author.DisplayName,
		author.Bio,
		author.AvatarURL,
		author.Email,
		author.IsActive,
		author.UpdatedAt,
		author.ID,
		author.TenantID,
	)
	if err != nil {
		return fmt.Errorf("failed to update author: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("author not found")
	}

	return nil
}

// Delete deletes an author
func (r *AuthorRepository) Delete(ctx context.Context, tenantID, authorID uint) error {
	query := `DELETE FROM blog_authors WHERE id = ? AND tenant_id = ?`

	result, err := r.db.ExecContext(ctx, query, authorID, tenantID)
	if err != nil {
		return fmt.Errorf("failed to delete author: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("author not found")
	}

	return nil
}

// List lists authors with pagination
func (r *AuthorRepository) List(ctx context.Context, tenantID uint, req request.ListAuthorRequest) ([]*models.BlogAuthor, string, bool, error) {
	var (
		authors     []*models.BlogAuthor
		whereClause string
		args        []interface{}
		query       string
		limit       = 20
	)

	// Add tenant ID to where clause
	whereClause = "WHERE tenant_id = ?"
	args = append(args, tenantID)

	// Add cursor-based pagination
	if req.Cursor != "" {
		cursorID, err := strconv.ParseUint(req.Cursor, 10, 64)
		if err != nil {
			return nil, "", false, fmt.Errorf("invalid cursor format: %w", err)
		}
		whereClause += " AND id > ?"
		args = append(args, cursorID)
	}

	// Add search by display name if provided
	if req.Search != "" {
		whereClause += " AND display_name LIKE ?"
		args = append(args, "%"+req.Search+"%")
	}

	// Add filter by active status if provided
	if req.IsActive != nil {
		whereClause += " AND is_active = ?"
		args = append(args, *req.IsActive)
	}

	// Set limit if provided
	if req.Limit > 0 {
		limit = req.Limit
	}

	// Build query
	query = fmt.Sprintf(`
		SELECT id, tenant_id, user_id, display_name, bio, avatar_url, email, is_active, created_at, updated_at
		FROM blog_authors
		%s
		ORDER BY id
		LIMIT %d
	`, whereClause, limit+1) // +1 to check if there are more results

	// Execute query
	err := r.db.SelectContext(ctx, &authors, query, args...)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to list authors: %w", err)
	}

	// Check if there are more results
	hasMore := false
	nextCursor := ""
	if len(authors) > limit {
		hasMore = true
		authors = authors[:limit]
		nextCursor = strconv.FormatUint(uint64(authors[len(authors)-1].ID), 10)
	}

	return authors, nextCursor, hasMore, nil
}
