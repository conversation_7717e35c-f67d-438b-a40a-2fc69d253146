package service

import (
	"context"
	"fmt"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/dto/response"
	"wnapi/modules/blog/models"
	"wnapi/modules/blog/repository"
)

// PostService defines the interface for post business logic
type PostService interface {
	CreatePost(ctx context.Context, tenantID uint, req request.CreatePostRequest) (*response.PostResponse, error)
	GetPost(ctx context.Context, tenantID uint, postID uint) (*response.PostResponse, error)
	GetPostBySlug(ctx context.Context, tenantID uint, slug string) (*response.PostResponse, error)
	UpdatePost(ctx context.Context, tenantID uint, postID uint, req request.UpdatePostRequest) (*response.PostResponse, error)
	DeletePost(ctx context.Context, tenantID uint, postID uint) error
	ListPosts(ctx context.Context, tenantID uint, req request.ListPostRequest) (*response.PostListResponse, error)
}

// postService implements the PostService interface
type postService struct {
	postRepo     repository.PostRepository
	categoryRepo repository.CategoryRepository
	tagRepo      repository.TagRepository
}

// NewPostService creates a new PostService instance
func NewPostService(postRepo repository.PostRepository, categoryRepo repository.CategoryRepository, tagRepo repository.TagRepository) PostService {
	return &postService{
		postRepo:     postRepo,
		categoryRepo: categoryRepo,
		tagRepo:      tagRepo,
	}
}

// CreatePost creates a new post
func (s *postService) CreatePost(ctx context.Context, tenantID uint, req request.CreatePostRequest) (*response.PostResponse, error) {
	// Create model from request
	userID := getUserIDFromContext(ctx)

	// Convert []int64 to []uint for CategoryIDs
	categoryIDs := make([]uint, len(req.CategoryIDs))
	for i, id := range req.CategoryIDs {
		categoryIDs[i] = uint(id)
	}

	// Convert []int64 to []uint for TagIDs
	tagIDs := make([]uint, len(req.TagIDs))
	for i, id := range req.TagIDs {
		tagIDs[i] = uint(id)
	}

	post := &models.Post{
		TenantID:      tenantID,
		Title:         req.Title,
		Slug:          req.Slug,
		Description:   req.Description,
		Content:       req.Content,
		FeaturedImage: req.FeaturedImage,
		Status:        req.Status,
		Visibility:    req.Visibility,
		Password:      req.Password,
		CommentStatus: req.CommentStatus,
		PublishDate:   req.PublishDate,
		CategoryIDs:   categoryIDs, // Using converted slice
		TagIDs:        tagIDs,      // Using converted slice
		AuthorID:      userID,
		CreatedBy:     &userID,
	}

	// Save to repository
	if err := s.postRepo.Create(ctx, post); err != nil {
		return nil, err
	}

	// Get the saved post to return with all fields populated
	return s.getPostWithRelations(ctx, tenantID, post.PostID, true, true, true)
}

// GetPost retrieves a post by ID
func (s *postService) GetPost(ctx context.Context, tenantID uint, postID uint) (*response.PostResponse, error) {
	return s.getPostWithRelations(ctx, tenantID, postID, true, true, true)
}

// GetPostBySlug retrieves a post by slug
func (s *postService) GetPostBySlug(ctx context.Context, tenantID uint, slug string) (*response.PostResponse, error) {
	// Get from repository
	post, err := s.postRepo.GetBySlug(ctx, tenantID, slug)
	if err != nil {
		return nil, err
	}

	return s.getPostWithRelations(ctx, tenantID, post.PostID, true, true, true)
}

// UpdatePost updates an existing post
func (s *postService) UpdatePost(ctx context.Context, tenantID uint, postID uint, req request.UpdatePostRequest) (*response.PostResponse, error) {
	// Get existing post
	existingPost, err := s.postRepo.GetByID(ctx, tenantID, postID)
	if err != nil {
		return nil, err
	}

	// Update fields if provided in request
	if req.Title != "" {
		existingPost.Title = req.Title
	}

	if req.Slug != "" {
		existingPost.Slug = req.Slug
	}

	if req.Description != "" {
		existingPost.Description = req.Description
	}

	if req.Content != "" {
		existingPost.Content = req.Content
	}

	if req.FeaturedImage != "" {
		existingPost.FeaturedImage = req.FeaturedImage
	}

	if req.Status != "" {
		existingPost.Status = req.Status
	}

	if req.Visibility != "" {
		existingPost.Visibility = req.Visibility
	}

	if req.Password != nil {
		existingPost.Password = req.Password
	}

	if req.CommentStatus != "" {
		existingPost.CommentStatus = req.CommentStatus
	}

	if req.PublishDate != nil {
		existingPost.PublishDate = req.PublishDate
	}

	// Update categories and tags if provided
	if req.CategoryIDs != nil {
		// Convert []int64 to []uint for CategoryIDs
		categoryIDs := make([]uint, len(req.CategoryIDs))
		for i, id := range req.CategoryIDs {
			categoryIDs[i] = uint(id)
		}
		existingPost.CategoryIDs = categoryIDs
	}

	if req.TagIDs != nil {
		// Convert []int64 to []uint for TagIDs
		tagIDs := make([]uint, len(req.TagIDs))
		for i, id := range req.TagIDs {
			tagIDs[i] = uint(id)
		}
		existingPost.TagIDs = tagIDs
	}

	// Set editor ID
	existingPost.CreatedBy = new(uint)
	*existingPost.CreatedBy = getUserIDFromContext(ctx)

	// Save changes
	if err := s.postRepo.Update(ctx, existingPost); err != nil {
		return nil, err
	}

	// Get updated post with related entities
	return s.getPostWithRelations(ctx, tenantID, postID, true, true, true)
}

// DeletePost deletes a post
func (s *postService) DeletePost(ctx context.Context, tenantID uint, postID uint) error {
	return s.postRepo.Delete(ctx, tenantID, postID)
}

// ListPosts lists all posts with pagination
func (s *postService) ListPosts(ctx context.Context, tenantID uint, req request.ListPostRequest) (*response.PostListResponse, error) {
	// Get posts from repository
	posts, nextCursor, hasMore, err := s.postRepo.List(ctx, tenantID, req)
	if err != nil {
		return nil, err
	}

	// Convert to response
	postResponses := make([]response.PostResponse, 0, len(posts))

	for _, post := range posts {
		// Load relations if requested
		postResp, err := s.enrichPostWithRelations(ctx, tenantID, post, req.WithCategories, req.WithTags, req.WithAuthor)
		if err != nil {
			return nil, err
		}
		postResponses = append(postResponses, *postResp)
	}

	return &response.PostListResponse{
		Posts:      postResponses,
		NextCursor: nextCursor,
		HasMore:    hasMore,
	}, nil
}

// Helper functions

// getPostWithRelations fetches a post by ID and loads related entities
func (s *postService) getPostWithRelations(ctx context.Context, tenantID, postID uint, withCategories, withTags, withAuthor bool) (*response.PostResponse, error) {
	// Get post from repository
	post, err := s.postRepo.GetByID(ctx, tenantID, postID)
	if err != nil {
		return nil, err
	}

	return s.enrichPostWithRelations(ctx, tenantID, post, withCategories, withTags, withAuthor)
}

// enrichPostWithRelations loads related entities for a post
func (s *postService) enrichPostWithRelations(ctx context.Context, tenantID uint, post *models.Post, withCategories, withTags, withAuthor bool) (*response.PostResponse, error) {
	// Base post response
	postResp := &response.PostResponse{
		ID:            post.PostID,
		Title:         post.Title,
		Slug:          post.Slug,
		Description:   post.Description,
		Content:       post.Content,
		FeaturedImage: post.FeaturedImage,
		Status:        post.Status,
		Visibility:    post.Visibility,
		CommentStatus: post.CommentStatus,
		PublishDate:   post.PublishDate,
		CreatedAt:     post.CreatedAt,
		UpdatedAt:     post.UpdatedAt,
		Tags:          []response.PostTagResponse{}, // Khởi tạo mảng trống mặc định
	}

	// Load categories if requested
	if withCategories {
		categoryIDs, err := s.postRepo.GetPostCategories(ctx, tenantID, post.PostID)
		if err != nil {
			return nil, err
		}

		if len(categoryIDs) > 0 {
			postResp.Categories = make([]response.CategoryResponse, 0, len(categoryIDs))
			for _, catID := range categoryIDs {
				category, err := s.categoryRepo.GetByID(ctx, tenantID, catID)
				if err != nil {
					continue // Skip if category not found
				}

				postResp.Categories = append(postResp.Categories, response.CategoryResponse{
					ID:              category.CategoryID,
					ParentID:        category.ParentID,
					Name:            category.Name,
					Slug:            category.Slug,
					Description:     category.Description,
					FeaturedImage:   category.FeaturedImage,
					Depth:           category.Depth,
					Position:        category.Position,
					IsActive:        category.IsActive,
					IsFeatured:      category.IsFeatured,
					MetaTitle:       category.MetaTitle,
					MetaDescription: category.MetaDescription,
					CreatedAt:       category.CreatedAt,
					UpdatedAt:       category.UpdatedAt,
				})
			}
		}
	}

	// Load tags if requested
	if withTags {
		// DEBUG: In ra giá trị withTags và thông tin của post
		fmt.Printf("DEBUG: withTags=%v, tenantID=%d, postID=%d\n", withTags, tenantID, post.PostID)

		tagIDs, err := s.postRepo.GetPostTags(ctx, tenantID, post.PostID)
		if err != nil {
			fmt.Printf("DEBUG: Error getting post tags: %v\n", err)
			return nil, err
		}

		// DEBUG: In ra số lượng tagIDs lấy được
		fmt.Printf("DEBUG: Found %d tags for post ID %d\n", len(tagIDs), post.PostID)

		if len(tagIDs) > 0 {
			postResp.Tags = make([]response.PostTagResponse, 0, len(tagIDs))
			// Lấy thông tin chi tiết của từng tag
			for _, tagID := range tagIDs {
				fmt.Printf("DEBUG: Getting tag details for tagID=%d\n", tagID)
				tag, err := s.tagRepo.GetByID(ctx, tenantID, tagID)
				if err != nil {
					fmt.Printf("DEBUG: Error getting tag %d: %v\n", tagID, err)
					continue // Bỏ qua nếu không tìm thấy tag
				}

				postResp.Tags = append(postResp.Tags, response.PostTagResponse{
					ID:   tag.TagID,
					Name: tag.Name,
					Slug: tag.Slug,
				})
			}
			// DEBUG: In ra số lượng tags sau khi xử lý
			fmt.Printf("DEBUG: Added %d tags to response\n", len(postResp.Tags))
		} else {
			// Khởi tạo mảng trống cho tags nếu không có tags
			postResp.Tags = []response.PostTagResponse{}
			fmt.Printf("DEBUG: Initialized empty tags array\n")
		}
	} else {
		// Luôn đảm bảo trả về mảng trống nếu không lấy tags
		postResp.Tags = []response.PostTagResponse{}
		fmt.Printf("DEBUG: withTags=false, initialized empty tags array\n")
	}

	// Load author if requested
	if withAuthor && post.Author != nil {
		postResp.Author = &response.UserInfo{
			ID:        post.Author.UserID,
			Username:  post.Author.Username,
			Email:     post.Author.Email,
			FirstName: post.Author.FirstName,
			LastName:  post.Author.LastName,
			AvatarURL: post.Author.AvatarURL,
		}
	}

	// Load editor if present
	if post.Editor != nil {
		postResp.Editor = &response.UserInfo{
			ID:        post.Editor.UserID,
			Username:  post.Editor.Username,
			Email:     post.Editor.Email,
			FirstName: post.Editor.FirstName,
			LastName:  post.Editor.LastName,
			AvatarURL: post.Editor.AvatarURL,
		}
	}

	return postResp, nil
}
