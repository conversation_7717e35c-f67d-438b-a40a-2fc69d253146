package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/service"
	"wnapi/pkg/auth"
	"wnapi/pkg/response"
)

// AuthorHandler handles HTTP requests for blog authors
type AuthorHandler struct {
	authorService service.AuthorService
	jwtService    *auth.JWTService
}

// NewAuthorHandler creates a new author handler instance
func NewAuthorHandler(authorService service.AuthorService, jwtService *auth.JWTService) *AuthorHandler {
	return &AuthorHandler{
		authorService: authorService,
		jwtService:    jwtService,
	}
}

// <PERSON><PERSON> handles the creation of a new author
func (h *AuthorHandler) Create(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	// Parse request body
	var req request.CreateAuthorRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Call service
	author, err := h.authorService.CreateAuthor(c.Request.Context(), tenantID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusCreated, "Author created successfully", author)
}

// Get handles retrieval of an author by ID
func (h *AuthorHandler) Get(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	// Get author ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Author ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing author ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	authorID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid author ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid author ID", "INVALID_ID", details)
		return
	}

	// Call service
	author, err := h.authorService.GetAuthor(c.Request.Context(), tenantID, uint(authorID))
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Author retrieved successfully", author)
}

// GetByUserId handles retrieval of an author by user ID
func (h *AuthorHandler) GetByUserId(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	// Get user ID from URL
	userIdStr := c.Param("user_id")
	if userIdStr == "" {
		details := []interface{}{map[string]string{"message": "User ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing user ID", "MISSING_USER_ID", details)
		return
	}

	// Convert to uint
	userId, err := strconv.ParseUint(userIdStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid user ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid user ID", "INVALID_USER_ID", details)
		return
	}

	// Call service
	author, err := h.authorService.GetAuthorByUserId(c.Request.Context(), tenantID, uint(userId))
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Author retrieved successfully", author)
}

// Update handles updating an existing author
func (h *AuthorHandler) Update(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	// Get author ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Author ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing author ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	authorID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid author ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid author ID", "INVALID_ID", details)
		return
	}

	// Parse request body
	var req request.UpdateAuthorRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Call service
	author, err := h.authorService.UpdateAuthor(c.Request.Context(), tenantID, uint(authorID), req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Author updated successfully", author)
}

// Delete handles deletion of an author
func (h *AuthorHandler) Delete(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	// Get author ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Author ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing author ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	authorID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid author ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid author ID", "INVALID_ID", details)
		return
	}

	// Call service
	err = h.authorService.DeleteAuthor(c.Request.Context(), tenantID, uint(authorID))
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Author deleted successfully", nil)
}

// List handles listing of authors with pagination
func (h *AuthorHandler) List(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	// Parse query parameters
	var req request.ListAuthorRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid query parameters", "INVALID_QUERY", details)
		return
	}

	// Call service
	result, err := h.authorService.ListAuthors(c.Request.Context(), tenantID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response with pagination metadata
	meta := map[string]interface{}{
		"next_cursor": result.Meta.NextCursor,
		"has_more":    result.Meta.HasMore,
	}
	response.SuccessWithMeta(c, http.StatusOK, "Authors retrieved successfully", result.Authors, meta)
}
