package response

import (
	"time"

	"wnapi/modules/crm/models"
)

type CustomerResponse struct {
	CustomerID  uint       `json:"customer_id"`
	TenantID    uint       `json:"tenant_id"`
	UserID      *uint      `json:"user_id,omitempty"`
	GroupID     *uint      `json:"group_id,omitempty"`
	FullName    string     `json:"full_name"`
	Email       string     `json:"email"`
	Phone       string     `json:"phone,omitempty"`
	Status      string     `json:"status"`
	IsVerified  bool       `json:"is_verified"`
	AvatarURL   string     `json:"avatar_url,omitempty"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	LastLoginAt *time.Time `json:"last_login_at,omitempty"`
	CreatedBy   *uint      `json:"created_by,omitempty"`
	UpdatedBy   *uint      `json:"updated_by,omitempty"`
}

func NewCustomerResponse(customer *models.Customer) *CustomerResponse {
	return &CustomerResponse{
		CustomerID:  customer.CustomerID,
		TenantID:    customer.TenantID,
		UserID:      customer.UserID,
		GroupID:     customer.GroupID,
		FullName:    customer.FullName,
		Email:       customer.Email,
		Phone:       customer.Phone,
		Status:      string(customer.Status),
		IsVerified:  customer.IsVerified,
		AvatarURL:   customer.AvatarURL,
		CreatedAt:   customer.CreatedAt,
		UpdatedAt:   customer.UpdatedAt,
		LastLoginAt: customer.LastLoginAt,
		CreatedBy:   customer.CreatedBy,
		UpdatedBy:   customer.UpdatedBy,
	}
}
