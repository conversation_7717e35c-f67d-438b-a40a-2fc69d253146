package tracing

import (
	"context"

	pkgtracing "wnapi/pkg/tracing"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
)

const (
	// CustomerOperations represent the types of customer operations
	OpCustomerProfileManagement = "profile_management"
	OpCustomerLookup            = "customer_lookup"
	OpCustomerPreferences       = "customer_preferences"
	OpProfileUpdate             = "profile_update"
	OpAccountStatusChange       = "account_status_change"
	OpCustomerDataAccess        = "customer_data_access"
)

// StartCustomerOperationSpan starts a new span for a customer operation with appropriate attributes
func StartCustomerOperationSpan(ctx context.Context, operationName, customerID, operation string) (context.Context, trace.Span) {
	ctx, span := pkgtracing.StartSpan(ctx, operationName)

	// Add standard customer span attributes
	span.SetAttributes(
		attribute.String("customer.id", MaskCustomerID(customerID)),
		attribute.String("customer.operation", operation),
	)

	return ctx, span
}

// StartProfileManagementSpan starts a span for profile management operations
func StartProfileManagementSpan(ctx context.Context, customerID, section string) (context.Context, trace.Span) {
	ctx, span := StartCustomerOperationSpan(ctx, "CustomerProfile.Manage", customerID, OpCustomerProfileManagement)

	// Add profile-specific attributes
	if section != "" {
		span.SetAttributes(attribute.String("customer.profile_section", section))
	}

	return ctx, span
}

// StartCustomerLookupSpan starts a span for customer lookup operations
func StartCustomerLookupSpan(ctx context.Context, lookupParam string) (context.Context, trace.Span) {
	ctx, span := pkgtracing.StartSpan(ctx, "Customer.Lookup")

	// Use masked version of lookup param (could be ID, email, etc.)
	span.SetAttributes(
		attribute.String("customer.operation", OpCustomerLookup),
		attribute.String("customer.lookup_param_type", GetParamType(lookupParam)),
	)

	return ctx, span
}

// StartCustomerPreferencesSpan starts a span for customer preferences operations
func StartCustomerPreferencesSpan(ctx context.Context, customerID, preferenceName string) (context.Context, trace.Span) {
	ctx, span := StartCustomerOperationSpan(ctx, "CustomerPreferences.Manage", customerID, OpCustomerPreferences)

	// Add preference-specific attributes
	if preferenceName != "" {
		span.SetAttributes(attribute.String("customer.preference_name", preferenceName))
	}

	return ctx, span
}

// StartCustomerAccountStatusSpan starts a span for account status change operations
func StartCustomerAccountStatusSpan(ctx context.Context, customerID, newStatus string, source string) (context.Context, trace.Span) {
	ctx, span := StartCustomerOperationSpan(ctx, "CustomerAccount.StatusChange", customerID, OpAccountStatusChange)

	// Add status-specific attributes
	span.SetAttributes(
		attribute.String("customer.account_status", newStatus),
		attribute.String("customer.operation_source", source),
	)

	return ctx, span
}

// AddCustomerErrorToSpan adds error information to a span with customer context
func AddCustomerErrorToSpan(span trace.Span, err error, errorType string) {
	if span == nil || err == nil {
		return
	}

	pkgtracing.RecordError(span, err)
	span.SetAttributes(attribute.String("error.type", errorType))
}

// MaskCustomerID masks a customer ID for privacy/security in traces
func MaskCustomerID(customerID string) string {
	if len(customerID) <= 4 {
		return "****"
	}

	// Keep first two and last two characters, mask the rest
	return customerID[:2] + "****" + customerID[len(customerID)-2:]
}

// GetParamType determines the type of lookup parameter
func GetParamType(param string) string {
	// Simplified logic - in real implementation should be more robust
	if len(param) == 36 {
		return "uuid" // Assume UUID
	} else if IsEmailFormat(param) {
		return "email" // Assume email
	} else if IsNumeric(param) {
		return "numeric_id" // Assume numeric ID
	}
	return "other"
}

// IsEmailFormat does basic check if string might be email format
func IsEmailFormat(s string) bool {
	// Very simplified check - would be more robust in actual implementation
	for i := 0; i < len(s); i++ {
		if s[i] == '@' {
			return true
		}
	}
	return false
}

// IsNumeric checks if string is numeric
func IsNumeric(s string) bool {
	for _, c := range s {
		if c < '0' || c > '9' {
			return false
		}
	}
	return len(s) > 0
}
