package tracing

import (
	"log"

	"wnapi/modules/crm/configs"
	pkgtracing "wnapi/pkg/tracing"
)

// TracingCleanup represents a function to clean up tracing resources
type TracingCleanup func()

// InitTracing initializes OpenTelemetry tracing based on configuration
func InitTracing(cfg *configs.Config) (TracingCleanup, error) {
	if !cfg.Tracing.Enabled {
		log.Println("Tracing is disabled. Skipping tracer initialization.")
		return func() {}, nil
	}

	log.Printf("Initializing %s tracer for service: %s", cfg.Tracing.ExporterType, cfg.Tracing.ServiceName)

	switch cfg.Tracing.ExporterType {
	case "signoz":
		// The SignOz tracer only takes serviceName and endpoint parameters
		cleanupFn, err := pkgtracing.InitSignozTracer(
			cfg.Tracing.ServiceName,
			cfg.Tracing.Signoz.Endpoint,
		)
		if err != nil {
			return nil, err
		}
		return func() {
			cleanupFn()
		}, nil
	case "jaeger":
		// For Jaeger, we get a tracer, closer, and error
		_, closer, err := pkgtracing.InitJaeger(
			cfg.Tracing.ServiceName,
			cfg.Tracing.Jaeger.Host,
			cfg.Tracing.Jaeger.Port,
		)
		if err != nil {
			return nil, err
		}
		return func() {
			if closer != nil {
				if err := closer.Close(); err != nil {
					log.Printf("Error closing Jaeger tracer: %v", err)
				}
			}
		}, nil
	default:
		log.Printf("Unknown exporter type: %s. Using default SignOz tracer.", cfg.Tracing.ExporterType)
		cleanupFn, err := pkgtracing.InitSignozTracer(
			cfg.Tracing.ServiceName,
			cfg.Tracing.Signoz.Endpoint,
		)
		if err != nil {
			return nil, err
		}
		return func() {
			cleanupFn()
		}, nil
	}
}
