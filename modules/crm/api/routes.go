package api

import (
	"log"

	"wnapi/modules/crm/api/handlers"
	"wnapi/modules/crm/service"
	"wnapi/pkg/auth"
	pkgMiddleware "wnapi/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterRoutes(router *gin.Engine, jwtService *auth.JWTService, permService pkgMiddleware.PermissionService, customerService *service.CustomerService) {
	log.Println("Đăng ký routes cho module CRM")

	// Initialize handlers
	customerHandler := handlers.NewCustomerHandler(customerService)

	v1 := router.Group("/api/v1")
	{
		customers := v1.Group("/customers")
		{
			// Protected routes
			protected := customers.Group("")
			protected.Use(jwtService.JWTAuthMiddleware())
			{
				protected.GET("", pkgMiddleware.RequirePermission(permService, "customers.read"), customerHandler.ListCustomers)
				protected.POST("", pkgMiddleware.RequirePermission(permService, "customers.create"), customerHandler.CreateCustomer)
				protected.GET("/:id", pkgMiddleware.RequirePermission(permService, "customers.read"), customerHandler.GetCustomer)
				protected.PUT("/:id", pkgMiddleware.RequirePermission(permService, "customers.update"), customerHandler.UpdateCustomer)
				protected.DELETE("/:id", pkgMiddleware.RequirePermission(permService, "customers.delete"), customerHandler.DeleteCustomer)
			}
		}
	}
}
