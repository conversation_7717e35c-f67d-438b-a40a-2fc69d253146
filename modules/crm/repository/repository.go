package repository

import (
	"context"

	"wnapi/modules/crm/models"
)

type CustomerRepository interface {
	Create(ctx context.Context, customer *models.Customer) error
	Update(ctx context.Context, customer *models.Customer) error
	Delete(ctx context.Context, customerID uint) error
	FindByID(ctx context.Context, customerID uint) (*models.Customer, error)
	FindByEmail(ctx context.Context, email string, tenantID uint) (*models.Customer, error)
	List(ctx context.Context, cursor string, limit int, tenantID uint, search string) ([]*models.Customer, string, error)
}
