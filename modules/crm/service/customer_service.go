package service

import (
	"context"
	"errors"
	"fmt"

	"wnapi/modules/crm/dto/request"
	"wnapi/modules/crm/dto/response"
	"wnapi/modules/crm/models"
	"wnapi/modules/crm/repository"

	"gorm.io/gorm"
)

var (
	ErrCustomerNotFound    = errors.New("customer not found")
	ErrCustomerEmailExists = errors.New("email already exists for this tenant")
	ErrTenantIDRequired    = errors.New("tenant ID is required")
)

type CustomerService struct {
	db           *gorm.DB
	customerRepo repository.CustomerRepository
}

func NewCustomerService(customerRepo repository.CustomerRepository) *CustomerService {
	return &CustomerService{
		customerRepo: customerRepo,
	}
}

// NewCustomerServiceWithDB creates a new CustomerService with direct GORM access
func NewCustomerServiceWithDB(db *gorm.DB, customerRepo repository.CustomerRepository) *CustomerService {
	return &CustomerService{
		db:           db,
		customerRepo: customerRepo,
	}
}

func (s *CustomerService) Create(ctx context.Context, req *request.CreateCustomerRequest) (*response.CustomerResponse, error) {
	if req.TenantID <= 0 {
		return nil, ErrTenantIDRequired
	}

	// Using GORM for checking existing email
	var existingCount int64
	if err := s.db.WithContext(ctx).Model(&models.Customer{}).
		Where("email = ? AND tenant_id = ?", req.Email, req.TenantID).
		Count(&existingCount).Error; err != nil {
		return nil, fmt.Errorf("failed to check existing email: %w", err)
	}

	if existingCount > 0 {
		return nil, ErrCustomerEmailExists
	}

	status := models.CustomerStatusPending
	if req.Status != "" {
		status = models.CustomerStatus(req.Status)
	}

	customer := &models.Customer{
		TenantID:  req.TenantID,
		UserID:    req.UserID,
		GroupID:   req.GroupID,
		FullName:  req.FullName,
		Email:     req.Email,
		Phone:     req.Phone,
		Status:    status,
		AvatarURL: req.AvatarURL,
		CreatedBy: req.CreatedBy,
	}

	// Using GORM to create customer
	if err := s.db.WithContext(ctx).Create(customer).Error; err != nil {
		return nil, fmt.Errorf("failed to create customer: %w", err)
	}

	return response.NewCustomerResponse(customer), nil
}

func (s *CustomerService) Update(ctx context.Context, customerID uint, req *request.UpdateCustomerRequest) (*response.CustomerResponse, error) {
	// Using GORM to find customer
	var customer models.Customer
	if err := s.db.WithContext(ctx).Where("customer_id = ?", customerID).First(&customer).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrCustomerNotFound
		}
		return nil, fmt.Errorf("failed to find customer: %w", err)
	}

	if req.Email != nil {
		// Check if email exists for another customer
		var existingCount int64
		if err := s.db.WithContext(ctx).Model(&models.Customer{}).
			Where("email = ? AND tenant_id = ? AND customer_id != ?", *req.Email, customer.TenantID, customerID).
			Count(&existingCount).Error; err != nil {
			return nil, fmt.Errorf("failed to check existing email: %w", err)
		}

		if existingCount > 0 {
			return nil, ErrCustomerEmailExists
		}

		customer.Email = *req.Email
	}

	if req.FullName != nil {
		customer.FullName = *req.FullName
	}

	if req.Phone != nil {
		customer.Phone = *req.Phone
	}

	if req.GroupID != nil {
		customer.GroupID = req.GroupID
	}

	if req.Status != nil {
		customer.Status = models.CustomerStatus(*req.Status)
	}

	if req.IsVerified != nil {
		customer.IsVerified = *req.IsVerified
	}

	if req.AvatarURL != nil {
		customer.AvatarURL = *req.AvatarURL
	}

	if req.UpdatedBy != nil {
		customer.UpdatedBy = req.UpdatedBy
	}

	// Using GORM to update customer
	if err := s.db.WithContext(ctx).Save(&customer).Error; err != nil {
		return nil, fmt.Errorf("failed to update customer: %w", err)
	}

	return response.NewCustomerResponse(&customer), nil
}

func (s *CustomerService) Delete(ctx context.Context, customerID uint) error {
	// Using GORM to check if customer exists
	var customer models.Customer
	if err := s.db.WithContext(ctx).Where("customer_id = ?", customerID).First(&customer).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrCustomerNotFound
		}
		return fmt.Errorf("failed to find customer: %w", err)
	}

	// Using GORM to delete customer
	if err := s.db.WithContext(ctx).Delete(&models.Customer{}, customerID).Error; err != nil {
		return fmt.Errorf("failed to delete customer: %w", err)
	}

	return nil
}

func (s *CustomerService) GetByID(ctx context.Context, customerID uint) (*response.CustomerResponse, error) {
	// Using GORM to find customer by ID
	var customer models.Customer
	if err := s.db.WithContext(ctx).Where("customer_id = ?", customerID).First(&customer).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrCustomerNotFound
		}
		return nil, fmt.Errorf("failed to find customer: %w", err)
	}

	return response.NewCustomerResponse(&customer), nil
}

func (s *CustomerService) List(ctx context.Context, cursor string, limit int, tenantID uint, search string) ([]*response.CustomerResponse, string, error) {
	if tenantID <= 0 {
		return nil, "", ErrTenantIDRequired
	}

	// Keep using repository for List method as required
	customers, nextCursor, err := s.customerRepo.List(ctx, cursor, limit, tenantID, search)
	if err != nil {
		return nil, "", fmt.Errorf("failed to list customers: %w", err)
	}

	customerResponses := make([]*response.CustomerResponse, len(customers))
	for i, customer := range customers {
		customerResponses[i] = response.NewCustomerResponse(customer)
	}

	return customerResponses, nextCursor, nil
}
