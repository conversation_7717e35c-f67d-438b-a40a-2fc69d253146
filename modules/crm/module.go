package crm

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"time"

	"wnapi/modules/crm/api"
	"wnapi/modules/crm/configs"
	mysqlRepo "wnapi/modules/crm/repository/mysql"
	"wnapi/modules/crm/service"
	"wnapi/pkg/auth"
	pkgMiddleware "wnapi/pkg/middleware"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Module là struct đại diện cho module CRM
type Module struct {
	db              *gorm.DB
	sqlDB           *sql.DB
	jwtService      *auth.JWTService
	permService     pkgMiddleware.PermissionService
	customerService *service.CustomerService
	router          *gin.Engine
	server          *http.Server
	config          *configs.Config
}

// New khởi tạo module CRM mới
func New(db *gorm.DB, sqlDB *sql.DB, jwtService *auth.JWTService, permService pkgMiddleware.PermissionService) *Module {
	customerRepo := mysqlRepo.NewCustomerRepository(db)
	customerService := service.NewCustomerServiceWithDB(db, customerRepo)
	return &Module{
		db:              db,
		sqlDB:           sqlDB,
		jwtService:      jwtService,
		permService:     permService,
		customerService: customerService,
		router:          gin.Default(),
	}
}

// NewModuleWithConfig khởi tạo module CRM với cấu hình
func NewModuleWithConfig() *Module {
	// Đọc cấu hình từ file config
	config, err := configs.LoadConfig()
	if err != nil {
		log.Fatalf("Không thể tải cấu hình: %v", err)
	}

	router := gin.Default()

	// Tạo JWT service từ cấu hình
	jwtConfig := auth.JWTConfig{
		AccessSigningKey:       config.JWT.AccessSigningKey,
		RefreshSigningKey:      config.JWT.RefreshSigningKey,
		AccessTokenExpiration:  config.JWT.AccessTokenExpiration,
		RefreshTokenExpiration: config.JWT.RefreshTokenExpiration,
		Issuer:                 config.JWT.Issuer,
	}
	jwtService := auth.NewJWTService(jwtConfig)

	// Trong triển khai thực tế, cần tạo customerService với repository thực
	// Tạm thời để nil và sẽ được khởi tạo khi module được start với DB
	customerService := &service.CustomerService{}

	return &Module{
		router:          router,
		jwtService:      jwtService,
		customerService: customerService,
		config:          config,
	}
}

// RegisterRoutes đăng ký các route API cho module CRM
func (m *Module) RegisterRoutes(router *gin.Engine) {
	// Sử dụng package api để đăng ký routes
	api.RegisterRoutes(router, m.jwtService, m.permService, m.customerService)

	log.Println("CRM routes đã được đăng ký thành công")
}

// Start khởi động module CRM
func (m *Module) Start() error {
	log.Println("Khởi động module CRM")

	// Kiểm tra kết nối database
	if m.db != nil {
		sqlDB, err := m.db.DB()
		if err != nil {
			return fmt.Errorf("không thể lấy kết nối SQL: %w", err)
		}

		// Kiểm tra kết nối
		if err := sqlDB.Ping(); err != nil {
			return fmt.Errorf("không thể ping đến database: %w", err)
		}

		log.Println("Kết nối đến database thành công")

		// Khởi tạo customerService nếu chưa được khởi tạo
		if m.customerService == nil || m.customerService == (&service.CustomerService{}) {
			customerRepo := mysqlRepo.NewCustomerRepository(m.db)
			m.customerService = service.NewCustomerServiceWithDB(m.db, customerRepo)
		}
	}

	// Đảm bảo router đã được khởi tạo
	if m.router == nil {
		m.router = gin.Default()
	}

	// Đăng ký routes
	m.RegisterRoutes(m.router)

	// Lấy cổng từ cấu hình
	port := 8080
	if m.config != nil && m.config.CRM.Port > 0 {
		port = m.config.CRM.Port
	}

	// Tạo HTTP server
	m.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", port),
		Handler: m.router,
	}

	// Khởi động server trong goroutine
	go func() {
		log.Printf("Khởi động CRM service trên cổng %d", port)
		if err := m.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Không thể khởi động server: %v", err)
		}
	}()

	return nil
}

// Stop dừng module CRM
func (m *Module) Stop() error {
	log.Println("Dừng module CRM")

	// Đóng server HTTP nếu đã được khởi tạo
	if m.server != nil {
		// Tạo context với timeout cho việc shutdown
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		// Shutdown server
		if err := m.server.Shutdown(ctx); err != nil {
			log.Printf("Lỗi khi dừng HTTP server: %v", err)
			return fmt.Errorf("không thể dừng HTTP server: %w", err)
		}

		log.Println("HTTP server đã được dừng thành công")
	}

	// Đóng kết nối database nếu cần
	if m.sqlDB != nil {
		if err := m.sqlDB.Close(); err != nil {
			return fmt.Errorf("không thể đóng kết nối DB: %w", err)
		}
		log.Println("Đã đóng kết nối đến database")
	}

	return nil
}
