package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"sort"
	"strings"
	"syscall"
	"wnapi/internal/bootstrap"
	"wnapi/internal/core"
	"wnapi/internal/pkg/cache/memorycache"
	"wnapi/internal/pkg/config/viperconfig"
	"wnapi/internal/pkg/database/gormdb"
	"wnapi/internal/pkg/logger/zaplogger"

	// Import modules để đăng ký với registry
	_ "wnapi/modules/auth"
	_ "wnapi/modules/hello"

	//_ "wnapi/modules/media"
	_ "wnapi/modules/notification"
	_ "wnapi/modules/product"
	_ "wnapi/modules/rbac"

	// Import plugins để đăng ký với registry
	_ "wnapi/plugins/logger"
)

var (
	configFlag  = flag.String("config", ".env", "Path to .env file")
	projectFlag = flag.String("project", "", "Project to run")
	debugFlag   = flag.Bool("debug", false, "Enable debug mode")
)

func main() {
	// 1. Khởi tạo config
	cfg, err := viperconfig.NewConfigLoader().Load(".env")
	if err != nil {
		log.Fatalf("Lỗi khởi tạo config: %v", err)
	}

	// Thiết lập giá trị mặc định cho MODULES_ENABLED nếu chưa có
	if os.Getenv("MODULES_ENABLED") == "" {
		// Lưu ý: đặt biến môi trường MODULES_ENABLED chỉ có tác dụng với os.Getenv
		// Nhưng viper không tự động đọc giá trị này nếu đặt sau khi viper đã load
		os.Setenv("MODULES_ENABLED", "hello,auth,rbac,notification,product")

		// Log debug về viper config
		if vCfg, ok := cfg.(*viperconfig.ViperConfig); ok {
			log.Println("Thêm modules mặc định vào cấu hình viper")
			vCfg.SetModulesEnabled([]string{"hello", "auth", "rbac", "notification", "product"})
		}
	}

	// 2. Khởi tạo logger
	appLogger, err := zaplogger.NewLogger(
		cfg.GetString("logger.level"),
		cfg.GetString("logger.format"),
	)
	if err != nil {
		log.Fatalf("Lỗi khởi tạo logger: %v", err)
	}

	// 3. Khởi tạo database
	dbManager, err := gormdb.NewGORMDBManager(
		cfg.GetString("database.dsn_write"),
		cfg.GetString("database.dsn_read"),
		appLogger,
	)
	if err != nil {
		appLogger.Error("Lỗi khởi tạo DB manager", "error", err)
		os.Exit(1)
	}

	// 4. Khởi tạo cache
	appCache := memorycache.NewMemoryCache()
	appLogger.Info("Cache initialized", "type", "in-memory")

	// 5. Bootstrap RBAC và Permission
	mwFactory, err := bootstrap.BootstrapRBAC(
		dbManager.DB(),
		appCache,
		appLogger,
		cfg,
	)
	if err != nil {
		appLogger.Error("Lỗi khởi tạo RBAC components", "error", err)
		os.Exit(1)
	}
	appLogger.Info("RBAC Permission system initialized")

	// 6. Khởi tạo App
	appBootstrap := core.NewAppBootstrap(
		cfg,
		appLogger,
		dbManager,
		appCache,
		mwFactory,
	)

	// 7. Khởi tạo các module và plugin
	log.Println("Bắt đầu khởi tạo modules và plugins")
	if err := appBootstrap.Initialize(); err != nil {
		appLogger.Error("Lỗi khởi tạo modules và plugins", "error", err)
		log.Printf("Lỗi khởi tạo modules và plugins: %v", err)
		os.Exit(1)
	}

	// In thông tin về các module đã nạp
	log.Println("In thông tin về modules đã nạp")
	printModuleInfo(appBootstrap)

	// 8. Khởi tạo Server
	log.Println("Khởi tạo HTTP server")
	server := core.NewServer(appBootstrap)
	appLogger.Info("HTTP Server initialized")

	// 9. Khởi động server trong goroutine
	go func() {
		if err := server.Start(); err != nil {
			appLogger.Error("Lỗi chạy server", "error", err)
			os.Exit(1)
		}
	}()

	// 10. Graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	appLogger.Info("Shutting down server...")
	ctx, cancel := context.WithTimeout(context.Background(), cfg.GetDuration("server.shutdown_timeout"))
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		appLogger.Error("Lỗi shutdown server", "error", err)
	}

	// Shutdown AppBootstrap để giải phóng tài nguyên
	if err := appBootstrap.Shutdown(ctx); err != nil {
		appLogger.Error("Lỗi shutdown application", "error", err)
	}

	appLogger.Info("Server shutdown complete")
}

// printModuleInfo hiển thị thông tin về các module đã nạp
func printModuleInfo(app *core.AppBootstrap) {
	// Lấy danh sách modules đã được đăng ký
	modules := app.GetModules()

	fmt.Printf("\n=== Module Information ===\n")
	fmt.Printf("Enabled modules: %s\n", os.Getenv("MODULES_ENABLED"))

	// Hiển thị danh sách module factories đã đăng ký
	factories := core.GlobalModuleRegistry.Factories()
	fmt.Printf("Registered module factories: %d\n", len(factories))

	var registeredModules []string
	for name := range factories {
		registeredModules = append(registeredModules, name)
	}
	sort.Strings(registeredModules)
	fmt.Printf("Available modules: %s\n", strings.Join(registeredModules, ", "))

	if len(modules) == 0 {
		fmt.Println("No modules loaded. Kiểm tra biến môi trường MODULES_ENABLED.")
		return
	}

	fmt.Printf("\n=== Loaded Modules (%d) ===\n", len(modules))
	for _, module := range modules {
		migrationPath := module.GetMigrationPath()
		migrationInfo := ""
		if migrationPath != "" {
			migrationInfo = fmt.Sprintf(" (Migrations: %s, Order: %d)", migrationPath, module.GetMigrationOrder())
		}
		fmt.Printf("- %s%s\n", module.Name(), migrationInfo)
	}

	// Hiển thị các plugin đã nạp
	plugins := app.GetPlugins()
	if len(plugins) > 0 {
		fmt.Printf("\n=== Loaded Plugins (%d) ===\n", len(plugins))
		for _, plugin := range plugins {
			fmt.Printf("- %s\n", plugin.Name())
		}
	} else {
		fmt.Printf("\n=== Plugins ===\n")
		fmt.Println("No plugins loaded. Kiểm tra biến môi trường PLUGINS_ENABLED.")
	}
}
