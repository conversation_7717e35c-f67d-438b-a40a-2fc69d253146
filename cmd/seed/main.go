package main

import (
	"flag"
	"fmt"
	"os"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/seed"
)

var (
	envFlag     = flag.String("env", "dev", "Environment (dev, staging, prod)")
	moduleFlag  = flag.String("module", "", "Module name to seed")
	seederFlag  = flag.String("seeder", "", "Specific seeder to run")
	listFlag    = flag.Bool("list", false, "List available seeds")
	dryRunFlag  = flag.Bool("dry-run", false, "Dry run mode")
	verboseFlag = flag.Bool("verbose", false, "Verbose logging")
)

func main() {
	flag.Parse()

	// Khởi tạo logger
	logLevel := logger.LevelInfo
	if *verboseFlag {
		logLevel = logger.LevelDebug
	}
	log := logger.NewConsoleLogger("seed", logLevel)

	// Khởi tạo config
	config := seed.LoadConfig()
	config.Environment = *envFlag
	config.DryRun = *dryRunFlag
	config.Verbose = *verboseFlag

	if err := config.Validate(); err != nil {
		log.Error("Lỗi validate config", "error", err.Error())
		os.Exit(1)
	}

	log.Info("Seed command started",
		"environment", config.Environment,
		"dry_run", config.DryRun)

	// Xử lý commands
	if *listFlag {
		listSeeds(log)
		return
	}

	if *moduleFlag != "" {
		if *seederFlag != "" {
			seedSpecific(log, config, *moduleFlag, *seederFlag)
		} else {
			seedModule(log, config, *moduleFlag)
		}
		return
	}

	// Default: seed all modules
	seedAll(log, config)
}

func listSeeds(log logger.Logger) {
	log.Info("Listing available seeds...")

	seeds := seed.ListAvailableSeeds()
	if len(seeds) == 0 {
		log.Info("No seeds registered")
		return
	}

	for moduleName, seeders := range seeds {
		log.Info("Module", "name", moduleName)
		for _, seederName := range seeders {
			log.Info("  - Seeder", "name", seederName)
		}
	}
}

func seedAll(log logger.Logger, config *seed.Config) {
	log.Info("Seeding all modules...")

	// TODO: Implement with proper database connection
	// runner := seed.NewRunner(db, log, config)
	// ctx := context.Background()
	// if err := runner.SeedAllModules(ctx); err != nil {
	//     log.Error("Lỗi seed all modules", "error", err.Error())
	//     os.Exit(1)
	// }

	log.Info("Seed all modules - stub implementation")
	log.Info("Would seed all registered modules", "environment", config.Environment)
}

func seedModule(log logger.Logger, config *seed.Config, moduleName string) {
	log.Info("Seeding module", "module", moduleName)

	// TODO: Implement with proper database connection
	// runner := seed.NewRunner(db, log, config)
	// ctx := context.Background()
	// if err := runner.SeedModule(ctx, moduleName); err != nil {
	//     log.Error("Lỗi seed module", "module", moduleName, "error", err.Error())
	//     os.Exit(1)
	// }

	log.Info("Seed module - stub implementation", "module", moduleName)
}

func seedSpecific(log logger.Logger, config *seed.Config, moduleName, seederName string) {
	log.Info("Seeding specific", "module", moduleName, "seeder", seederName)

	// TODO: Implement with proper database connection
	// runner := seed.NewRunner(db, log, config)
	// ctx := context.Background()
	// if err := runner.SeedSpecific(ctx, moduleName, seederName); err != nil {
	//     log.Error("Lỗi seed specific", "module", moduleName, "seeder", seederName, "error", err.Error())
	//     os.Exit(1)
	// }

	log.Info("Seed specific - stub implementation",
		"module", moduleName,
		"seeder", seederName)
}

func printUsage() {
	fmt.Println("Usage:")
	fmt.Println("  seed [options]")
	fmt.Println("")
	fmt.Println("Options:")
	fmt.Println("  -env string       Environment (dev, staging, prod) (default \"dev\")")
	fmt.Println("  -module string    Module name to seed")
	fmt.Println("  -seeder string    Specific seeder to run")
	fmt.Println("  -list             List available seeds")
	fmt.Println("  -dry-run          Dry run mode")
	fmt.Println("  -verbose          Verbose logging")
	fmt.Println("")
	fmt.Println("Examples:")
	fmt.Println("  seed -list                           # List all available seeds")
	fmt.Println("  seed                                 # Seed all modules")
	fmt.Println("  seed -env=dev                        # Seed all modules for dev environment")
	fmt.Println("  seed -module=auth                    # Seed auth module")
	fmt.Println("  seed -module=auth -seeder=users      # Seed specific seeder")
	fmt.Println("  seed -dry-run                        # Dry run mode")
}
